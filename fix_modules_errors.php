<?php
/**
 * Correction des erreurs de modules et widgets
 * Résout les problèmes de chargement et les erreurs SQL
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Correction des Erreurs de Modules</title></head><body>';
print '<h1>Correction des Erreurs de Modules et Widgets</h1>';

if (isset($_GET['action']) && $_GET['action'] == 'fix_modules') {
    print '<h2>Correction en cours...</h2>';
    
    $fixes = array();
    $errors = array();
    
    try {
        // 1. Désactiver les modules problématiques
        print '<h3>1. Désactivation des modules problématiques</h3>';
        
        $problematic_modules = array(
            'MAIN_MODULE_PURCHASEREQUEST',
            'MAIN_MODULE_FRAISDEPORT', 
            'MAIN_MODULE_DIGIRISKDOLIBARR',
            'MAIN_MODULE_SMI',
            'MAIN_MODULE_MULTISOCIETE',
            'MAIN_MODULE_PRODUCTAUTOPARTS',
            'MAIN_MODULE_SCRUMBOARD',
            'MAIN_MODULE_DECLINAISON'
        );
        
        foreach ($problematic_modules as $module) {
            $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '0' WHERE name = '{$module}'";
            if ($db->query($sql)) {
                $fixes[] = "Module {$module} désactivé";
            }
        }
        
        // 2. Nettoyer les widgets/boxes corrompus
        print '<h3>2. Nettoyage des widgets corrompus</h3>';
        
        // Supprimer les boxes qui ne peuvent pas se charger
        $corrupted_boxes = array(
            'MyBox',
            'box_riskassessmentdocument',
            'smiwidgetactions',
            'smiwidgetindicators', 
            'smiwidgetdashboard',
            'box_members',
            'multisocietewidget1',
            'productautopartswidget1',
            'scrumboard_box'
        );
        
        foreach ($corrupted_boxes as $box) {
            $sql = "DELETE FROM " . MAIN_DB_PREFIX . "boxes WHERE box_id = '{$box}'";
            if ($db->query($sql)) {
                $affected = $db->affected_rows();
                if ($affected > 0) {
                    $fixes[] = "Widget {$box} supprimé ({$affected} entrées)";
                }
            }
        }
        
        // Supprimer les boxes par chemin de fichier
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "boxes WHERE file LIKE '%purchaserequest%' 
                OR file LIKE '%fraisdeport%' 
                OR file LIKE '%digiriskdolibar%'
                OR file LIKE '%smi/%'
                OR file LIKE '%multisociete%'
                OR file LIKE '%productautoparts%'
                OR file LIKE '%scrumboard%'
                OR file LIKE '%Declinaison%'";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            if ($affected > 0) {
                $fixes[] = "Widgets corrompus supprimés par chemin ({$affected} entrées)";
            }
        }
        
        // 3. Corriger les erreurs SQL dans les requêtes
        print '<h3>3. Correction des erreurs SQL</h3>';
        
        // Vérifier et corriger les vues/requêtes corrompues
        $sql = "SHOW TABLES LIKE '" . MAIN_DB_PREFIX . "%'";
        $result = $db->query($sql);
        if ($result) {
            while ($table = $db->fetch_row($result)) {
                $table_name = $table[0];
                
                // Vérifier l'intégrité de chaque table
                $check_sql = "CHECK TABLE {$table_name}";
                $check_result = $db->query($check_sql);
                if ($check_result) {
                    $check_obj = $db->fetch_object($check_result);
                    if ($check_obj && $check_obj->Msg_text != 'OK') {
                        // Réparer la table si nécessaire
                        $repair_sql = "REPAIR TABLE {$table_name}";
                        if ($db->query($repair_sql)) {
                            $fixes[] = "Table {$table_name} réparée";
                        }
                    }
                }
            }
        }
        
        // 4. Nettoyer les menus corrompus
        print '<h3>4. Nettoyage des menus corrompus</h3>';
        
        // Supprimer les menus qui pointent vers des modules inexistants
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "menu WHERE url LIKE '%purchaserequest%' 
                OR url LIKE '%fraisdeport%'
                OR url LIKE '%digiriskdolibar%'
                OR url LIKE '%smi/%'
                OR url LIKE '%multisociete%'
                OR url LIKE '%productautoparts%'
                OR url LIKE '%scrumboard%'
                OR url LIKE '%Declinaison%'";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            if ($affected > 0) {
                $fixes[] = "Menus corrompus supprimés ({$affected} entrées)";
            }
        }
        
        // 5. Nettoyer les hooks corrompus
        print '<h3>5. Nettoyage des hooks corrompus</h3>';
        
        // Supprimer les hooks qui pointent vers des modules inexistants
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%HOOK%' AND (
                value LIKE '%purchaserequest%' 
                OR value LIKE '%fraisdeport%'
                OR value LIKE '%digiriskdolibar%'
                OR value LIKE '%smi%'
                OR value LIKE '%multisociete%'
                OR value LIKE '%productautoparts%'
                OR value LIKE '%scrumboard%'
                OR value LIKE '%Declinaison%'
                )";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            if ($affected > 0) {
                $fixes[] = "Hooks corrompus supprimés ({$affected} entrées)";
            }
        }
        
        // 6. Corriger les droits utilisateur corrompus
        print '<h3>6. Correction des droits utilisateur</h3>';
        
        // Supprimer les droits liés aux modules inexistants
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "user_rights WHERE module LIKE '%purchaserequest%' 
                OR module LIKE '%fraisdeport%'
                OR module LIKE '%digiriskdolibar%'
                OR module LIKE '%smi%'
                OR module LIKE '%multisociete%'
                OR module LIKE '%productautoparts%'
                OR module LIKE '%scrumboard%'
                OR module LIKE '%Declinaison%'";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            if ($affected > 0) {
                $fixes[] = "Droits corrompus supprimés ({$affected} entrées)";
            }
        }
        
        // 7. Optimiser les tables principales
        print '<h3>7. Optimisation des tables</h3>';
        
        $tables_to_optimize = array(
            'const', 'boxes', 'menu', 'user_rights', 'societe', 'product', 'facture'
        );
        
        foreach ($tables_to_optimize as $table) {
            $sql = "OPTIMIZE TABLE " . MAIN_DB_PREFIX . $table;
            if ($db->query($sql)) {
                $fixes[] = "Table {$table} optimisée";
            }
        }
        
        // 8. Vider les caches
        print '<h3>8. Vidage des caches</h3>';
        
        // Supprimer les caches de modules
        $cache_dirs = array(
            DOL_DATA_ROOT . '/admin/temp',
            DOL_DATA_ROOT . '/admin/temp/module',
            DOL_DATA_ROOT . '/admin/temp/js',
            DOL_DATA_ROOT . '/admin/temp/css'
        );
        
        foreach ($cache_dirs as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '/*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
                $fixes[] = "Cache {$dir} vidé";
            }
        }
        
        // Supprimer les caches en base
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%CACHE%' OR name LIKE '%_LOADED'";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            if ($affected > 0) {
                $fixes[] = "Caches en base supprimés ({$affected} entrées)";
            }
        }
        
        // 9. Réinitialiser la configuration des modules
        print '<h3>9. Réinitialisation de la configuration</h3>';
        
        // Forcer la configuration de base
        $base_config = array(
            'MAIN_MODULE_API' => '0',
            'MAIN_MODULE_FCKEDITOR' => '0',
            'MAIN_USE_ADVANCED_PERMS' => '0',
            'MAIN_DELAY_ACTIONS_TODO' => '7',
            'MAIN_SIZE_LISTE_LIMIT' => '25'
        );
        
        foreach ($base_config as $param => $value) {
            $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                    VALUES ('{$param}', '{$value}', 'chaine', 'Base config', '0', '1') 
                    ON DUPLICATE KEY UPDATE value = '{$value}'";
            if ($db->query($sql)) {
                $fixes[] = "Configuration {$param} réinitialisée";
            }
        }
        
        // 10. Vérification finale
        print '<h3>10. Vérification finale</h3>';
        
        // Vérifier qu'il n'y a plus de références aux modules problématiques
        $check_sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "const WHERE value LIKE '%purchaserequest%' OR value LIKE '%fraisdeport%' OR value LIKE '%digiriskdolibar%'";
        $result = $db->query($check_sql);
        if ($result) {
            $obj = $db->fetch_object($result);
            $remaining = $obj->count;
            if ($remaining == 0) {
                $fixes[] = "✅ Aucune référence aux modules problématiques trouvée";
            } else {
                $fixes[] = "⚠️ {$remaining} références aux modules problématiques restantes";
            }
        }
        
    } catch (Exception $e) {
        $errors[] = "Erreur: " . $e->getMessage();
    }
    
    // Affichage des résultats
    if (!empty($fixes)) {
        print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>✅ Corrections appliquées :</h4>';
        print '<ul>';
        foreach ($fixes as $fix) {
            print '<li>' . $fix . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    if (!empty($errors)) {
        print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>❌ Erreurs :</h4>';
        print '<ul>';
        foreach ($errors as $error) {
            print '<li>' . $error . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4 style="color: white;">🔄 Actions finales :</h4>';
    print '<ol style="color: white;">';
    print '<li><strong>Redémarrez MAMP</strong></li>';
    print '<li><strong>Videz le cache du navigateur</strong></li>';
    print '<li><strong>Reconnectez-vous à Dolibarr</strong></li>';
    print '<li><strong>Vérifiez le tableau de bord</strong></li>';
    print '</ol>';
    print '</div>';
    
    print '<p><a href="/" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Retour au tableau de bord</a></p>';
    
} else {
    // Menu principal
    print '<h2>Correction des Erreurs de Modules</h2>';
    
    print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🚨 Erreurs détectées :</h3>';
    print '<ul>';
    print '<li>❌ Modules manquants : purchaserequest, fraisdeport, digiriskdolibarr, smi, etc.</li>';
    print '<li>❌ Widgets corrompus qui ne peuvent pas se charger</li>';
    print '<li>❌ Erreurs SQL dans les requêtes</li>';
    print '<li>❌ Menus pointant vers des fichiers inexistants</li>';
    print '<li>❌ Hooks corrompus</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🛠️ Ce script va :</h3>';
    print '<ul>';
    print '<li>✅ <strong>Désactiver</strong> les modules problématiques</li>';
    print '<li>✅ <strong>Supprimer</strong> les widgets corrompus</li>';
    print '<li>✅ <strong>Réparer</strong> les tables avec erreurs SQL</li>';
    print '<li>✅ <strong>Nettoyer</strong> les menus et hooks corrompus</li>';
    print '<li>✅ <strong>Corriger</strong> les droits utilisateur</li>';
    print '<li>✅ <strong>Optimiser</strong> les tables</li>';
    print '<li>✅ <strong>Vider</strong> tous les caches</li>';
    print '<li>✅ <strong>Réinitialiser</strong> la configuration de base</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 30px 0;">';
    print '<a href="?action=fix_modules" style="background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; font-weight: bold;">🔧 CORRIGER LES ERREURS DE MODULES</a>';
    print '</div>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h4>💡 Recommandation :</h4>';
    print '<p>Exécutez d\'abord ce script pour nettoyer les erreurs de modules, puis utilisez les autres scripts pour résoudre le problème "A text to show".</p>';
    print '</div>';
}

print '</body></html>';
?>
