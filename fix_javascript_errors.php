<?php
/**
 * Script pour corriger les erreurs JavaScript et CSS
 * À exécuter via l'interface web de Dolibarr
 */

// Vérification de sécurité
if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

// Vérification des droits admin
if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Correction des erreurs JavaScript</title></head><body>';
print '<h1>Correction des erreurs JavaScript et CSS</h1>';

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'diagnostic':
            print '<h2>Diagnostic des problèmes JavaScript/CSS</h2>';
            
            // 1. Vérifier les fichiers manquants
            print '<h3>1. Fichiers manquants</h3>';
            
            $missing_files = array();
            
            // mobile_styles.css
            $mobile_css = DOL_DOCUMENT_ROOT . '/custom/douchette/mobile/mobile_styles.css';
            if (!file_exists($mobile_css)) {
                $missing_files[] = 'mobile_styles.css (module douchette)';
            }
            
            // quality.js.php
            $quality_js = DOL_DOCUMENT_ROOT . '/custom/quality/js/quality.js.php';
            if (!file_exists($quality_js)) {
                $missing_files[] = 'quality.js.php (module quality)';
            } else {
                // Vérifier s'il y a des erreurs de syntaxe
                $content = file_get_contents($quality_js);
                if (strpos($content, '<?') === false) {
                    $missing_files[] = 'quality.js.php (pas de balise PHP)';
                }
            }
            
            // jQuery UI
            $jquery_ui = DOL_DOCUMENT_ROOT . '/includes/jquery/js/jquery-ui.min.js';
            if (!file_exists($jquery_ui)) {
                $missing_files[] = 'jquery-ui.min.js';
            }
            
            // Select2
            $select2 = DOL_DOCUMENT_ROOT . '/includes/jquery/plugins/select2/dist/js/select2.min.js';
            if (!file_exists($select2)) {
                $missing_files[] = 'select2.min.js';
            }
            
            if (empty($missing_files)) {
                print '<p style="color: green;">Tous les fichiers requis sont présents.</p>';
            } else {
                print '<p style="color: red;">Fichiers manquants :</p>';
                print '<ul>';
                foreach ($missing_files as $file) {
                    print '<li>' . $file . '</li>';
                }
                print '</ul>';
            }
            
            // 2. Vérifier la configuration jQuery
            print '<h3>2. Configuration jQuery</h3>';
            
            $jquery_config = array(
                'MAIN_USE_JQUERY_MULTISELECT' => getDolGlobalString('MAIN_USE_JQUERY_MULTISELECT'),
                'MAIN_DISABLE_JAVASCRIPT' => getDolGlobalString('MAIN_DISABLE_JAVASCRIPT'),
                'MAIN_OPTIMIZE_SPEED' => getDolGlobalString('MAIN_OPTIMIZE_SPEED')
            );
            
            foreach ($jquery_config as $param => $value) {
                print $param . ': ' . ($value ? $value : 'non défini') . '<br>';
            }
            
            // 3. Vérifier les modules problématiques
            print '<h3>3. Modules avec erreurs JavaScript</h3>';
            
            $problematic_modules = array();
            
            // Vérifier le module quality
            if (file_exists($quality_js)) {
                $content = file_get_contents($quality_js);
                if (strpos($content, 'syntax error') !== false || strpos($content, 'Parse error') !== false) {
                    $problematic_modules[] = 'quality (erreur de syntaxe)';
                }
            }
            
            if (empty($problematic_modules)) {
                print '<p style="color: green;">Aucun module avec erreur détecté.</p>';
            } else {
                print '<p style="color: red;">Modules problématiques :</p>';
                print '<ul>';
                foreach ($problematic_modules as $module) {
                    print '<li>' . $module . '</li>';
                }
                print '</ul>';
            }
            
            break;
            
        case 'fix_all':
            print '<h2>Correction des problèmes JavaScript/CSS</h2>';
            
            // 1. Créer le fichier mobile_styles.css manquant
            print '<h3>1. Création des fichiers manquants</h3>';
            
            $mobile_css = DOL_DOCUMENT_ROOT . '/custom/douchette/mobile/mobile_styles.css';
            if (!file_exists($mobile_css)) {
                $mobile_css_content = '/* Mobile styles for Douchette module */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    margin: 0;
    padding: 10px;
    background-color: #f5f5f5;
}

.container {
    max-width: 100%;
    margin: 0 auto;
}

.card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background: #005a87;
}

@media (max-width: 768px) {
    body {
        padding: 5px;
    }
    
    .card {
        padding: 10px;
    }
}';
                
                if (!is_dir(dirname($mobile_css))) {
                    mkdir(dirname($mobile_css), 0755, true);
                }
                
                if (file_put_contents($mobile_css, $mobile_css_content)) {
                    print "✓ mobile_styles.css créé<br>";
                } else {
                    print "✗ Erreur lors de la création de mobile_styles.css<br>";
                }
            } else {
                print "✓ mobile_styles.css existe déjà<br>";
            }
            
            // 2. Corriger quality.js.php
            print '<h3>2. Correction de quality.js.php</h3>';
            
            $quality_js = DOL_DOCUMENT_ROOT . '/custom/quality/js/quality.js.php';
            if (file_exists($quality_js)) {
                $content = file_get_contents($quality_js);
                
                // Vérifier s'il y a des erreurs de syntaxe PHP
                if (strpos($content, '<?php') === false) {
                    $fixed_content = '<?php
header("Content-Type: application/javascript");
if (!defined("DOL_VERSION")) {
    require_once "../../../main.inc.php";
}
?>

' . $content;
                    
                    if (file_put_contents($quality_js, $fixed_content)) {
                        print "✓ quality.js.php corrigé (ajout de l'en-tête PHP)<br>";
                    } else {
                        print "✗ Erreur lors de la correction de quality.js.php<br>";
                    }
                } else {
                    print "✓ quality.js.php semble correct<br>";
                }
            } else {
                // Créer un fichier quality.js.php basique
                $quality_js_content = '<?php
header("Content-Type: application/javascript");
if (!defined("DOL_VERSION")) {
    require_once "../../../main.inc.php";
}
?>

// Quality module JavaScript
console.log("Quality module JavaScript loaded");

$(document).ready(function() {
    // Initialisation du module Quality
    if (typeof $ !== "undefined") {
        console.log("jQuery loaded for Quality module");
        
        // Initialiser Select2 si disponible
        if (typeof $.fn.select2 !== "undefined") {
            $(".quality-select").select2({
                width: "100%"
            });
        }
        
        // Autres initialisations...
    }
});';
                
                if (!is_dir(dirname($quality_js))) {
                    mkdir(dirname($quality_js), 0755, true);
                }
                
                if (file_put_contents($quality_js, $quality_js_content)) {
                    print "✓ quality.js.php créé<br>";
                } else {
                    print "✗ Erreur lors de la création de quality.js.php<br>";
                }
            }
            
            // 3. Configurer jQuery et ses plugins
            print '<h3>3. Configuration jQuery</h3>';
            
            // Activer Select2
            $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) VALUES ('MAIN_USE_JQUERY_MULTISELECT', 'select2', 'chaine', 'Use Select2 for multiselect', '0', '1') ON DUPLICATE KEY UPDATE value = 'select2'";
            if ($db->query($sql)) {
                print "✓ Select2 activé<br>";
            }
            
            // Désactiver l'optimisation de vitesse qui peut causer des problèmes
            $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) VALUES ('MAIN_OPTIMIZE_SPEED', '0', 'chaine', 'Disable speed optimization', '0', '1') ON DUPLICATE KEY UPDATE value = '0'";
            if ($db->query($sql)) {
                print "✓ Optimisation de vitesse désactivée<br>";
            }
            
            // S'assurer que JavaScript n'est pas désactivé
            $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name = 'MAIN_DISABLE_JAVASCRIPT'";
            if ($db->query($sql)) {
                print "✓ JavaScript activé<br>";
            }
            
            // 4. Vider les caches
            print '<h3>4. Vidage des caches</h3>';
            
            $cache_dirs = array(
                DOL_DATA_ROOT . '/admin/temp',
                DOL_DATA_ROOT . '/admin/temp/js',
                DOL_DATA_ROOT . '/admin/temp/css'
            );
            
            foreach ($cache_dirs as $dir) {
                if (is_dir($dir)) {
                    if (function_exists('dol_delete_dir_recursive')) {
                        dol_delete_dir_recursive($dir);
                        print "✓ Cache {$dir} vidé<br>";
                    }
                }
            }
            
            print '<p><strong style="color: green;">Correction terminée !</strong></p>';
            print '<p><strong>Actions recommandées :</strong></p>';
            print '<ul>';
            print '<li>Rechargez la page principale (F5)</li>';
            print '<li>Videz le cache de votre navigateur (Ctrl+Shift+R)</li>';
            print '<li>Vérifiez la console JavaScript (F12) pour confirmer que les erreurs ont disparu</li>';
            print '</ul>';
            
            break;
    }
} else {
    // Menu principal
    print '<h2>Correction des erreurs JavaScript et CSS</h2>';
    print '<p>Ce script corrige les erreurs JavaScript détectées dans la console :</p>';
    
    print '<h3>Erreurs détectées :</h3>';
    print '<ul>';
    print '<li><strong>mobile_styles.css (404)</strong> : Fichier CSS manquant pour le module douchette</li>';
    print '<li><strong>quality.js.php (500)</strong> : Erreur serveur dans le fichier JavaScript du module quality</li>';
    print '<li><strong>jQuery(...).sortable is not a function</strong> : jQuery UI manquant</li>';
    print '<li><strong>$(...).select2 is not a function</strong> : Plugin Select2 non chargé</li>';
    print '<li><strong>jQuery(...).tooltip is not a function</strong> : jQuery UI tooltip manquant</li>';
    print '<li><strong>$(...).datepicker is not a function</strong> : jQuery UI datepicker manquant</li>';
    print '</ul>';
    
    print '<p><a href="?action=diagnostic" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Diagnostic Détaillé</a></p>';
    print '<p><a href="?action=fix_all" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Corriger Tous les Problèmes</a></p>';
}

print '</body></html>';
?>
