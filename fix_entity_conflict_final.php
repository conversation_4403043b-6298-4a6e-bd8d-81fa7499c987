<?php
/**
 * Correction finale du conflit d'entités et des traductions bizarres
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Correction Conflit Entités</title></head><body>';
print '<h1>Correction Finale du Conflit d\'Entités</h1>';

if (isset($_GET['action']) && $_GET['action'] == 'fix_entity_conflict') {
    print '<h2>Correction du Conflit d\'Entités</h2>';
    
    $fixes = array();
    $errors = array();
    
    try {
        global $user, $conf;
        
        // 1. Corriger le conflit d'entités utilisateur
        print '<h3>1. Correction du conflit d\'entités</h3>';
        
        print "<p><strong>Situation actuelle :</strong></p>";
        print "<ul>";
        print "<li>Utilisateur entité : {$user->entity}</li>";
        print "<li>Système entité : {$conf->entity}</li>";
        print "</ul>";
        
        if ($user->entity != $conf->entity) {
            // Synchroniser l'entité utilisateur avec le système
            $sql = "UPDATE " . MAIN_DB_PREFIX . "user SET entity = {$conf->entity} WHERE rowid = {$user->id}";
            if ($db->query($sql)) {
                $fixes[] = "Entité utilisateur synchronisée : {$user->entity} → {$conf->entity}";
                $user->entity = $conf->entity; // Mettre à jour en mémoire
            }
        } else {
            $fixes[] = "Entités déjà synchronisées";
        }
        
        // 2. Vérifier et corriger les fichiers de langue
        print '<h3>2. Vérification des fichiers de langue</h3>';
        
        $main_lang_file = DOL_DOCUMENT_ROOT . '/langs/fr_FR/main.lang';
        if (file_exists($main_lang_file)) {
            $content = file_get_contents($main_lang_file);
            
            // Vérifier le contenu bizarre
            if (strpos($content, 'Company=Connexion sur l\'entité') !== false) {
                $fixes[] = "❌ Contenu bizarre détecté dans main.lang : 'Company=Connexion sur l'entité'";
                
                // Corriger le contenu
                $corrected_content = str_replace('Company=Connexion sur l\'entité', 'Company=Société', $content);
                
                if (file_put_contents($main_lang_file, $corrected_content)) {
                    $fixes[] = "✅ Fichier main.lang corrigé : Company=Société";
                } else {
                    $errors[] = "❌ Impossible de corriger le fichier main.lang";
                }
            } else {
                $fixes[] = "✅ Fichier main.lang semble correct";
            }
        }
        
        // 3. Forcer la cohérence des configurations d'entité
        print '<h3>3. Cohérence des configurations</h3>';
        
        // S'assurer que toutes les configurations critiques sont dans la bonne entité
        $critical_configs = array(
            'MAIN_LANG_DEFAULT',
            'MAIN_MULTILANGS'
        );
        
        foreach ($critical_configs as $config) {
            // Vérifier dans l'entité courante
            $sql = "SELECT value FROM " . MAIN_DB_PREFIX . "const WHERE name = '{$config}' AND entity = {$conf->entity}";
            $result = $db->query($sql);
            
            if (!$result || $db->num_rows($result) == 0) {
                // Copier depuis l'entité 0 ou créer
                $sql2 = "SELECT value FROM " . MAIN_DB_PREFIX . "const WHERE name = '{$config}' AND entity = 0";
                $result2 = $db->query($sql2);
                
                if ($result2 && $obj = $db->fetch_object($result2)) {
                    // Copier vers l'entité courante
                    $sql3 = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                             VALUES ('{$config}', '{$obj->value}', 'chaine', 'Copied for entity consistency', '0', {$conf->entity})";
                    if ($db->query($sql3)) {
                        $fixes[] = "Configuration {$config} copiée vers entité {$conf->entity}";
                    }
                } else {
                    // Créer avec valeur par défaut
                    $default_value = ($config == 'MAIN_LANG_DEFAULT') ? 'fr_FR' : '1';
                    $sql3 = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                             VALUES ('{$config}', '{$default_value}', 'chaine', 'Created for entity consistency', '0', {$conf->entity})";
                    if ($db->query($sql3)) {
                        $fixes[] = "Configuration {$config} créée pour entité {$conf->entity}";
                    }
                }
            }
        }
        
        // 4. Nettoyer les caches de traduction
        print '<h3>4. Nettoyage des caches</h3>';
        
        // Vider tous les caches liés aux traductions
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%CACHE%'";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            $fixes[] = "Caches vidés ({$affected} entrées)";
        }
        
        // Supprimer les fichiers de cache
        $cache_dirs = array(
            DOL_DATA_ROOT . '/admin/temp',
            DOL_DATA_ROOT . '/admin/temp/module'
        );
        
        foreach ($cache_dirs as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '/*');
                $deleted = 0;
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                        $deleted++;
                    }
                }
                if ($deleted > 0) {
                    $fixes[] = "Fichiers cache supprimés dans {$dir} ({$deleted} fichiers)";
                }
            }
        }
        
        // 5. Test final des traductions
        print '<h3>5. Test final après correction</h3>';
        
        // Recharger les traductions
        global $langs;
        $langs->load("main");
        
        $test_keys = array('Company', 'Product', 'Invoice', 'Order', 'Contact');
        $all_ok = true;
        
        print '<table border="1" style="border-collapse: collapse; width: 100%;">';
        print '<tr><th>Clé</th><th>Traduction</th><th>Statut</th></tr>';
        
        foreach ($test_keys as $key) {
            $translation = $langs->trans($key);
            $status = '✅ OK';
            
            if ($translation == 'A text to show') {
                $status = '❌ PROBLÈME PERSISTE';
                $all_ok = false;
                $errors[] = "Traduction {$key} retourne encore 'A text to show'";
            } elseif ($translation == $key) {
                $status = '⚠️ NON TRADUIT';
            }
            
            print "<tr><td>{$key}</td><td>{$translation}</td><td>{$status}</td></tr>";
        }
        print '</table>';
        
        if ($all_ok) {
            $fixes[] = "✅ Toutes les traductions testées fonctionnent";
        }
        
        // 6. Informations de débogage
        print '<h3>6. Informations de débogage</h3>';
        
        print '<table border="1" style="border-collapse: collapse; width: 100%;">';
        print '<tr><th>Information</th><th>Valeur</th></tr>';
        print "<tr><td>Entité utilisateur (après correction)</td><td>{$user->entity}</td></tr>";
        print "<tr><td>Entité système</td><td>{$conf->entity}</td></tr>";
        print "<tr><td>Langue par défaut</td><td>{$langs->defaultlang}</td></tr>";
        print "<tr><td>Multisociete actif</td><td>" . ($conf->global->MAIN_MODULE_MULTISOCIETE ?? '0') . "</td></tr>";
        print '</table>';
        
    } catch (Exception $e) {
        $errors[] = "Erreur: " . $e->getMessage();
    }
    
    // Affichage des résultats
    if (!empty($fixes)) {
        print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>✅ Corrections effectuées :</h4>';
        print '<ul>';
        foreach ($fixes as $fix) {
            print '<li>' . $fix . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    if (!empty($errors)) {
        print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>❌ Problèmes détectés :</h4>';
        print '<ul>';
        foreach ($errors as $error) {
            print '<li>' . $error . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4 style="color: white;">🔄 Étapes Suivantes</h4>';
    print '<ol style="color: white;">';
    print '<li><strong>Redémarrez MAMP complètement</strong></li>';
    print '<li><strong>Videz le cache de votre navigateur</strong> (Ctrl+F5)</li>';
    print '<li><strong>Reconnectez-vous à Dolibarr</strong></li>';
    print '<li><strong>Vérifiez les pages où "A text to show" apparaissait</strong></li>';
    print '<li><strong>Si le problème persiste, indiquez-nous les pages exactes</strong></li>';
    print '</ol>';
    print '</div>';
    
} else {
    // Menu principal
    print '<h2>Correction du Conflit d\'Entités</h2>';
    
    print '<div style="background: #ffc107; color: black; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🎯 Problèmes Identifiés</h3>';
    print '<ul>';
    print '<li>⚠️ <strong>Conflit d\'entités</strong> : Utilisateur entité 0, système entité 1</li>';
    print '<li>⚠️ <strong>Contenu bizarre</strong> dans main.lang : "Company=Connexion sur l\'entité"</li>';
    print '<li>⚠️ <strong>Module Multisociete</strong> actif avec conflits potentiels</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #28a745; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🛠️ Solution Ciblée</h3>';
    print '<p style="color: white;">Cette correction va résoudre les problèmes identifiés :</p>';
    print '<ul style="color: white;">';
    print '<li>🔧 <strong>Synchroniser les entités</strong> utilisateur et système</li>';
    print '<li>🔧 <strong>Corriger le fichier main.lang</strong> avec le bon contenu</li>';
    print '<li>🔧 <strong>Forcer la cohérence</strong> des configurations</li>';
    print '<li>🔧 <strong>Vider tous les caches</strong> corrompus</li>';
    print '<li>🔧 <strong>Tester les traductions</strong> après correction</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 30px 0;">';
    print '<a href="?action=fix_entity_conflict" style="background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold;">🔧 CORRIGER LES CONFLITS</a>';
    print '</div>';
    
    print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>💡 Pourquoi cette correction ?</h4>';
    print '<p>Le diagnostic a révélé que :</p>';
    print '<ul>';
    print '<li>✅ <strong>Les traductions de base fonctionnent</strong> dans le script de test</li>';
    print '<li>❌ <strong>Mais il y a des conflits d\'entités</strong> et du contenu bizarre</li>';
    print '<li>❌ <strong>"A text to show" apparaît probablement</strong> dans des contextes spécifiques</li>';
    print '</ul>';
    print '<p><strong>Cette correction va éliminer ces incohérences.</strong></p>';
    print '</div>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>❓ Question Importante</h4>';
    print '<p><strong>Après cette correction, si "A text to show" apparaît encore :</strong></p>';
    print '<ul>';
    print '<li>📍 <strong>Indiquez-nous les pages exactes</strong> où vous le voyez</li>';
    print '<li>📍 <strong>Quels éléments spécifiques</strong> affichent ce texte</li>';
    print '<li>📍 <strong>Dans quels contextes</strong> (menus, listes, formulaires, etc.)</li>';
    print '</ul>';
    print '<p>Cela nous aidera à identifier le problème contextuel précis.</p>';
    print '</div>';
}

print '</body></html>';
?>
