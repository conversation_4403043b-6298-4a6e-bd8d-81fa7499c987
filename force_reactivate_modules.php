<?php
/**
 * Forcer la réactivation des modules SMI et Digirisk
 * Reconstruction complète des modules corrompus
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Forcer Réactivation Modules</title></head><body>';
print '<h1>Forcer la Réactivation des Modules SMI et Digirisk</h1>';

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'analyze_corruption':
            print '<h2>Analyse de la Corruption des Modules</h2>';
            
            $modules = array(
                'SMI' => array(
                    'const_name' => 'MAIN_MODULE_SMI',
                    'path' => 'custom/smi',
                    'class_file' => 'core/modules/modSMI.class.php',
                    'tables' => array('smi_indicator', 'smi_process', 'smi_action')
                ),
                'DIGIRISKDOLIBARR' => array(
                    'const_name' => 'MAIN_MODULE_DIGIRISKDOLIBARR',
                    'path' => 'custom/digiriskdolibarr',
                    'class_file' => 'core/modules/modDigiriskDolibarr.class.php',
                    'tables' => array('digiriskdolibarr_risk', 'digiriskdolibarr_riskassessment')
                )
            );
            
            foreach ($modules as $module_name => $module_info) {
                print "<h3>Module {$module_name}</h3>";
                print '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
                print '<tr><th>Vérification</th><th>Statut</th><th>Détails</th></tr>';
                
                // 1. Vérifier la constante d'activation
                $sql = "SELECT value FROM " . MAIN_DB_PREFIX . "const WHERE name = '{$module_info['const_name']}'";
                $result = $db->query($sql);
                $const_status = '❌ Non trouvée';
                $const_value = '';
                if ($result && $obj = $db->fetch_object($result)) {
                    $const_value = $obj->value;
                    $const_status = ($obj->value == '1') ? '✅ Activé' : '⚠️ Désactivé';
                }
                print "<tr><td>Constante d'activation</td><td>{$const_status}</td><td>{$module_info['const_name']} = {$const_value}</td></tr>";
                
                // 2. Vérifier les fichiers du module
                $module_path = DOL_DOCUMENT_ROOT . '/' . $module_info['path'];
                $path_status = is_dir($module_path) ? '✅ Existe' : '❌ Manquant';
                print "<tr><td>Répertoire module</td><td>{$path_status}</td><td>{$module_path}</td></tr>";
                
                if (is_dir($module_path)) {
                    $class_file_path = $module_path . '/' . $module_info['class_file'];
                    $class_status = file_exists($class_file_path) ? '✅ Existe' : '❌ Manquant';
                    print "<tr><td>Fichier de classe</td><td>{$class_status}</td><td>{$class_file_path}</td></tr>";
                    
                    // Vérifier d'autres fichiers critiques
                    $other_files = array(
                        'admin/setup.php',
                        'admin/about.php',
                        'sql/llx_' . strtolower($module_name) . '.sql'
                    );
                    
                    foreach ($other_files as $file) {
                        $file_path = $module_path . '/' . $file;
                        $file_status = file_exists($file_path) ? '✅' : '❌';
                        print "<tr><td>Fichier {$file}</td><td>{$file_status}</td><td>{$file_path}</td></tr>";
                    }
                }
                
                // 3. Vérifier les tables
                foreach ($module_info['tables'] as $table) {
                    $sql = "SHOW TABLES LIKE '" . MAIN_DB_PREFIX . $table . "'";
                    $result = $db->query($sql);
                    $table_status = ($result && $db->num_rows($result) > 0) ? '✅ Existe' : '❌ Manquante';
                    print "<tr><td>Table {$table}</td><td>{$table_status}</td><td>" . MAIN_DB_PREFIX . $table . "</td></tr>";
                }
                
                // 4. Vérifier les droits
                $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "user_rights WHERE module LIKE '%{$module_name}%'";
                $result = $db->query($sql);
                $rights_count = 0;
                if ($result && $obj = $db->fetch_object($result)) {
                    $rights_count = $obj->count;
                }
                $rights_status = ($rights_count > 0) ? "✅ {$rights_count} droits" : '❌ Aucun droit';
                print "<tr><td>Droits utilisateur</td><td>{$rights_status}</td><td>Droits dans user_rights</td></tr>";
                
                // 5. Vérifier les menus
                $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "menu WHERE module LIKE '%{$module_name}%'";
                $result = $db->query($sql);
                $menu_count = 0;
                if ($result && $obj = $db->fetch_object($result)) {
                    $menu_count = $obj->count;
                }
                $menu_status = ($menu_count > 0) ? "✅ {$menu_count} menus" : '❌ Aucun menu';
                print "<tr><td>Menus</td><td>{$menu_status}</td><td>Entrées dans menu</td></tr>";
                
                print '</table>';
            }
            
            // Vérifier les erreurs dans les logs
            print '<h3>Erreurs dans les logs</h3>';
            $log_file = DOL_DATA_ROOT . '/dolibarr.log';
            if (file_exists($log_file)) {
                $log_content = file_get_contents($log_file);
                $smi_errors = substr_count($log_content, 'smi');
                $digirisk_errors = substr_count($log_content, 'digirisk');
                
                print "<p>Erreurs SMI dans les logs : {$smi_errors}</p>";
                print "<p>Erreurs Digirisk dans les logs : {$digirisk_errors}</p>";
                
                if ($smi_errors > 0 || $digirisk_errors > 0) {
                    print '<div style="background: #f8d7da; padding: 10px; border-radius: 5px;">';
                    print '<h4>Dernières erreurs :</h4>';
                    $lines = explode("\n", $log_content);
                    $recent_errors = array_slice($lines, -20);
                    foreach ($recent_errors as $line) {
                        if (stripos($line, 'smi') !== false || stripos($line, 'digirisk') !== false) {
                            print '<p style="font-size: 12px; color: #721c24;">' . htmlspecialchars($line) . '</p>';
                        }
                    }
                    print '</div>';
                }
            }
            
            break;
            
        case 'force_reactivate':
            print '<h2>Forcer la Réactivation</h2>';
            
            $fixes = array();
            $errors = array();
            
            try {
                // 1. Nettoyer complètement les modules
                print '<h3>1. Nettoyage complet</h3>';
                
                $modules_to_clean = array('SMI', 'DIGIRISKDOLIBARR');
                
                foreach ($modules_to_clean as $module) {
                    // Supprimer toutes les traces du module
                    $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%{$module}%'";
                    if ($db->query($sql)) {
                        $affected = $db->affected_rows();
                        $fixes[] = "Module {$module}: {$affected} configurations supprimées";
                    }
                    
                    // Supprimer les droits
                    $sql = "DELETE FROM " . MAIN_DB_PREFIX . "user_rights WHERE module LIKE '%{$module}%'";
                    if ($db->query($sql)) {
                        $affected = $db->affected_rows();
                        $fixes[] = "Module {$module}: {$affected} droits supprimés";
                    }
                    
                    // Supprimer les menus
                    $sql = "DELETE FROM " . MAIN_DB_PREFIX . "menu WHERE module LIKE '%{$module}%'";
                    if ($db->query($sql)) {
                        $affected = $db->affected_rows();
                        $fixes[] = "Module {$module}: {$affected} menus supprimés";
                    }
                    
                    // Supprimer les widgets
                    $sql = "DELETE FROM " . MAIN_DB_PREFIX . "boxes WHERE file LIKE '%{$module}%'";
                    if ($db->query($sql)) {
                        $affected = $db->affected_rows();
                        $fixes[] = "Module {$module}: {$affected} widgets supprimés";
                    }
                }
                
                // 2. Recréer les configurations de base
                print '<h3>2. Recréation des configurations</h3>';
                
                // SMI
                $smi_configs = array(
                    'MAIN_MODULE_SMI' => '0',
                    'SMI_VERSION' => '1.0.0',
                    'SMI_SETUP_COMPLETE' => '0'
                );
                
                foreach ($smi_configs as $name => $value) {
                    $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                            VALUES ('{$name}', '{$value}', 'chaine', 'SMI module config', '0', '1')";
                    if ($db->query($sql)) {
                        $fixes[] = "SMI: Configuration {$name} créée";
                    }
                }
                
                // Digirisk
                $digirisk_configs = array(
                    'MAIN_MODULE_DIGIRISKDOLIBARR' => '0',
                    'DIGIRISKDOLIBARR_VERSION' => '1.0.0',
                    'DIGIRISKDOLIBARR_SETUP_COMPLETE' => '0'
                );
                
                foreach ($digirisk_configs as $name => $value) {
                    $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                            VALUES ('{$name}', '{$value}', 'chaine', 'Digirisk module config', '0', '1')";
                    if ($db->query($sql)) {
                        $fixes[] = "Digirisk: Configuration {$name} créée";
                    }
                }
                
                // 3. Vérifier et créer les tables si nécessaire
                print '<h3>3. Vérification des tables</h3>';
                
                // Tables SMI
                $smi_tables = array(
                    'smi_indicator' => "CREATE TABLE IF NOT EXISTS " . MAIN_DB_PREFIX . "smi_indicator (
                        rowid int(11) NOT NULL AUTO_INCREMENT,
                        ref varchar(128) NOT NULL,
                        label varchar(255) NOT NULL,
                        description text,
                        target_value decimal(10,2),
                        current_value decimal(10,2),
                        fk_process int(11),
                        status int(11) DEFAULT 1,
                        datec datetime,
                        tms timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        fk_user_creat int(11),
                        fk_user_modif int(11),
                        entity int(11) DEFAULT 1,
                        PRIMARY KEY (rowid)
                    )",
                    'smi_process' => "CREATE TABLE IF NOT EXISTS " . MAIN_DB_PREFIX . "smi_process (
                        rowid int(11) NOT NULL AUTO_INCREMENT,
                        ref varchar(128) NOT NULL,
                        label varchar(255) NOT NULL,
                        description text,
                        status int(11) DEFAULT 1,
                        datec datetime,
                        tms timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        fk_user_creat int(11),
                        fk_user_modif int(11),
                        entity int(11) DEFAULT 1,
                        PRIMARY KEY (rowid)
                    )"
                );
                
                foreach ($smi_tables as $table_name => $create_sql) {
                    if ($db->query($create_sql)) {
                        $fixes[] = "Table {$table_name} créée/vérifiée";
                    }
                }
                
                // Tables Digirisk (basiques)
                $digirisk_tables = array(
                    'digiriskdolibarr_risk' => "CREATE TABLE IF NOT EXISTS " . MAIN_DB_PREFIX . "digiriskdolibarr_risk (
                        rowid int(11) NOT NULL AUTO_INCREMENT,
                        ref varchar(128) NOT NULL,
                        label varchar(255) NOT NULL,
                        description text,
                        category varchar(50),
                        status int(11) DEFAULT 1,
                        datec datetime,
                        tms timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        fk_user_creat int(11),
                        fk_user_modif int(11),
                        entity int(11) DEFAULT 1,
                        PRIMARY KEY (rowid)
                    )"
                );
                
                foreach ($digirisk_tables as $table_name => $create_sql) {
                    if ($db->query($create_sql)) {
                        $fixes[] = "Table {$table_name} créée/vérifiée";
                    }
                }
                
                // 4. Forcer l'activation
                print '<h3>4. Activation forcée</h3>';
                
                $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '1' WHERE name = 'MAIN_MODULE_SMI'";
                if ($db->query($sql)) {
                    $fixes[] = "Module SMI activé de force";
                }
                
                $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '1' WHERE name = 'MAIN_MODULE_DIGIRISKDOLIBARR'";
                if ($db->query($sql)) {
                    $fixes[] = "Module Digirisk activé de force";
                }
                
                // 5. Créer les droits de base pour l'admin
                print '<h3>5. Création des droits admin</h3>';
                
                $admin_rights = array(
                    array('module' => 'smi', 'perms' => 'read'),
                    array('module' => 'smi', 'perms' => 'write'),
                    array('module' => 'digiriskdolibarr', 'perms' => 'read'),
                    array('module' => 'digiriskdolibarr', 'perms' => 'write')
                );
                
                foreach ($admin_rights as $right) {
                    $sql = "INSERT IGNORE INTO " . MAIN_DB_PREFIX . "user_rights (fk_user, module, perms, subperms, entity) 
                            VALUES (1, '{$right['module']}', '{$right['perms']}', '', 1)";
                    if ($db->query($sql)) {
                        $fixes[] = "Droit {$right['module']}.{$right['perms']} créé pour admin";
                    }
                }
                
                // 6. Vider les caches
                print '<h3>6. Vidage des caches</h3>';
                
                $cache_dirs = array(
                    DOL_DATA_ROOT . '/admin/temp',
                    DOL_DATA_ROOT . '/admin/temp/module'
                );
                
                foreach ($cache_dirs as $dir) {
                    if (is_dir($dir)) {
                        $files = glob($dir . '/*');
                        foreach ($files as $file) {
                            if (is_file($file)) {
                                unlink($file);
                            }
                        }
                        $fixes[] = "Cache {$dir} vidé";
                    }
                }
                
            } catch (Exception $e) {
                $errors[] = "Erreur: " . $e->getMessage();
            }
            
            // Affichage des résultats
            if (!empty($fixes)) {
                print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>✅ Réactivation forcée effectuée :</h4>';
                print '<ul>';
                foreach ($fixes as $fix) {
                    print '<li>' . $fix . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            if (!empty($errors)) {
                print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>❌ Erreurs :</h4>';
                print '<ul>';
                foreach ($errors as $error) {
                    print '<li>' . $error . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            print '<div style="background: #28a745; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
            print '<h4 style="color: white;">🎉 Réactivation Terminée !</h4>';
            print '<p style="color: white;"><strong>Les modules ont été forcés à se réactiver. Maintenant :</strong></p>';
            print '<ol style="color: white;">';
            print '<li><strong>Redémarrez MAMP</strong></li>';
            print '<li><strong>Reconnectez-vous à Dolibarr</strong></li>';
            print '<li><strong>Allez dans Configuration > Modules</strong></li>';
            print '<li><strong>Vérifiez que SMI et Digirisk sont activés</strong></li>';
            print '<li><strong>Si nécessaire, désactivez puis réactivez-les</strong></li>';
            print '</ol>';
            print '</div>';
            
            break;
    }
} else {
    // Menu principal
    print '<h2>Forcer la Réactivation des Modules</h2>';
    
    print '<div style="background: #dc3545; color: white; padding: 20px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🚨 Modules Corrompus</h3>';
    print '<p style="color: white;">Les modules <strong>SMI et Digirisk ne se réactivent plus</strong> à cause de corruptions dans :</p>';
    print '<ul style="color: white;">';
    print '<li>❌ Configurations en base de données</li>';
    print '<li>❌ Tables manquantes ou corrompues</li>';
    print '<li>❌ Droits utilisateur perdus</li>';
    print '<li>❌ Fichiers de classe endommagés</li>';
    print '<li>❌ Caches corrompus</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🔧 Solution de Force</h3>';
    print '<p style="color: white;">Ce script va <strong>forcer la réactivation</strong> en :</p>';
    print '<ul style="color: white;">';
    print '<li>✅ <strong>Nettoyant complètement</strong> toutes les traces des modules</li>';
    print '<li>✅ <strong>Recréant</strong> les configurations de base</li>';
    print '<li>✅ <strong>Vérifiant/créant</strong> les tables nécessaires</li>';
    print '<li>✅ <strong>Forçant l\'activation</strong> en base</li>';
    print '<li>✅ <strong>Recréant</strong> les droits admin</li>';
    print '<li>✅ <strong>Vidant</strong> tous les caches</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 20px 0;">';
    print '<a href="?action=analyze_corruption" style="background: #ffc107; color: black; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin-right: 10px;">🔍 ANALYSER LA CORRUPTION</a>';
    print '<a href="?action=force_reactivate" style="background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px;">💪 FORCER LA RÉACTIVATION</a>';
    print '</div>';
    
    print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>⚠️ ATTENTION</h4>';
    print '<p>Cette opération va <strong>reconstruire complètement</strong> les modules. Toutes les données et configurations personnalisées des modules SMI et Digirisk seront perdues.</p>';
    print '<p><strong>Recommandation :</strong> Commencez par l\'analyse pour voir exactement ce qui est corrompu.</p>';
    print '</div>';
}

print '</body></html>';
?>
