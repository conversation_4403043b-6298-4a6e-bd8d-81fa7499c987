<?php
/**
 * Restaurer les droits des modules SMI et Digirisk
 * Solution ciblée pour le problème de réactivation
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Restaurer Droits Modules</title></head><body>';
print '<h1>Restaurer les Droits des Modules SMI et Digirisk</h1>';

if (isset($_GET['action']) && $_GET['action'] == 'restore_rights') {
    print '<h2>Restauration des Droits</h2>';
    
    $fixes = array();
    $errors = array();
    
    try {
        // 1. Créer les droits pour le module SMI
        print '<h3>1. Restauration des droits SMI</h3>';
        
        $smi_rights = array(
            array('module' => 'smi', 'perms' => 'lire', 'subperms' => ''),
            array('module' => 'smi', 'perms' => 'creer', 'subperms' => ''),
            array('module' => 'smi', 'perms' => 'modifier', 'subperms' => ''),
            array('module' => 'smi', 'perms' => 'supprimer', 'subperms' => ''),
            array('module' => 'smi', 'perms' => 'configurer', 'subperms' => ''),
            array('module' => 'smi', 'perms' => 'indicator', 'subperms' => 'lire'),
            array('module' => 'smi', 'perms' => 'indicator', 'subperms' => 'creer'),
            array('module' => 'smi', 'perms' => 'indicator', 'subperms' => 'modifier'),
            array('module' => 'smi', 'perms' => 'indicator', 'subperms' => 'supprimer'),
            array('module' => 'smi', 'perms' => 'process', 'subperms' => 'lire'),
            array('module' => 'smi', 'perms' => 'process', 'subperms' => 'creer'),
            array('module' => 'smi', 'perms' => 'process', 'subperms' => 'modifier'),
            array('module' => 'smi', 'perms' => 'process', 'subperms' => 'supprimer'),
            array('module' => 'smi', 'perms' => 'action', 'subperms' => 'lire'),
            array('module' => 'smi', 'perms' => 'action', 'subperms' => 'creer'),
            array('module' => 'smi', 'perms' => 'action', 'subperms' => 'modifier'),
            array('module' => 'smi', 'perms' => 'action', 'subperms' => 'supprimer')
        );
        
        foreach ($smi_rights as $right) {
            $sql = "INSERT IGNORE INTO " . MAIN_DB_PREFIX . "user_rights (fk_user, module, perms, subperms, entity) 
                    VALUES (1, '{$right['module']}', '{$right['perms']}', '{$right['subperms']}', 1)";
            if ($db->query($sql)) {
                $fixes[] = "SMI: Droit {$right['perms']}" . ($right['subperms'] ? ".{$right['subperms']}" : "") . " créé";
            }
        }
        
        // 2. Créer les droits pour le module Digirisk
        print '<h3>2. Restauration des droits Digirisk</h3>';
        
        $digirisk_rights = array(
            array('module' => 'digiriskdolibarr', 'perms' => 'lire', 'subperms' => ''),
            array('module' => 'digiriskdolibarr', 'perms' => 'creer', 'subperms' => ''),
            array('module' => 'digiriskdolibarr', 'perms' => 'modifier', 'subperms' => ''),
            array('module' => 'digiriskdolibarr', 'perms' => 'supprimer', 'subperms' => ''),
            array('module' => 'digiriskdolibarr', 'perms' => 'configurer', 'subperms' => ''),
            array('module' => 'digiriskdolibarr', 'perms' => 'risk', 'subperms' => 'lire'),
            array('module' => 'digiriskdolibarr', 'perms' => 'risk', 'subperms' => 'creer'),
            array('module' => 'digiriskdolibarr', 'perms' => 'risk', 'subperms' => 'modifier'),
            array('module' => 'digiriskdolibarr', 'perms' => 'risk', 'subperms' => 'supprimer'),
            array('module' => 'digiriskdolibarr', 'perms' => 'riskassessment', 'subperms' => 'lire'),
            array('module' => 'digiriskdolibarr', 'perms' => 'riskassessment', 'subperms' => 'creer'),
            array('module' => 'digiriskdolibarr', 'perms' => 'riskassessment', 'subperms' => 'modifier'),
            array('module' => 'digiriskdolibarr', 'perms' => 'riskassessment', 'subperms' => 'supprimer'),
            array('module' => 'digiriskdolibarr', 'perms' => 'accident', 'subperms' => 'lire'),
            array('module' => 'digiriskdolibarr', 'perms' => 'accident', 'subperms' => 'creer'),
            array('module' => 'digiriskdolibarr', 'perms' => 'accident', 'subperms' => 'modifier'),
            array('module' => 'digiriskdolibarr', 'perms' => 'accident', 'subperms' => 'supprimer')
        );
        
        foreach ($digirisk_rights as $right) {
            $sql = "INSERT IGNORE INTO " . MAIN_DB_PREFIX . "user_rights (fk_user, module, perms, subperms, entity) 
                    VALUES (1, '{$right['module']}', '{$right['perms']}', '{$right['subperms']}', 1)";
            if ($db->query($sql)) {
                $fixes[] = "Digirisk: Droit {$right['perms']}" . ($right['subperms'] ? ".{$right['subperms']}" : "") . " créé";
            }
        }
        
        // 3. Créer les droits pour tous les utilisateurs admin
        print '<h3>3. Application aux autres utilisateurs admin</h3>';
        
        $sql = "SELECT rowid FROM " . MAIN_DB_PREFIX . "user WHERE admin = 1";
        $result = $db->query($sql);
        $admin_users = array();
        if ($result) {
            while ($obj = $db->fetch_object($result)) {
                $admin_users[] = $obj->rowid;
            }
        }
        
        foreach ($admin_users as $user_id) {
            if ($user_id == 1) continue; // Déjà fait
            
            // SMI rights
            foreach ($smi_rights as $right) {
                $sql = "INSERT IGNORE INTO " . MAIN_DB_PREFIX . "user_rights (fk_user, module, perms, subperms, entity) 
                        VALUES ({$user_id}, '{$right['module']}', '{$right['perms']}', '{$right['subperms']}', 1)";
                $db->query($sql);
            }
            
            // Digirisk rights
            foreach ($digirisk_rights as $right) {
                $sql = "INSERT IGNORE INTO " . MAIN_DB_PREFIX . "user_rights (fk_user, module, perms, subperms, entity) 
                        VALUES ({$user_id}, '{$right['module']}', '{$right['perms']}', '{$right['subperms']}', 1)";
                $db->query($sql);
            }
            
            $fixes[] = "Droits appliqués à l'utilisateur admin ID {$user_id}";
        }
        
        // 4. Activer les modules
        print '<h3>4. Activation des modules</h3>';
        
        $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '1' WHERE name = 'MAIN_MODULE_SMI'";
        if ($db->query($sql)) {
            $fixes[] = "Module SMI activé";
        }
        
        $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '1' WHERE name = 'MAIN_MODULE_DIGIRISKDOLIBARR'";
        if ($db->query($sql)) {
            $fixes[] = "Module Digirisk activé";
        }
        
        // 5. Vérification finale
        print '<h3>5. Vérification finale</h3>';
        
        $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "user_rights WHERE module = 'smi'";
        $result = $db->query($sql);
        if ($result && $obj = $db->fetch_object($result)) {
            $fixes[] = "✅ SMI: {$obj->count} droits créés";
        }
        
        $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "user_rights WHERE module = 'digiriskdolibarr'";
        $result = $db->query($sql);
        if ($result && $obj = $db->fetch_object($result)) {
            $fixes[] = "✅ Digirisk: {$obj->count} droits créés";
        }
        
        // 6. Vider le cache des permissions
        print '<h3>6. Vidage du cache des permissions</h3>';
        
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%CACHE_PERMS%'";
        if ($db->query($sql)) {
            $fixes[] = "Cache des permissions vidé";
        }
        
    } catch (Exception $e) {
        $errors[] = "Erreur: " . $e->getMessage();
    }
    
    // Affichage des résultats
    if (!empty($fixes)) {
        print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>✅ Droits restaurés :</h4>';
        print '<ul>';
        foreach ($fixes as $fix) {
            print '<li>' . $fix . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    if (!empty($errors)) {
        print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>❌ Erreurs :</h4>';
        print '<ul>';
        foreach ($errors as $error) {
            print '<li>' . $error . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    print '<div style="background: #28a745; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4 style="color: white;">🎉 Droits Restaurés !</h4>';
    print '<p style="color: white;"><strong>Les modules devraient maintenant être réactivables :</strong></p>';
    print '<ol style="color: white;">';
    print '<li><strong>Fermez cet onglet</strong></li>';
    print '<li><strong>Allez dans Configuration > Modules</strong></li>';
    print '<li><strong>Recherchez "SMI" et "Digirisk"</strong></li>';
    print '<li><strong>Ils devraient être activés automatiquement</strong></li>';
    print '<li><strong>Si pas activés, cliquez sur "Activer"</strong></li>';
    print '</ol>';
    print '</div>';
    
    print '<p><a href="/admin/modules.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Aller aux Modules</a></p>';
    
} else {
    // Menu principal
    print '<h2>Restaurer les Droits des Modules</h2>';
    
    print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🎯 Problème Identifié</h3>';
    print '<p style="color: white;">L\'analyse a révélé que <strong>les droits utilisateur ont été supprimés</strong> :</p>';
    print '<ul style="color: white;">';
    print '<li>✅ Fichiers de classe : <strong>Présents</strong></li>';
    print '<li>✅ Tables : <strong>Créées</strong></li>';
    print '<li>✅ Menus : <strong>Existants</strong></li>';
    print '<li>❌ Droits utilisateur : <strong>Manquants</strong></li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #28a745; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">✅ Solution Simple</h3>';
    print '<p style="color: white;">Ce script va <strong>restaurer uniquement les droits</strong> :</p>';
    print '<ul style="color: white;">';
    print '<li>🔧 <strong>Créer tous les droits SMI</strong> (lire, créer, modifier, supprimer, etc.)</li>';
    print '<li>🔧 <strong>Créer tous les droits Digirisk</strong> (risk, riskassessment, accident, etc.)</li>';
    print '<li>🔧 <strong>Appliquer aux utilisateurs admin</strong></li>';
    print '<li>🔧 <strong>Activer automatiquement</strong> les modules</li>';
    print '<li>🔧 <strong>Vider le cache</strong> des permissions</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 30px 0;">';
    print '<a href="?action=restore_rights" style="background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold;">🔧 RESTAURER LES DROITS</a>';
    print '</div>';
    
    print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>💡 Pourquoi cette solution ?</h4>';
    print '<p>Contrairement à une reconstruction complète, cette approche :</p>';
    print '<ul>';
    print '<li>✅ <strong>Préserve vos données</strong> existantes</li>';
    print '<li>✅ <strong>Ne touche pas aux configurations</strong></li>';
    print '<li>✅ <strong>Restaure uniquement</strong> ce qui manque</li>';
    print '<li>✅ <strong>Solution rapide</strong> et ciblée</li>';
    print '</ul>';
    print '</div>';
}

print '</body></html>';
?>
