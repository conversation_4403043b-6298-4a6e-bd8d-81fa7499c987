<?php
/**
 * Correction finale : Conflit d'entités + Encodage + Cache
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Correction Finale - Entités + Encodage</title></head><body>';
print '<h1>Correction Finale : Entités + Encodage + Cache</h1>';

if (isset($_GET['action']) && $_GET['action'] == 'fix_final') {
    print '<h2>Correction Finale</h2>';
    
    $fixes = array();
    $errors = array();
    
    try {
        global $user, $conf, $db;
        
        // 1. Corriger le conflit d'entités DÉFINITIVEMENT
        print '<h3>1. Correction définitive du conflit d\'entités</h3>';
        
        print "<p><strong>Situation actuelle :</strong></p>";
        print "<ul>";
        print "<li>Utilisateur entité : {$user->entity}</li>";
        print "<li>Système entité : {$conf->entity}</li>";
        print "</ul>";
        
        // Forcer l'utilisateur dans la même entité que le système
        $sql = "UPDATE " . MAIN_DB_PREFIX . "user SET entity = {$conf->entity} WHERE rowid = {$user->id}";
        if ($db->query($sql)) {
            $fixes[] = "✅ Utilisateur {$user->login} déplacé vers entité {$conf->entity}";
            $user->entity = $conf->entity; // Mettre à jour en mémoire
        }
        
        // Vérifier tous les autres utilisateurs avec entité 0
        $sql = "SELECT rowid, login FROM " . MAIN_DB_PREFIX . "user WHERE entity = 0 AND rowid != {$user->id}";
        $result = $db->query($sql);
        if ($result) {
            while ($obj = $db->fetch_object($result)) {
                $sql2 = "UPDATE " . MAIN_DB_PREFIX . "user SET entity = 1 WHERE rowid = {$obj->rowid}";
                if ($db->query($sql2)) {
                    $fixes[] = "✅ Utilisateur {$obj->login} déplacé vers entité 1";
                }
            }
        }
        
        // 2. Nettoyer COMPLÈTEMENT tous les caches
        print '<h3>2. Nettoyage complet de tous les caches</h3>';
        
        // Caches en base de données
        $cache_patterns = array(
            '%CACHE%',
            '%_CACHE_%',
            'MAIN_OPTIMIZE_%',
            'MAIN_DISABLE_TRANSLATION_CACHE'
        );
        
        foreach ($cache_patterns as $pattern) {
            $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '{$pattern}'";
            if ($db->query($sql)) {
                $affected = $db->affected_rows();
                if ($affected > 0) {
                    $fixes[] = "Cache base {$pattern} vidé ({$affected} entrées)";
                }
            }
        }
        
        // Caches fichiers
        $cache_dirs = array(
            DOL_DATA_ROOT . '/admin/temp',
            DOL_DATA_ROOT . '/admin/temp/module',
            DOL_DATA_ROOT . '/admin/temp/smarty',
            DOL_DATA_ROOT . '/admin/temp/js',
            DOL_DATA_ROOT . '/admin/temp/css'
        );
        
        foreach ($cache_dirs as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '/*');
                $deleted = 0;
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                        $deleted++;
                    }
                }
                if ($deleted > 0) {
                    $fixes[] = "Cache fichiers {$dir} vidé ({$deleted} fichiers)";
                }
            }
        }
        
        // 3. Forcer les configurations de langue dans TOUTES les entités
        print '<h3>3. Configuration forcée de la langue</h3>';
        
        $entities = array(0, 1, 2);
        $lang_configs = array(
            'MAIN_LANG_DEFAULT' => 'fr_FR',
            'MAIN_MULTILANGS' => '1',
            'MAIN_CHARSET' => 'UTF-8'
        );
        
        foreach ($entities as $entity) {
            foreach ($lang_configs as $config => $value) {
                $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                        VALUES ('{$config}', '{$value}', 'chaine', 'Forced config', '0', {$entity})
                        ON DUPLICATE KEY UPDATE value = '{$value}'";
                if ($db->query($sql)) {
                    $fixes[] = "Config {$config} = {$value} forcée pour entité {$entity}";
                }
            }
        }
        
        // 4. Corriger l'encodage HTML dans les traductions
        print '<h3>4. Correction de l\'encodage HTML</h3>';
        
        // Nettoyer les traductions avec encodage HTML
        $html_fixes = array(
            'Soci&eacute;t&eacute;' => 'Société',
            '&Eacute;diter' => 'Éditer',
            '&Eacute;tat' => 'État',
            'R&eacute;f&eacute;rence' => 'Référence',
            'T&eacute;l&eacute;phone' => 'Téléphone'
        );
        
        foreach ($html_fixes as $encoded => $decoded) {
            $sql = "UPDATE " . MAIN_DB_PREFIX . "overwrite_trans SET value = '{$decoded}' WHERE value = '{$encoded}'";
            if ($db->query($sql)) {
                $affected = $db->affected_rows();
                if ($affected > 0) {
                    $fixes[] = "Encodage corrigé : {$encoded} → {$decoded} ({$affected} entrées)";
                }
            }
        }
        
        // 5. Désactiver temporairement Multisociete pour test
        print '<h3>5. Test sans Multisociete</h3>';
        
        $multisociete_was_active = ($conf->global->MAIN_MODULE_MULTISOCIETE ?? '0') == '1';
        
        if ($multisociete_was_active) {
            $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '0' WHERE name = 'MAIN_MODULE_MULTISOCIETE'";
            if ($db->query($sql)) {
                $fixes[] = "⚠️ Multisociete désactivé temporairement pour test";
            }
        }
        
        // 6. Forcer le rechargement des traductions
        print '<h3>6. Rechargement forcé des traductions</h3>';
        
        // Supprimer toutes les traductions en cache
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE value = '' OR value IS NULL OR value = 'A text to show'";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            if ($affected > 0) {
                $fixes[] = "Traductions vides/corrompues supprimées ({$affected} entrées)";
            }
        }
        
        // Forcer la désactivation du cache des traductions
        $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                VALUES ('MAIN_DISABLE_TRANSLATION_CACHE', '1', 'chaine', 'Disable cache for debug', '0', 0)
                ON DUPLICATE KEY UPDATE value = '1'";
        if ($db->query($sql)) {
            $fixes[] = "Cache des traductions désactivé de force";
        }
        
        // 7. Test final après corrections
        print '<h3>7. Test final après corrections</h3>';
        
        // Recharger les traductions
        global $langs;
        $langs->load("main");
        
        $test_keys = array('Company', 'Product', 'Invoice', 'Contact', 'Menu', 'Action', 'Status', 'Date');
        $problems_found = 0;
        
        print '<table border="1" style="border-collapse: collapse; width: 100%;">';
        print '<tr><th>Clé</th><th>Traduction</th><th>Statut</th></tr>';
        
        foreach ($test_keys as $key) {
            $translation = $langs->trans($key);
            $status = '✅ OK';
            $class = '';
            
            if ($translation == 'A text to show') {
                $status = '❌ PROBLÈME PERSISTE';
                $class = 'style="background: #f8d7da;"';
                $problems_found++;
                $errors[] = "Traduction {$key} retourne encore 'A text to show'";
            } elseif (strpos($translation, '&') !== false) {
                $status = '⚠️ Encodage HTML';
                $class = 'style="background: #fff3cd;"';
            }
            
            print "<tr {$class}><td>{$key}</td><td>" . htmlspecialchars($translation) . "</td><td>{$status}</td></tr>";
        }
        print '</table>';
        
        if ($problems_found == 0) {
            $fixes[] = "✅ Aucun 'A text to show' détecté après correction";
        }
        
        // 8. Réactiver Multisociete si nécessaire
        if ($multisociete_was_active && $problems_found == 0) {
            $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '1' WHERE name = 'MAIN_MODULE_MULTISOCIETE'";
            if ($db->query($sql)) {
                $fixes[] = "✅ Multisociete réactivé (test réussi)";
            }
        } elseif ($multisociete_was_active && $problems_found > 0) {
            $fixes[] = "⚠️ Multisociete laissé désactivé (problèmes détectés)";
        }
        
    } catch (Exception $e) {
        $errors[] = "Erreur: " . $e->getMessage();
    }
    
    // Affichage des résultats
    if (!empty($fixes)) {
        print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>✅ Corrections effectuées :</h4>';
        print '<ul>';
        foreach ($fixes as $fix) {
            print '<li>' . $fix . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    if (!empty($errors)) {
        print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>❌ Problèmes persistants :</h4>';
        print '<ul>';
        foreach ($errors as $error) {
            print '<li>' . $error . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4 style="color: white;">🔄 Test Final Obligatoire</h4>';
    print '<ol style="color: white;">';
    print '<li><strong>Redémarrez MAMP complètement</strong></li>';
    print '<li><strong>Fermez tous les onglets Dolibarr</strong></li>';
    print '<li><strong>Videz le cache navigateur</strong> (Ctrl+Shift+Delete)</li>';
    print '<li><strong>Reconnectez-vous à Dolibarr</strong></li>';
    print '<li><strong>Testez le dashboard et les pages principales</strong></li>';
    print '</ol>';
    print '<p style="color: white;"><strong>Si "A text to show" apparaît encore, indiquez-nous les pages exactes.</strong></p>';
    print '</div>';
    
} else {
    // Menu principal
    print '<h2>Correction Finale Basée sur le Diagnostic</h2>';
    
    print '<div style="background: #28a745; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">✅ Diagnostic Réussi</h3>';
    print '<p style="color: white;">Le diagnostic en temps réel a révélé :</p>';
    print '<ul style="color: white;">';
    print '<li>✅ <strong>Aucun "A text to show"</strong> dans le contexte de test</li>';
    print '<li>⚠️ <strong>Conflit d\'entités</strong> : Utilisateur entité 0 ≠ Système entité 1</li>';
    print '<li>⚠️ <strong>Encodage HTML</strong> : Soci&eacute;t&eacute; au lieu de Société</li>';
    print '<li>⚠️ <strong>Traductions incohérentes</strong> : Certaines clés non traduites</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #dc3545; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🛠️ Correction Finale</h3>';
    print '<p style="color: white;">Cette correction va résoudre TOUS les problèmes identifiés :</p>';
    print '<ul style="color: white;">';
    print '<li>🔧 <strong>Corriger le conflit d\'entités</strong> définitivement</li>';
    print '<li>🔧 <strong>Nettoyer TOUS les caches</strong> (base + fichiers)</li>';
    print '<li>🔧 <strong>Forcer les configurations</strong> de langue</li>';
    print '<li>🔧 <strong>Corriger l\'encodage HTML</strong> dans les traductions</li>';
    print '<li>🔧 <strong>Tester sans Multisociete</strong> temporairement</li>';
    print '<li>🔧 <strong>Forcer le rechargement</strong> des traductions</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 30px 0;">';
    print '<a href="?action=fix_final" style="background: #dc3545; color: white; padding: 20px 40px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold;">🔧 CORRECTION FINALE</a>';
    print '</div>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>💡 Pourquoi cette correction va fonctionner ?</h4>';
    print '<p>Le diagnostic a montré que :</p>';
    print '<ul>';
    print '<li>✅ <strong>Le système de traduction fonctionne</strong> dans certains contextes</li>';
    print '<li>❌ <strong>Mais il y a des conflits d\'entités</strong> et des problèmes de cache</li>';
    print '<li>❌ <strong>"A text to show" apparaît probablement</strong> dans des pages spécifiques</li>';
    print '</ul>';
    print '<p><strong>Cette correction va éliminer toutes les causes possibles.</strong></p>';
    print '</div>';
}

print '</body></html>';
?>
