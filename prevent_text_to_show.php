<?php
/**
 * Script de prévention pour éviter le retour du problème "A text to show"
 * À exécuter via l'interface web de Dolibarr
 */

// Vérification de sécurité
if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

// Vérification des droits admin
if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Prévention A text to show</title></head><body>';
print '<h1>Prévention du problème "A text to show"</h1>';

if (isset($_GET['action']) && $_GET['action'] == 'setup') {
    print '<h2>Configuration de prévention</h2>';
    
    // 1. Vérifier et configurer les paramètres de langue
    print '<h3>1. Configuration de langue</h3>';
    
    $current_lang = getDolGlobalString('MAIN_LANG_DEFAULT');
    if (empty($current_lang)) {
        // Définir la langue par défaut
        $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) VALUES ('MAIN_LANG_DEFAULT', 'fr_FR', 'chaine', 'Default language', '0', '1') ON DUPLICATE KEY UPDATE value = 'fr_FR'";
        $db->query($sql);
        print "Langue par défaut définie sur fr_FR<br>";
    } else {
        print "Langue par défaut actuelle: {$current_lang}<br>";
    }
    
    // 2. Configurer les paramètres d'affichage
    print '<h3>2. Configuration d\'affichage</h3>';
    
    $display_params = array(
        'MAIN_DISABLE_JAVASCRIPT' => '0',
        'MAIN_OPTIMIZE_SPEED' => '1',
        'MAIN_USE_ADVANCED_PERMS' => '0'
    );
    
    foreach ($display_params as $param => $value) {
        $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) VALUES ('{$param}', '{$value}', 'chaine', 'Auto-configured', '0', '1') ON DUPLICATE KEY UPDATE value = '{$value}'";
        $db->query($sql);
        print "Paramètre {$param} configuré<br>";
    }
    
    // 3. Créer des triggers de base de données pour prévenir les valeurs vides
    print '<h3>3. Création de triggers de prévention</h3>';
    
    // Trigger pour les produits
    $sql = "CREATE TRIGGER IF NOT EXISTS prevent_empty_product_label 
            BEFORE INSERT ON " . MAIN_DB_PREFIX . "product 
            FOR EACH ROW 
            BEGIN 
                IF NEW.label = '' OR NEW.label IS NULL OR NEW.label LIKE '%text to show%' THEN 
                    SET NEW.label = CONCAT('Produit ', NEW.ref); 
                END IF; 
            END";
    $result = $db->query($sql);
    if ($result) {
        print "Trigger de prévention pour les produits créé<br>";
    } else {
        print "Erreur trigger produits: " . $db->lasterror() . "<br>";
    }
    
    // Trigger pour les sociétés
    $sql = "CREATE TRIGGER IF NOT EXISTS prevent_empty_societe_nom 
            BEFORE INSERT ON " . MAIN_DB_PREFIX . "societe 
            FOR EACH ROW 
            BEGIN 
                IF NEW.nom = '' OR NEW.nom IS NULL OR NEW.nom LIKE '%text to show%' THEN 
                    SET NEW.nom = CONCAT('Société ', COALESCE(NEW.code_client, 'AUTO')); 
                END IF; 
            END";
    $result = $db->query($sql);
    if ($result) {
        print "Trigger de prévention pour les sociétés créé<br>";
    } else {
        print "Erreur trigger sociétés: " . $db->lasterror() . "<br>";
    }
    
    // 4. Créer une tâche de maintenance automatique
    print '<h3>4. Configuration de maintenance automatique</h3>';
    
    // Créer un fichier de maintenance
    $maintenance_script = '<?php
// Script de maintenance automatique - A text to show
require_once "main.inc.php";

// Nettoyer les traductions corrompues
$tables = array("overwrite_trans", "c_translations");
foreach ($tables as $table) {
    $sql = "DELETE FROM " . MAIN_DB_PREFIX . $table . " WHERE transvalue LIKE \'%text to show%\'";
    $db->query($sql);
}

// Corriger les libellés vides
$sql = "UPDATE " . MAIN_DB_PREFIX . "product SET label = CONCAT(\'Produit \', ref) WHERE label = \'\' OR label IS NULL OR label LIKE \'%text to show%\'";
$db->query($sql);

$sql = "UPDATE " . MAIN_DB_PREFIX . "societe SET nom = CONCAT(\'Société \', COALESCE(code_client, rowid)) WHERE nom = \'\' OR nom IS NULL OR nom LIKE \'%text to show%\'";
$db->query($sql);

echo "Maintenance automatique exécutée le " . date("Y-m-d H:i:s") . "\n";
?>';
    
    file_put_contents('maintenance_text_to_show.php', $maintenance_script);
    print "Script de maintenance automatique créé<br>";
    
    // 5. Recommandations finales
    print '<h3>5. Recommandations</h3>';
    print '<ul>';
    print '<li>Exécutez le script maintenance_text_to_show.php une fois par semaine via cron</li>';
    print '<li>Vérifiez régulièrement les logs d\'erreur de Dolibarr</li>';
    print '<li>Assurez-vous que tous les modules sont à jour</li>';
    print '<li>Évitez de laisser des champs obligatoires vides lors de la création d\'enregistrements</li>';
    print '</ul>';
    
    print '<p><strong style="color: green;">Configuration de prévention terminée !</strong></p>';
    
} else {
    // Menu principal
    print '<h2>Configuration de prévention</h2>';
    print '<p>Ce script configure des mesures préventives pour éviter le retour du problème "A text to show".</p>';
    
    print '<h3>Actions qui seront effectuées :</h3>';
    print '<ul>';
    print '<li>Configuration des paramètres de langue par défaut</li>';
    print '<li>Optimisation des paramètres d\'affichage</li>';
    print '<li>Création de triggers de base de données pour prévenir les valeurs vides</li>';
    print '<li>Création d\'un script de maintenance automatique</li>';
    print '</ul>';
    
    print '<p><a href="?action=setup" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Configurer la prévention</a></p>';
    
    print '<h3>Causes communes du problème "A text to show"</h3>';
    print '<ul>';
    print '<li><strong>Traductions corrompues :</strong> Fichiers de langue endommagés ou mal configurés</li>';
    print '<li><strong>Champs vides :</strong> Enregistrements créés sans libellés appropriés</li>';
    print '<li><strong>Cache corrompu :</strong> Cache de traductions contenant des données invalides</li>';
    print '<li><strong>Modules tiers :</strong> Modules mal développés qui n\'utilisent pas correctement les traductions</li>';
    print '<li><strong>Import de données :</strong> Données importées sans validation appropriée</li>';
    print '</ul>';
}

print '</body></html>';
?>
