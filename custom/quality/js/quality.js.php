<?php
/* Module Quality - JavaScript
 * Copyright (C) 2025 Sinedtyi
 */

// Predefined constants
if (!defined('NOREQUIREDB')) {
    define('NOREQUIREDB', '1');
}
if (!defined('NOREQUIREUSER')) {
    define('NOREQUIREUSER', '1');
}
if (!defined('NOREQUIRESOC')) {
    define('NOREQUIRESOC', '1');
}
if (!defined('NOREQUIRETRAN')) {
    define('NOREQUIRETRAN', '1');
}
if (!defined('NOCSRFCHECK')) {
    define('NOCSRFCHECK', 1);
}
if (!defined('NOTOKENRENEWAL')) {
    define('NOTOKENRENEWAL', 1);
}
if (!defined('NOLOGIN')) {
    define('NOLOGIN', 1);
}
if (!defined('NOREQUIREMENU')) {
    define('NOREQUIREMENU', 1);
}
if (!defined('NOREQUIREHTML')) {
    define('NOREQUIREHTML', 1);
}
if (!defined('NOREQUIREAJAX')) {
    define('NOREQ<PERSON>REA<PERSON>AX', '1');
}

session_cache_limiter('public');

// Load Dolibarr environment
$res = @include("../../../main.inc.php"); // From htdocs directory
if (! $res) {
    $res = @include("../../../../main.inc.php"); // From "custom" directory
}

// Define js type
header('Content-type: application/javascript');
// Important: Following code is to cache this file to avoid page request by browser at each Dolibarr page access.
// You can use CTRL+F5 to refresh your browser cache.
if (empty($dolibarr_nocache)) {
    header('Cache-Control: max-age=3600, public, must-revalidate');
} else {
    header('Cache-Control: no-cache');
}

if (is_object($langs)) {
    $langs->load('quality@quality');
}

?>
function qualityDefineMotif(fk_object,type_object,qty) {

	$div = $('<div />');

	$div.load("<?php echo dol_buildpath('/quality/script/interface.php',1) ?>?get=quality_definition&qty="+qty+"&fk_object="+fk_object+"&type_object="+type_object,function() {
		$div.dialog({
			title:"<?php echo $langs->transnoentities('DefineQualityUsage'); ?>"
			,modal:true
			,width:'50%'
		});

		$div.find('form').submit(function() {

			$.post($(this).attr('action'), $(this).serialize(), function() {

			});

			$div.dialog('close');

			return false;


		});

	});





}
