<?php
/**
 * Tests automatisés du module Plan Formation
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) {
	$res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
}
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) {
	$i--; $j--;
}
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) {
	$res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
}
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) {
	$res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
}
// Try main.inc.php using relative path
if (!$res && file_exists("../../main.inc.php")) {
	$res = @include "../../main.inc.php";
}
if (!$res && file_exists("../../../main.inc.php")) {
	$res = @include "../../../main.inc.php";
}
if (!$res) {
	die("Include of main fails");
}

// Security check
if (!$user->admin) {
    accessforbidden();
}

$langs->load('planformation@planformation');
$langs->load('admin');

$action = GETPOST('action', 'alpha');

$title = $langs->trans('PFModuleTests');
llxHeader('', $title);

$linkback = '<a href="' . DOL_URL_ROOT . '/admin/modules.php?restore_lastsearch_values=1">' . $langs->trans("BackToModuleList") . '</a>';
print load_fiche_titre($title, $linkback, 'planformation@planformation');

print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th>' . $langs->trans('Test') . '</th>';
print '<th>' . $langs->trans('Description') . '</th>';
print '<th class="center">' . $langs->trans('Status') . '</th>';
print '<th class="center">' . $langs->trans('Result') . '</th>';
print '</tr>';

// Test 1: Vérification de la base de données
print '<tr class="oddeven">';
print '<td><strong>' . $langs->trans('PFDatabaseTest') . '</strong></td>';
print '<td>' . $langs->trans('PFDatabaseTestDesc') . '</td>';
print '<td class="center">';

$db_test_result = true;
$db_test_message = '';

// Vérifier les tables principales
$tables_to_check = array(
    'planform',
    'planform_session',
    'planform_formation'
);

foreach ($tables_to_check as $table) {
    $sql = "SHOW TABLES LIKE '" . MAIN_DB_PREFIX . $table . "'";
    $resql = $db->query($sql);
    if (!$resql || $db->num_rows($resql) == 0) {
        $db_test_result = false;
        $db_test_message .= $langs->trans('PFTableMissing', $table) . ' ';
    }
}

if ($db_test_result) {
    print '<span class="badge badge-status4"><i class="fas fa-check"></i> OK</span>';
} else {
    print '<span class="badge badge-status8"><i class="fas fa-times"></i> KO</span>';
}
print '</td>';
print '<td class="center">';
if ($db_test_result) {
    print $langs->trans('PFAllTablesPresent');
} else {
    print $db_test_message;
}
print '</td>';
print '</tr>';

// Test 2: Vérification des permissions
print '<tr class="oddeven">';
print '<td><strong>' . $langs->trans('PFPermissionsTest') . '</strong></td>';
print '<td>' . $langs->trans('PFPermissionsTestDesc') . '</td>';
print '<td class="center">';

$perm_test_result = true;
$perm_test_message = '';

// Vérifier les permissions de base
if (!$user->hasRight('planformation', 'read')) {
    $perm_test_result = false;
    $perm_test_message .= $langs->trans('PFMissingReadPermission') . ' ';
}

if ($perm_test_result) {
    print '<span class="badge badge-status4"><i class="fas fa-check"></i> OK</span>';
} else {
    print '<span class="badge badge-status8"><i class="fas fa-times"></i> KO</span>';
}
print '</td>';
print '<td class="center">';
if ($perm_test_result) {
    print $langs->trans('PFPermissionsOK');
} else {
    print $perm_test_message;
}
print '</td>';
print '</tr>';

// Test 3: Vérification des fichiers
print '<tr class="oddeven">';
print '<td><strong>' . $langs->trans('PFFilesTest') . '</strong></td>';
print '<td>' . $langs->trans('PFFilesTestDesc') . '</td>';
print '<td class="center">';

$files_test_result = true;
$files_test_message = '';

// Vérifier les fichiers principaux
$files_to_check = array(
    DOL_DOCUMENT_ROOT . '/custom/planformation/class/planformation.class.php',
    DOL_DOCUMENT_ROOT . '/custom/planformation/class/sessionformation.class.php',
    DOL_DOCUMENT_ROOT . '/custom/planformation/class/formation.class.php'
);

foreach ($files_to_check as $file) {
    if (!file_exists($file)) {
        $files_test_result = false;
        $files_test_message .= $langs->trans('PFFileMissing', basename($file)) . ' ';
    }
}

if ($files_test_result) {
    print '<span class="badge badge-status4"><i class="fas fa-check"></i> OK</span>';
} else {
    print '<span class="badge badge-status8"><i class="fas fa-times"></i> KO</span>';
}
print '</td>';
print '<td class="center">';
if ($files_test_result) {
    print $langs->trans('PFAllFilesPresent');
} else {
    print $files_test_message;
}
print '</td>';
print '</tr>';

// Test 4: Vérification des intégrations
print '<tr class="oddeven">';
print '<td><strong>' . $langs->trans('PFIntegrationsTest') . '</strong></td>';
print '<td>' . $langs->trans('PFIntegrationsTestDesc') . '</td>';
print '<td class="center">';

$integrations_test_result = true;
$integrations_test_message = '';

// Vérifier les modules d'intégration
$integrations_status = array();
$integrations_status['ticket'] = isModEnabled('ticket');
$integrations_status['supplierassessment'] = isModEnabled('supplierassessment');

$enabled_integrations = array_filter($integrations_status);
if (count($enabled_integrations) > 0) {
    print '<span class="badge badge-status4"><i class="fas fa-check"></i> OK</span>';
    $integrations_test_message = count($enabled_integrations) . ' ' . $langs->trans('PFIntegrationsEnabled');
} else {
    print '<span class="badge badge-status1"><i class="fas fa-info"></i> INFO</span>';
    $integrations_test_message = $langs->trans('PFNoIntegrationsEnabled');
}

print '</td>';
print '<td class="center">';
print $integrations_test_message;
print '</td>';
print '</tr>';

print '</table>';
print '</div>';

// Résumé des tests
print '<br>';
print '<div class="info">';
print '<div class="center">';
print '<h3>' . $langs->trans('PFTestSummary') . '</h3>';

$total_tests = 4;
$passed_tests = 0;
if ($db_test_result) $passed_tests++;
if ($perm_test_result) $passed_tests++;
if ($files_test_result) $passed_tests++;
if ($integrations_test_result) $passed_tests++;

if ($passed_tests == $total_tests) {
    print '<span class="badge badge-status4" style="font-size: 1.2em; padding: 10px 20px;">';
    print '<i class="fas fa-check-circle"></i> ' . $langs->trans('PFAllTestsPassed', $passed_tests, $total_tests);
    print '</span>';
} else {
    print '<span class="badge badge-status8" style="font-size: 1.2em; padding: 10px 20px;">';
    print '<i class="fas fa-exclamation-triangle"></i> ' . $langs->trans('PFSomeTestsFailed', $passed_tests, $total_tests);
    print '</span>';
}

print '</div>';
print '</div>';

// Actions recommandées
if ($passed_tests < $total_tests) {
    print '<br>';
    print '<div class="warning">';
    print '<h4>' . $langs->trans('PFRecommendedActions') . '</h4>';
    print '<ul>';
    
    if (!$db_test_result) {
        print '<li>' . $langs->trans('PFCheckDatabaseStructure') . '</li>';
    }
    if (!$perm_test_result) {
        print '<li>' . $langs->trans('PFCheckPermissions') . '</li>';
    }
    if (!$files_test_result) {
        print '<li>' . $langs->trans('PFCheckFiles') . '</li>';
    }
    
    print '</ul>';
    print '</div>';
}

llxFooter();
?>
