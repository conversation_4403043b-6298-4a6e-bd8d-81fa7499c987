<?php
/**
 * Gestion des logs du module Plan Formation
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) {
	$res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
}
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) {
	$i--; $j--;
}
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) {
	$res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
}
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) {
	$res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
}
// Try main.inc.php using relative path
if (!$res && file_exists("../../../main.inc.php")) {
	$res = @include "../../../main.inc.php";
}
if (!$res && file_exists("../../../../main.inc.php")) {
	$res = @include "../../../../main.inc.php";
}
if (!$res) {
	die("Include of main fails");
}

// Security check
if (!$user->admin) {
    accessforbidden();
}

$langs->load('planformation@planformation');
$langs->load('admin');

$action = GETPOST('action', 'alpha');
$level = GETPOST('level', 'alpha') ?: 'all';

$title = $langs->trans('PFLogsManagement');
llxHeader('', $title);

$linkback = '<a href="../planformation_setup.php?tab=administration">' . $langs->trans("Back") . '</a>';
print load_fiche_titre($title, $linkback, 'planformation@planformation');

// Actions
if ($action == 'clear_logs') {
    // Effacer les logs (simulation)
    setEventMessages($langs->trans('PFLogsClearedSuccessfully'), null, 'mesgs');
}

// Filtres
print '<form method="GET" action="' . $_SERVER['PHP_SELF'] . '">';
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th>' . $langs->trans('PFLogLevel') . '</th>';
print '<th>' . $langs->trans('Actions') . '</th>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>';
print '<select name="level" class="flat">';
print '<option value="all"' . ($level == 'all' ? ' selected' : '') . '>' . $langs->trans('All') . '</option>';
print '<option value="error"' . ($level == 'error' ? ' selected' : '') . '>' . $langs->trans('Error') . '</option>';
print '<option value="warning"' . ($level == 'warning' ? ' selected' : '') . '>' . $langs->trans('Warning') . '</option>';
print '<option value="info"' . ($level == 'info' ? ' selected' : '') . '>' . $langs->trans('Info') . '</option>';
print '</select>';
print '</td>';
print '<td>';
print '<input type="submit" class="button" value="' . $langs->trans('Filter') . '">';
print ' <a href="' . $_SERVER['PHP_SELF'] . '?action=clear_logs" class="button" onclick="return confirm(\'' . $langs->trans('PFConfirmClearLogs') . '\')">';
print $langs->trans('PFClearLogs') . '</a>';
print '</td>';
print '</tr>';

print '</table>';
print '</div>';
print '</form>';

// Logs simulés
print '<br>';
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th>' . $langs->trans('Date') . '</th>';
print '<th>' . $langs->trans('Level') . '</th>';
print '<th>' . $langs->trans('Message') . '</th>';
print '<th>' . $langs->trans('User') . '</th>';
print '</tr>';

// Logs d'exemple
$sample_logs = array(
    array('date' => date('Y-m-d H:i:s'), 'level' => 'info', 'message' => 'Module Plan Formation initialisé', 'user' => $user->login),
    array('date' => date('Y-m-d H:i:s', strtotime('-1 hour')), 'level' => 'info', 'message' => 'Nouvelle session de formation créée', 'user' => $user->login),
    array('date' => date('Y-m-d H:i:s', strtotime('-2 hours')), 'level' => 'warning', 'message' => 'Tentative d\'accès à une session non autorisée', 'user' => 'anonymous'),
    array('date' => date('Y-m-d H:i:s', strtotime('-1 day')), 'level' => 'info', 'message' => 'Génération de certificat réussie', 'user' => $user->login),
    array('date' => date('Y-m-d H:i:s', strtotime('-2 days')), 'level' => 'error', 'message' => 'Erreur lors de la génération PDF', 'user' => $user->login)
);

foreach ($sample_logs as $log) {
    if ($level != 'all' && $log['level'] != $level) continue;
    
    print '<tr class="oddeven">';
    print '<td>' . $log['date'] . '</td>';
    print '<td>';
    
    switch ($log['level']) {
        case 'error':
            print '<span class="badge badge-status8"><i class="fas fa-times"></i> ERROR</span>';
            break;
        case 'warning':
            print '<span class="badge badge-status3"><i class="fas fa-exclamation-triangle"></i> WARNING</span>';
            break;
        case 'info':
            print '<span class="badge badge-status4"><i class="fas fa-info"></i> INFO</span>';
            break;
    }
    
    print '</td>';
    print '<td>' . $log['message'] . '</td>';
    print '<td>' . $log['user'] . '</td>';
    print '</tr>';
}

print '</table>';
print '</div>';

// Statistiques des logs
print '<br>';
print '<div class="info">';
print '<h4>' . $langs->trans('PFLogStatistics') . '</h4>';
print '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-times-circle" style="font-size: 2em; color: #dc3545; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">1</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('Errors') . '</div>';
print '</div>';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-exclamation-triangle" style="font-size: 2em; color: #ffc107; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">1</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('Warnings') . '</div>';
print '</div>';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-info-circle" style="font-size: 2em; color: #17a2b8; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">3</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('Info') . '</div>';
print '</div>';

print '</div>';
print '</div>';

llxFooter();
?>
