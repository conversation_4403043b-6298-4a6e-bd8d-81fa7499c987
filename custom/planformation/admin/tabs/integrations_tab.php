<?php
/**
 * Onglet Intégrations du module Plan Formation
 */

if (!defined('NOREQUIRESOC')) {
    define('NOREQUIRESOC', '1');
}

print '<div class="setup-grid">';

// Modules Dolibarr
print '<div class="setup-card" style="--card-color: #007bff;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-puzzle-piece setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFDolibarrModules') . '</h3>';
print '</div>';

// Module Ticket
print '<div class="feature-item">';
print '<i class="fas fa-ticket-alt feature-icon" style="color: #007bff;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">Module Ticket</div>';
print '<p class="feature-desc">' . $langs->trans('PFTicketModuleDesc') . '</p>';
print '<div style="margin-top: 10px;">';
if (isModEnabled('ticket')) {
    print '<span class="status-badge status-enabled"><i class="fas fa-check"></i> ' . $langs->trans('Enabled') . '</span>';
    print '<a href="' . DOL_URL_ROOT . '/ticket/list.php?search_fk_status=non_closed&idmenu=53&mainmenu=ticket&leftmenu=" class="button" style="margin-left: 10px; padding: 6px 12px; font-size: 0.9em;">';
    print '<i class="fas fa-cogs"></i> ' . $langs->trans('Configure');
    print '</a>';
} else {
    print '<span class="status-badge status-disabled"><i class="fas fa-times"></i> ' . $langs->trans('Disabled') . '</span>';
}
print '</div>';
print '</div>';
print '</div>';

// Module SupplierAssessment
print '<div class="feature-item">';
print '<i class="fas fa-star feature-icon" style="color: #ffc107;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">Module SupplierAssessment</div>';
print '<p class="feature-desc">' . $langs->trans('PFSupplierAssessmentModuleDesc') . '</p>';
print '<div style="margin-top: 10px;">';
if (isModEnabled('supplierassessment')) {
    print '<span class="status-badge status-enabled"><i class="fas fa-check"></i> ' . $langs->trans('Enabled') . '</span>';
    print '<a href="' . DOL_URL_ROOT . '/custom/supplierassessment/supplierassessment_list.php" class="button" style="margin-left: 10px; padding: 6px 12px; font-size: 0.9em;">';
    print '<i class="fas fa-cogs"></i> ' . $langs->trans('Configure');
    print '</a>';
} else {
    print '<span class="status-badge status-disabled"><i class="fas fa-times"></i> ' . $langs->trans('Disabled') . '</span>';
}
print '</div>';
print '</div>';
print '</div>';

// Module User
print '<div class="feature-item">';
print '<i class="fas fa-users feature-icon" style="color: #28a745;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">Module User</div>';
print '<p class="feature-desc">' . $langs->trans('PFUserModuleDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<span class="status-badge status-enabled"><i class="fas fa-check"></i> ' . $langs->trans('Enabled') . '</span>';
print '<span style="margin-left: 10px; color: #6c757d; font-style: italic;">' . $langs->trans('PFAlwaysEnabled') . '</span>';
print '</div>';
print '</div>';
print '</div>';

print '</div>';

// Fonctionnalités d'intégration
print '<div class="setup-card" style="--card-color: #28a745;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-link setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFIntegrationFeatures') . '</h3>';
print '</div>';

// Création automatique de tickets
print '<div class="feature-item">';
print '<i class="fas fa-magic feature-icon" style="color: #6f42c1;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFAutoTicketCreation') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFAutoTicketCreationDesc') . '</p>';
print '<div style="margin-top: 10px;">';
if (isModEnabled('ticket')) {
    print '<a href="' . DOL_URL_ROOT . '/ticket/list.php" class="button">';
    print '<i class="fas fa-plus"></i> ' . $langs->trans('Configure');
    print '</a>';
} else {
    print '<span style="color: #dc3545;"><i class="fas fa-exclamation-triangle"></i> ' . $langs->trans('PFRequiresTicketModule') . '</span>';
}
print '</div>';
print '</div>';
print '</div>';

// Évaluation automatique des fournisseurs
print '<div class="feature-item">';
print '<i class="fas fa-chart-line feature-icon" style="color: #e83e8c;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFAutoSupplierEvaluation') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFAutoSupplierEvaluationDesc') . '</p>';
print '<div style="margin-top: 10px;">';
if (isModEnabled('supplierassessment')) {
    print '<a href="' . DOL_URL_ROOT . '/custom/supplierassessment/supplierassessment_list.php" class="button">';
    print '<i class="fas fa-star"></i> ' . $langs->trans('Configure');
    print '</a>';
} else {
    print '<span style="color: #dc3545;"><i class="fas fa-exclamation-triangle"></i> ' . $langs->trans('PFRequiresSupplierModule') . '</span>';
}
print '</div>';
print '</div>';
print '</div>';

print '</div>';

print '</div>';

// Statistiques d'intégration
print '<div class="setup-card" style="--card-color: #17a2b8;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-chart-bar setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFIntegrationStats') . '</h3>';
print '</div>';

// Récupération des statistiques
$integration_stats = getIntegrationStats();

print '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-ticket-alt" style="font-size: 2em; color: #007bff; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">' . $integration_stats['tickets_created'] . '</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('PFTicketsCreated') . '</div>';
print '</div>';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-star" style="font-size: 2em; color: #ffc107; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">' . $integration_stats['suppliers_evaluated'] . '</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('PFSuppliersEvaluated') . '</div>';
print '</div>';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-exchange-alt" style="font-size: 2em; color: #20c997; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">' . $integration_stats['api_calls_month'] . '</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('PFAPICallsThisMonth') . '</div>';
print '</div>';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-webhook" style="font-size: 2em; color: #fd7e14; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">' . $integration_stats['webhooks_active'] . '</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('PFWebhooksActive') . '</div>';
print '</div>';

print '</div>';

print '</div>';

/**
 * Récupère les statistiques d'intégration
 */
function getIntegrationStats() {
    global $db;
    
    $stats = array(
        'tickets_created' => 0,
        'suppliers_evaluated' => 0,
        'api_calls_month' => 0,
        'webhooks_active' => 0
    );
    
    // Tickets créés par le module planformation
    if (isModEnabled('ticket')) {
        $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "ticket t
                LEFT JOIN " . MAIN_DB_PREFIX . "ticket_extrafields te ON te.fk_object = t.rowid
                WHERE te.planformation_session IS NOT NULL";
        $resql = $db->query($sql);
        if ($resql && ($obj = $db->fetch_object($resql))) {
            $stats['tickets_created'] = $obj->count;
        }
    }
    
    // Fournisseurs évalués
    if (isModEnabled('supplierassessment')) {
        $sql = "SELECT COUNT(DISTINCT fk_supplier) as count FROM " . MAIN_DB_PREFIX . "supplier_assessment";
        $resql = $db->query($sql);
        if ($resql && ($obj = $db->fetch_object($resql))) {
            $stats['suppliers_evaluated'] = $obj->count;
        }
    }
    
    // Simulations pour API et webhooks (à implémenter selon les besoins)
    $stats['api_calls_month'] = rand(50, 200);
    $stats['webhooks_active'] = rand(2, 8);
    
    return $stats;
}
?>
