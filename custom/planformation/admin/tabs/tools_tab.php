<?php
/**
 * Onglet Outils et Rapports du module Plan Formation
 */

if (!defined('NOREQUIRESOC')) {
    define('NOREQUIRESOC', '1');
}

print '<div class="setup-grid">';

// Outils d'impression
print '<div class="setup-card" style="--card-color: #007bff;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-print setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFPrintingTools') . '</h3>';
print '</div>';

// Feuilles de présence
print '<div class="feature-item">';
print '<i class="fas fa-clipboard-list feature-icon" style="color: #007bff;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFPrintAttendanceSheets') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFPrintAttendanceSheetsDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<a href="../session.php?action=list&print_mode=1" class="button">';
print '<i class="fas fa-print"></i> ' . $langs->trans('Access');
print '</a>';
print '</div>';
print '</div>';
print '</div>';

// Certificats
print '<div class="feature-item">';
print '<i class="fas fa-certificate feature-icon" style="color: #28a745;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFGenerateCertificates') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFGenerateCertificatesDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<a href="../session.php?action=list&cert_mode=1" class="button">';
print '<i class="fas fa-certificate"></i> ' . $langs->trans('Access');
print '</a>';
print '</div>';
print '</div>';
print '</div>';

print '</div>';

// Rapports et analyses
print '<div class="setup-card" style="--card-color: #28a745;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-chart-bar setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFReportsAnalytics') . '</h3>';
print '</div>';

// Dashboard
print '<div class="feature-item">';
print '<i class="fas fa-tachometer-alt feature-icon" style="color: #17a2b8;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFDashboard') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFDashboardDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<a href="dashboard.php" class="button">';
print '<i class="fas fa-chart-pie"></i> ' . $langs->trans('Access');
print '</a>';
print '</div>';
print '</div>';
print '</div>';

// Rapport de présence
print '<div class="feature-item">';
print '<i class="fas fa-user-check feature-icon" style="color: #ffc107;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFAttendanceReport') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFAttendanceReportDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<a href="reports/attendance_report.php" class="button">';
print '<i class="fas fa-file-alt"></i> ' . $langs->trans('Generate');
print '</a>';
print '</div>';
print '</div>';
print '</div>';

// Rapport budgétaire
print '<div class="feature-item">';
print '<i class="fas fa-euro-sign feature-icon" style="color: #dc3545;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFBudgetReport') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFBudgetReportDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<a href="reports/budget_report.php" class="button">';
print '<i class="fas fa-file-excel"></i> ' . $langs->trans('Generate');
print '</a>';
print '</div>';
print '</div>';
print '</div>';

print '</div>';

// Outils d'export
print '<div class="setup-card" style="--card-color: #6f42c1;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-download setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFExportTools') . '</h3>';
print '</div>';

// Export Excel
print '<div class="feature-item">';
print '<i class="fas fa-file-excel feature-icon" style="color: #28a745;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFExportExcel') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFExportExcelDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<a href="export/excel_export.php" class="button">';
print '<i class="fas fa-file-excel"></i> ' . $langs->trans('Export');
print '</a>';
print '</div>';
print '</div>';
print '</div>';

// Export PDF
print '<div class="feature-item">';
print '<i class="fas fa-file-pdf feature-icon" style="color: #dc3545;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFExportPDF') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFExportPDFDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<a href="export/pdf_export.php" class="button">';
print '<i class="fas fa-file-pdf"></i> ' . $langs->trans('Export');
print '</a>';
print '</div>';
print '</div>';
print '</div>';

print '</div>';

print '</div>';

// Statistiques rapides
print '<div class="setup-card" style="--card-color: #17a2b8;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-info-circle setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFQuickStats') . '</h3>';
print '</div>';

// Récupération des statistiques rapides
$stats = getQuickStats();

print '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-clipboard-list" style="font-size: 2em; color: #007bff; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">' . $stats['active_plans'] . '</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('PFTotalActivePlans') . '</div>';
print '</div>';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-chalkboard-teacher" style="font-size: 2em; color: #28a745; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">' . $stats['sessions_this_month'] . '</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('PFSessionsThisMonth') . '</div>';
print '</div>';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-users" style="font-size: 2em; color: #ffc107; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">' . $stats['participants_this_month'] . '</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('PFParticipantsThisMonth') . '</div>';
print '</div>';

print '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px;">';
print '<i class="fas fa-euro-sign" style="font-size: 2em; color: #dc3545; margin-bottom: 10px;"></i>';
print '<div style="font-size: 1.5em; font-weight: bold; color: #2c3e50;">' . price($stats['budget_consumed_year']) . '</div>';
print '<div style="color: #6c757d; font-size: 0.9em;">' . $langs->trans('PFBudgetConsumedThisYear') . '</div>';
print '</div>';

print '</div>';

print '</div>';

/**
 * Récupère les statistiques rapides
 */
function getQuickStats() {
    global $db;
    
    $stats = array();
    
    // Plans actifs
    $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "planform WHERE statut = 1";
    $resql = $db->query($sql);
    $stats['active_plans'] = ($resql && ($obj = $db->fetch_object($resql))) ? $obj->count : 0;
    
    // Sessions ce mois
    $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "planform_session 
            WHERE MONTH(date_debut) = MONTH(NOW()) AND YEAR(date_debut) = YEAR(NOW())";
    $resql = $db->query($sql);
    $stats['sessions_this_month'] = ($resql && ($obj = $db->fetch_object($resql))) ? $obj->count : 0;
    
    // Participants ce mois
    $sql = "SELECT COUNT(DISTINCT sp.fk_user) as count 
            FROM " . MAIN_DB_PREFIX . "planform_session_participant sp
            INNER JOIN " . MAIN_DB_PREFIX . "planform_session s ON s.rowid = sp.fk_session
            WHERE MONTH(s.date_debut) = MONTH(NOW()) AND YEAR(s.date_debut) = YEAR(NOW())";
    $resql = $db->query($sql);
    $stats['participants_this_month'] = ($resql && ($obj = $db->fetch_object($resql))) ? $obj->count : 0;
    
    // Budget consommé cette année
    $sql = "SELECT SUM(budget_consomme) as total FROM " . MAIN_DB_PREFIX . "planform 
            WHERE YEAR(date_start) = YEAR(NOW())";
    $resql = $db->query($sql);
    $stats['budget_consumed_year'] = ($resql && ($obj = $db->fetch_object($resql))) ? ($obj->total ?: 0) : 0;
    
    return $stats;
}
?>
