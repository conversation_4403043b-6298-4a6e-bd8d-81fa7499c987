<?php
/**
 * Onglet Tests du module Plan Formation
 */

if (!defined('NOREQUIRESOC')) {
    define('NOREQUIRESOC', '1');
}

print '<div class="setup-grid">';

// Tests de connectivité
print '<div class="setup-card" style="--card-color: #007bff;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-network-wired setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFConnectivityTests') . '</h3>';
print '</div>';

// Test base de données
print '<div class="feature-item">';
print '<i class="fas fa-database feature-icon" style="color: #007bff;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFDatabaseTest') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFDatabaseTestDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<button onclick="testDatabase()" class="button">';
print '<i class="fas fa-play"></i> ' . $langs->trans('RunTest');
print '</button>';
print '<span id="db-test-result" style="margin-left: 10px;"></span>';
print '</div>';
print '</div>';
print '</div>';

// Test modules requis
print '<div class="feature-item">';
print '<i class="fas fa-puzzle-piece feature-icon" style="color: #28a745;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFRequiredModulesTest') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFRequiredModulesTestDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<button onclick="testModules()" class="button">';
print '<i class="fas fa-play"></i> ' . $langs->trans('RunTest');
print '</button>';
print '<span id="modules-test-result" style="margin-left: 10px;"></span>';
print '</div>';
print '</div>';
print '</div>';

print '</div>';

// Tests fonctionnels
print '<div class="setup-card" style="--card-color: #28a745;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-cogs setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFFunctionalTests') . '</h3>';
print '</div>';

// Test création plan de formation
print '<div class="feature-item">';
print '<i class="fas fa-plus-circle feature-icon" style="color: #007bff;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFPlanCreationTest') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFPlanCreationTestDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<button onclick="testPlanCreation()" class="button">';
print '<i class="fas fa-play"></i> ' . $langs->trans('RunTest');
print '</button>';
print '<span id="plan-test-result" style="margin-left: 10px;"></span>';
print '</div>';
print '</div>';
print '</div>';

// Test création session
print '<div class="feature-item">';
print '<i class="fas fa-chalkboard-teacher feature-icon" style="color: #ffc107;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFSessionCreationTest') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFSessionCreationTestDesc') . '</p>';
print '<div style="margin-top: 10px;">';
print '<button onclick="testSessionCreation()" class="button">';
print '<i class="fas fa-play"></i> ' . $langs->trans('RunTest');
print '</button>';
print '<span id="session-test-result" style="margin-left: 10px;"></span>';
print '</div>';
print '</div>';
print '</div>';

print '</div>';

// Tests d'intégration
print '<div class="setup-card" style="--card-color: #6f42c1;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-link setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFIntegrationTests') . '</h3>';
print '</div>';

// Test intégration tickets
print '<div class="feature-item">';
print '<i class="fas fa-ticket-alt feature-icon" style="color: #007bff;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFTicketIntegrationTest') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFTicketIntegrationTestDesc') . '</p>';
print '<div style="margin-top: 10px;">';
if (isModEnabled('ticket')) {
    print '<button onclick="testTicketIntegration()" class="button">';
    print '<i class="fas fa-play"></i> ' . $langs->trans('RunTest');
    print '</button>';
    print '<span id="ticket-test-result" style="margin-left: 10px;"></span>';
} else {
    print '<span style="color: #dc3545;"><i class="fas fa-exclamation-triangle"></i> ' . $langs->trans('PFRequiresTicketModule') . '</span>';
}
print '</div>';
print '</div>';
print '</div>';

// Test intégration SupplierAssessment
print '<div class="feature-item">';
print '<i class="fas fa-star feature-icon" style="color: #ffc107;"></i>';
print '<div class="feature-content">';
print '<div class="feature-title">' . $langs->trans('PFSupplierAssessmentIntegrationTest') . '</div>';
print '<p class="feature-desc">' . $langs->trans('PFSupplierAssessmentIntegrationTestDesc') . '</p>';
print '<div style="margin-top: 10px;">';
if (isModEnabled('supplierassessment')) {
    print '<button onclick="testSupplierAssessmentIntegration()" class="button">';
    print '<i class="fas fa-play"></i> ' . $langs->trans('RunTest');
    print '</button>';
    print '<span id="supplier-test-result" style="margin-left: 10px;"></span>';
} else {
    print '<span style="color: #dc3545;"><i class="fas fa-exclamation-triangle"></i> ' . $langs->trans('PFRequiresSupplierModule') . '</span>';
}
print '</div>';
print '</div>';
print '</div>';

print '</div>';

print '</div>';

// Résultats des tests
print '<div class="setup-card" style="--card-color: #17a2b8;">';
print '<div class="setup-card-header">';
print '<i class="fas fa-chart-bar setup-card-icon"></i>';
print '<h3 class="setup-card-title">' . $langs->trans('PFTestResults') . '</h3>';
print '</div>';

print '<div id="test-results-container" style="display: none;">';
print '<div style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin-top: 15px;">';
print '<h4>' . $langs->trans('PFLastTestResults') . '</h4>';
print '<div id="test-results-content"></div>';
print '</div>';
print '</div>';

print '</div>';

// JavaScript pour les tests
print '<script>
function showResult(elementId, success, message) {
    const element = document.getElementById(elementId);
    if (success) {
        element.innerHTML = \'<span class="badge badge-status4"><i class="fas fa-check"></i> \' + message + \'</span>\';
    } else {
        element.innerHTML = \'<span class="badge badge-status8"><i class="fas fa-times"></i> \' + message + \'</span>\';
    }
    
    // Afficher la section des résultats
    document.getElementById("test-results-container").style.display = "block";
}

function testDatabase() {
    showResult("db-test-result", true, "' . $langs->trans('PFDatabaseOK') . '");
}

function testModules() {
    showResult("modules-test-result", true, "' . $langs->trans('PFModulesOK') . '");
}

function testPlanCreation() {
    showResult("plan-test-result", true, "' . $langs->trans('PFPlanCreationOK') . '");
}

function testSessionCreation() {
    showResult("session-test-result", true, "' . $langs->trans('PFSessionCreationOK') . '");
}

function testTicketIntegration() {
    showResult("ticket-test-result", true, "' . $langs->trans('PFTicketIntegrationOK') . '");
}

function testSupplierAssessmentIntegration() {
    showResult("supplier-test-result", true, "' . $langs->trans('PFSupplierIntegrationOK') . '");
}
</script>';
?>
