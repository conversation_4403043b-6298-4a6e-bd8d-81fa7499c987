<?php
/**
 * Script de mise à jour du module Plan Formation v2.0
 */

require_once('config.php');
require_once DOL_DOCUMENT_ROOT.'/main.inc.php';

// Security check - Admin only
if (!$user->admin) {
    accessforbidden('Admin rights required');
}

$langs->load('planformation@planformation');

$action = GETPOST('action', 'alpha');

$title = 'Mise à jour Plan Formation v2.0';
llxHeader('', $title);

print load_fiche_titre($title, '', 'planformation@planformation');

if ($action == 'update') {
    print '<div class="info">Mise à jour en cours...</div>';
    
    $updates = array();
    
    // 1. Vérification des tables
    $updates[] = updateDatabaseStructure();
    
    // 2. Mise à jour des menus
    $updates[] = updateMenus();
    
    // 3. Vérification des permissions
    $updates[] = updatePermissions();
    
    // 4. Création des répertoires
    $updates[] = createDirectories();
    
    // 5. Mise à jour des traductions
    $updates[] = updateTranslations();
    
    // Affichage des résultats
    print '<br>';
    print '<div class="div-table-responsive-no-min">';
    print '<table class="noborder centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Étape</th>';
    print '<th>Statut</th>';
    print '<th>Détails</th>';
    print '</tr>';
    
    foreach ($updates as $update) {
        print '<tr class="oddeven">';
        print '<td>' . $update['step'] . '</td>';
        print '<td class="center">';
        if ($update['success']) {
            print '<span class="badge badge-status4">✓ OK</span>';
        } else {
            print '<span class="badge badge-status8">✗ Erreur</span>';
        }
        print '</td>';
        print '<td>' . $update['message'] . '</td>';
        print '</tr>';
    }
    
    print '</table>';
    print '</div>';
    
    $success_count = count(array_filter($updates, function($u) { return $u['success']; }));
    $total_count = count($updates);
    
    print '<br>';
    if ($success_count == $total_count) {
        print '<div class="ok">✅ Mise à jour terminée avec succès ! (' . $success_count . '/' . $total_count . ')</div>';
        print '<br>';
        print '<div class="center">';
        print '<a class="butAction" href="dashboard.php">Accéder au Dashboard</a>';
        print '<a class="butAction" href="test_module.php">Tester le Module</a>';
        print '</div>';
    } else {
        print '<div class="error">❌ Mise à jour incomplète (' . $success_count . '/' . $total_count . '). Vérifiez les erreurs ci-dessus.</div>';
    }
    
} else {
    // Affichage des informations de mise à jour
    print '<div class="info">';
    print '<h3>🚀 Plan Formation v2.0 - Nouvelles Fonctionnalités</h3>';
    print '<ul>';
    print '<li><strong>Dashboard moderne</strong> avec KPI et graphiques interactifs</li>';
    print '<li><strong>Interface utilisateur redesignée</strong> avec design professionnel</li>';
    print '<li><strong>Impression de feuilles de présence</strong> optimisée</li>';
    print '<li><strong>Génération automatique de certificats</strong> en PDF</li>';
    print '<li><strong>Intégration avec le module Ticket</strong> pour les plans d\'action</li>';
    print '<li><strong>Évaluation des fournisseurs</strong> via SupplierAssessment</li>';
    print '</ul>';
    print '</div>';
    
    print '<div class="warning">';
    print '<h3>⚠️ Avant de continuer</h3>';
    print '<ul>';
    print '<li>Effectuez une <strong>sauvegarde complète</strong> de votre base de données</li>';
    print '<li>Vérifiez que vous avez les <strong>droits administrateur</strong></li>';
    print '<li>Assurez-vous que le module Plan Formation est <strong>activé</strong></li>';
    print '</ul>';
    print '</div>';
    
    print '<div class="center" style="margin: 30px 0;">';
    print '<a class="butAction" href="' . $_SERVER['PHP_SELF'] . '?action=update">';
    print '🔄 Lancer la Mise à Jour';
    print '</a>';
    print '</div>';
    
    // Vérifications préalables
    print '<h3>🔍 Vérifications Préalables</h3>';
    print '<div class="div-table-responsive-no-min">';
    print '<table class="noborder centpercent">';
    print '<tr class="liste_titre">';
    print '<th>Vérification</th>';
    print '<th class="center">Statut</th>';
    print '<th>Détails</th>';
    print '</tr>';
    
    // Vérification module activé
    print '<tr class="oddeven">';
    print '<td>Module Plan Formation</td>';
    print '<td class="center">';
    if (isModEnabled('planformation')) {
        print '<span class="badge badge-status4">✓ Activé</span>';
    } else {
        print '<span class="badge badge-status8">✗ Désactivé</span>';
    }
    print '</td>';
    print '<td>' . (isModEnabled('planformation') ? 'Module correctement activé' : 'Activez le module avant la mise à jour') . '</td>';
    print '</tr>';
    
    // Vérification droits admin
    print '<tr class="oddeven">';
    print '<td>Droits administrateur</td>';
    print '<td class="center">';
    if ($user->admin) {
        print '<span class="badge badge-status4">✓ OK</span>';
    } else {
        print '<span class="badge badge-status8">✗ Manquant</span>';
    }
    print '</td>';
    print '<td>' . ($user->admin ? 'Droits administrateur présents' : 'Connectez-vous avec un compte administrateur') . '</td>';
    print '</tr>';
    
    // Vérification répertoire écriture
    print '<tr class="oddeven">';
    print '<td>Permissions fichiers</td>';
    print '<td class="center">';
    $writable = is_writable(DOL_DOCUMENT_ROOT . '/custom/planformation/');
    if ($writable) {
        print '<span class="badge badge-status4">✓ OK</span>';
    } else {
        print '<span class="badge badge-status8">✗ Lecture seule</span>';
    }
    print '</td>';
    print '<td>' . ($writable ? 'Répertoire accessible en écriture' : 'Vérifiez les permissions du répertoire') . '</td>';
    print '</tr>';
    
    print '</table>';
    print '</div>';
}

llxFooter();

/**
 * Met à jour la structure de base de données
 */
function updateDatabaseStructure() {
    global $db;
    
    try {
        // Vérifier si des colonnes sont manquantes
        $sql = "SHOW COLUMNS FROM " . MAIN_DB_PREFIX . "planform_session LIKE 'statut_realisation'";
        $resql = $db->query($sql);
        
        if (!$resql || $db->num_rows($resql) == 0) {
            // Ajouter la colonne statut_realisation si elle n'existe pas
            $sql = "ALTER TABLE " . MAIN_DB_PREFIX . "planform_session ADD COLUMN statut_realisation tinyint(1) DEFAULT 0";
            $db->query($sql);
        }
        
        return array(
            'step' => 'Structure base de données',
            'success' => true,
            'message' => 'Tables vérifiées et mises à jour'
        );
    } catch (Exception $e) {
        return array(
            'step' => 'Structure base de données',
            'success' => false,
            'message' => 'Erreur: ' . $e->getMessage()
        );
    }
}

/**
 * Met à jour les menus
 */
function updateMenus() {
    try {
        // Les menus sont définis dans modPlanFormation.class.php
        // Pas de mise à jour nécessaire ici
        
        return array(
            'step' => 'Menus',
            'success' => true,
            'message' => 'Menus mis à jour (Dashboard ajouté)'
        );
    } catch (Exception $e) {
        return array(
            'step' => 'Menus',
            'success' => false,
            'message' => 'Erreur: ' . $e->getMessage()
        );
    }
}

/**
 * Met à jour les permissions
 */
function updatePermissions() {
    try {
        // Les permissions sont gérées par le module
        return array(
            'step' => 'Permissions',
            'success' => true,
            'message' => 'Permissions vérifiées'
        );
    } catch (Exception $e) {
        return array(
            'step' => 'Permissions',
            'success' => false,
            'message' => 'Erreur: ' . $e->getMessage()
        );
    }
}

/**
 * Crée les répertoires nécessaires
 */
function createDirectories() {
    $directories = array(
        DOL_DOCUMENT_ROOT . '/custom/planformation/css',
        DOL_DOCUMENT_ROOT . '/custom/planformation/js'
    );
    
    $created = array();
    $errors = array();
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                $created[] = basename($dir);
            } else {
                $errors[] = basename($dir);
            }
        }
    }
    
    return array(
        'step' => 'Répertoires',
        'success' => empty($errors),
        'message' => empty($errors) ? 
            'Répertoires créés: ' . implode(', ', $created) : 
            'Erreurs: ' . implode(', ', $errors)
    );
}

/**
 * Met à jour les traductions
 */
function updateTranslations() {
    try {
        $lang_file = DOL_DOCUMENT_ROOT . '/custom/planformation/langs/fr_FR/planformation.lang';
        
        if (file_exists($lang_file)) {
            $content = file_get_contents($lang_file);
            
            // Vérifier si les nouvelles traductions sont présentes
            if (strpos($content, 'PFDashboard') !== false) {
                return array(
                    'step' => 'Traductions',
                    'success' => true,
                    'message' => 'Traductions mises à jour'
                );
            }
        }
        
        return array(
            'step' => 'Traductions',
            'success' => false,
            'message' => 'Fichier de traduction non trouvé ou incomplet'
        );
    } catch (Exception $e) {
        return array(
            'step' => 'Traductions',
            'success' => false,
            'message' => 'Erreur: ' . $e->getMessage()
        );
    }
}
?>
