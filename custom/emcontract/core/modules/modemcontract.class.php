<?php
/* Copyright (C) 2013 <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 * or see http://www.gnu.org/
 */

/**
 *    \defgroup   employment_contract
 *    \brief      Module de suivi des contrats de travail
 *    \file       htdocs/includes/modules/modEmploymentcontract.class.php
 *    \ingroup    employment_contract
 *    \brief      Description and activation file for module employment contract
 */
include_once(DOL_DOCUMENT_ROOT . "/core/modules/DolibarrModules.class.php");

/**
 *    Description and activation class for module employment contract
 */
class modemcontract extends DolibarrModules
{
    /**
     * Constructor
     */
    public function __construct($db)
    {
        global $conf; // 🔴 Obligatoire pour accéder à $conf

        $this->db = $db;
        $this->numero = 20600;
        $this->rights_class = 'emcontract';
        $this->family = "hr";
        $this->name = preg_replace('/^mod/i', '', get_class($this));
        $this->description = "Module to manage employment contract";
        $this->version = '1.0.0';
        $this->revision = '1.1.0';
        $this->const_name = 'MAIN_MODULE_' . strtoupper($this->name);
        $this->special = 0;
        $this->picto = 'list';
        $this->triggers = 0;

        // Définir les dossiers
        $this->dirs = array(
            '/emcontract/temp',
            '/emcontract/contrat',
            '/emcontract/template',
            '/emcontract/template/temp'
        );

        $this->depends = array("modSociete");
        $this->requiredby = array();
        $this->phpmin = array(5, 0);
        $this->need_dolibarr_version = array(3, 4);
        $this->langfiles = array("emcontract@emcontract");

        // 🔧 Constantes du module (une seule déclaration)
        $this->const = array(
            // Paramètres de numérotation
            array('EMCONTRACT_ADDON', 'chaine', 'mod_emcontract_standard', 'Name of numbering numerotation rules of contract', 0, 'current', 0),
            array('EMCONTRACT_ADDON_NUMBER_PATTERN', 'chaine', 'CONT{yy}{mm}-{0000}', 'Pattern used to generate contract reference', 0, 'current', 0),
            // Modèles PDF
            array('EMCONTRACT_ADDON_PDF', 'chaine', 'emcontract_standard', 'Name of PDF model of contract', 0, 'current', 0),
            array('EMCONTRACT_ADDON_PDF_ODT_PATH', 'chaine', 'DOL_DATA_ROOT/doctemplates/contracts', 'Directory for ODT templates', 0, 'current', 0),
            // Signature électronique
            array('EMCONTRACT_SIGNATURE_PROVIDER', 'chaine', 'docusign', 'Electronic signature provider', 0, 'current', 0),
            array('EMCONTRACT_SIGNATURE_API_KEY', 'chaine', '', 'API Key for signature provider', 0, 'current', 1),
            // Workflow
            array('EMCONTRACT_WORKFLOW_ENABLE', 'yesno', '1', 'Enable contract workflow', 0, 'current', 0),
            array('EMCONTRACT_AUTO_VALIDATE', 'yesno', '0', 'Enable automatic validation', 0, 'current', 0),
            // Notifications
            array('EMCONTRACT_RENEWAL_ALERT_DAYS', 'chaine', '30', 'Days before contract renewal to send alert', 0, 'current', 0),
            array('EMCONTRACT_NOTIFICATION_EMAIL', 'chaine', '', 'Email for notifications', 0, 'current', 0),
            // Sécurité
            array('EMCONTRACT_SECURITY_LEVEL', 'chaine', '2', 'Security level for document encryption', 0, 'current', 0),
            array('EMCONTRACT_ENCRYPTION_KEY', 'chaine', '', 'Encryption key for documents', 0, 'current', 1),
            // Intégration
            array('EMCONTRACT_ENABLE_API', 'yesno', '1', 'Enable REST API', 0, 'current', 0),
            array('EMCONTRACT_INTEGRATION_PAYROLL', 'yesno', '0', 'Enable payroll integration', 0, 'current', 0)
        );

        // 🔧 Initialiser $conf->emcontract si nécessaire
        if (!isset($conf->emcontract)) {
            $conf->emcontract = new stdClass();
        }
        if (!isset($conf->emcontract->dir_output)) {
            $conf->emcontract->dir_output = DOL_DATA_ROOT . '/emcontract';
        }
        if (!isset($conf->emcontract->dir_temp)) {
            $conf->emcontract->dir_temp = DOL_DATA_ROOT . '/emcontract/temp';
        }

        // Page de configuration
        $this->config_page_url = array("setup.php@emcontract");

        // Tabs
        $this->tabs = array('user:+emcontract:ContractTitle:emcontract@emcontract:$user->rights->emcontract->view->own:/emcontract/index.php?mainmenu=GRH&id=__ID__');

        // Permissions
        $this->rights = array();
        $r = 0;
        $this->rights[$r][0] = 75001;
        $this->rights[$r][1] = 'Consulter tous les contrats de travail';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'view';
        $this->rights[$r][5] = 'all';
        $r++;
        $this->rights[$r][0] = 75003;
        $this->rights[$r][1] = 'Ajouter un contrat de travail';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'add';
        $this->rights[$r][5] = '';
        $r++;
        $this->rights[$r][0] = 75004;
        $this->rights[$r][1] = 'Supprimer un contrat de travail';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'delete';
        $this->rights[$r][5] = '';
        $r++;
        $this->rights[$r][0] = 75005;
        $this->rights[$r][1] = 'Gérer les modèles de contrats';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'template';
        $this->rights[$r][5] = '';
        $r++;
        $this->rights[$r][0] = 75006;
        $this->rights[$r][1] = 'Gérer les signatures électroniques';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'signature';
        $this->rights[$r][5] = '';
        $r++;
        $this->rights[] = array(
            0 => 75007,
            1 => 'Gérer les workflows des contrats',
            3 => 0,
            4 => 'workflow',
            5 => ''
        );
        $this->rights[] = array(
            0 => 75008,
            1 => 'Accéder aux statistiques',
            3 => 0,
            4 => 'stats',
            5 => ''
        );

        // Modules supportés
        $this->module_parts = array(
            'models' => 1,
            'pdf' => 1,
            'tpl' => 1,
            'dir' => array(
                '/emcontract/core/modules/contract',
                '/emcontract/core/modules/contract/doc'
            ),
            'triggers' => 1
        );

        // Menus
        $this->menu = array();
        $r = 0;
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=hrm',
            'type' => 'left',
            'titre' => 'ContractTitle',
            'prefix' => img_picto('', $this->picto, 'class="paddingright pictofixedwidth valignmiddle"'),
            'mainmenu' => 'hrm',
            'leftmenu' => 'emcontract',
            'url' => '/custom/emcontract/index.php',
            'langs' => 'emcontract@emcontract',
            'position' => 200,
            'enabled' => '$conf->emcontract->enabled',
            'perms' => '$user->rights->emcontract->view',
            'target' => '',
            'user' => 2
        );
        $r++;
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=hrm,fk_leftmenu=emcontract',
            'type' => 'left',
            'titre' => 'MenuListContract',
            'mainmenu' => 'hrm',
            'leftmenu' => 'emcontract_list',
            'url' => '/custom/emcontract/index.php',
            'langs' => 'emcontract@emcontract',
            'position' => 201,
            'enabled' => '$conf->emcontract->enabled',
            'perms' => '$user->rights->emcontract->view',
            'target' => '',
            'user' => 2
        );
        $r++;
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=hrm,fk_leftmenu=emcontract',
            'type' => 'left',
            'titre' => 'MenuAddContract',
            'mainmenu' => 'hrm',
            'leftmenu' => 'emcontract_add',
            'url' => '/custom/emcontract/fiche.php?action=create',
            'langs' => 'emcontract@emcontract',
            'position' => 202,
            'enabled' => '$conf->emcontract->enabled',
            'perms' => '$user->rights->emcontract->add',
            'target' => '',
            'user' => 2
        );
        $r++;
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=hrm,fk_leftmenu=emcontract',
            'type' => 'left',
            'titre' => 'ContractTemplates',
            'mainmenu' => 'hrm',
            'leftmenu' => 'emcontract_templates',
            'url' => '/custom/emcontract/template_list.php',
            'langs' => 'emcontract@emcontract',
            'position' => 203,
            'enabled' => '$conf->emcontract->enabled',
            'perms' => '$user->rights->emcontract->template',
            'target' => '',
            'user' => 0
        );
        $r++;
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=hrm,fk_leftmenu=emcontract',
            'type' => 'left',
            'titre' => 'ContractDashboard',
            'mainmenu' => 'hrm',
            'leftmenu' => 'emcontract_dashboard',
            'url' => '/custom/emcontract/dashboard.php',
            'langs' => 'emcontract@emcontract',
            'position' => 204,
            'enabled' => '$conf->emcontract->enabled',
            'perms' => '$user->rights->emcontract->stats',
            'target' => '',
            'user' => 0
        );
        $r++;
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=hrm,fk_leftmenu=emcontract',
            'type' => 'left',
            'titre' => 'ContractWorkflow',
            'mainmenu' => 'hrm',
            'leftmenu' => 'emcontract_workflow',
            'url' => '/emcontract/workflow.php',
            'langs' => 'emcontract@emcontract',
            'position' => 205,
            'enabled' => '$conf->emcontract->enabled',
            'perms' => '$user->rights->emcontract->workflow',
            'target' => '',
            'user' => 2
        );

        // Export
        $r++;
        $this->export_code[$r] = $this->rights_class . '_' . $r;
        $this->export_label[$r] = 'CustomersInvoicesAndInvoiceLines';
        $this->export_permission[$r] = array(array("facture", "facture", "export"));
        $this->export_fields_array[$r] = array(
            's.rowid' => "IdCompany", 's.nom' => 'CompanyName', 's.address' => 'Address', 's.zip' => 'Zip', 's.town' => 'Town',
            's.fk_pays' => 'Country', 's.phone' => 'Phone', 's.siren' => 'ProfId1', 's.siret' => 'ProfId2', 's.ape' => 'ProfId3',
            's.idprof4' => 'ProfId4', 's.code_compta' => 'CustomerAccountancyCode', 's.code_compta_fournisseur' => 'SupplierAccountancyCode',
            'f.rowid' => "InvoiceId", 'f.facnumber' => "InvoiceRef", 'f.datec' => "InvoiceDateCreation", 'f.datef' => "DateInvoice",
            'f.total' => "TotalHT", 'f.total_ttc' => "TotalTTC", 'f.tva' => "TotalVAT", 'f.paye' => "InvoicePaid", 'f.fk_statut' => 'InvoiceStatus',
            'f.note' => "InvoiceNote", 'fd.rowid' => 'LineId', 'fd.description' => "LineDescription", 'fd.price' => "LineUnitPrice",
            'fd.tva_tx' => "LineVATRate", 'fd.qty' => "LineQty", 'fd.total_ht' => "LineTotalHT", 'fd.total_tva' => "LineTotalTVA",
            'fd.total_ttc' => "LineTotalTTC", 'fd.date_start' => "DateStart", 'fd.date_end' => "DateEnd", 'fd.fk_product' => 'ProductId',
            'p.ref' => 'ProductRef'
        );
        $this->export_entities_array[$r] = array(
            's.rowid' => "company", 's.nom' => 'company', 's.address' => 'company', 's.zip' => 'company', 's.town' => 'company',
            's.fk_pays' => 'company', 's.phone' => 'company', 's.siren' => 'company', 's.siret' => 'company', 's.ape' => 'company',
            's.idprof4' => 'company', 's.code_compta' => 'company', 's.code_compta_fournisseur' => 'company',
            'f.rowid' => "invoice", 'f.facnumber' => "invoice", 'f.datec' => "invoice", 'f.datef' => "invoice", 'f.total' => "invoice",
            'f.total_ttc' => "invoice", 'f.tva' => "invoice", 'f.paye' => "invoice", 'f.fk_statut' => 'invoice', 'f.note' => "invoice",
            'fd.rowid' => 'invoice_line', 'fd.description' => "invoice_line", 'fd.price' => "invoice_line", 'fd.total_ht' => "invoice_line",
            'fd.total_tva' => "invoice_line", 'fd.total_ttc' => "invoice_line", 'fd.tva_tx' => "invoice_line", 'fd.qty' => "invoice_line",
            'fd.date_start' => "invoice_line", 'fd.date_end' => "invoice_line", 'fd.fk_product' => 'product', 'p.ref' => 'product'
        );
        $this->export_alias_array[$r] = array(
            's.rowid' => "socid", 's.nom' => 'soc_name', 's.address' => 'soc_adres', 's.zip' => 'soc_zip', 's.town' => 'soc_town',
            's.fk_pays' => 'soc_pays', 's.phone' => 'soc_tel', 's.siren' => 'soc_siren', 's.siret' => 'soc_siret', 's.ape' => 'soc_ape',
            's.idprof4' => 'soc_idprof4', 's.code_compta' => 'soc_customer_accountancy', 's.code_compta_fournisseur' => 'soc_supplier_accountancy',
            'f.rowid' => "invoiceid", 'f.facnumber' => "ref", 'f.datec' => "datecreation", 'f.datef' => "dateinvoice", 'f.total' => "totalht",
            'f.total_ttc' => "totalttc", 'f.tva' => "totalvat", 'f.paye' => "paid", 'f.fk_statut' => 'status', 'f.note' => "note",
            'fd.rowid' => 'lineid', 'fd.description' => "linedescription", 'fd.price' => "lineprice", 'fd.total_ht' => "linetotalht",
            'fd.total_tva' => "linetotaltva", 'fd.total_ttc' => "linetotalttc", 'fd.tva_tx' => "linevatrate", 'fd.qty' => "lineqty",
            'fd.date_start' => "linedatestart", 'fd.date_end' => "linedateend", 'fd.fk_product' => 'productid', 'p.ref' => 'productref'
        );
        $this->export_sql_start[$r] = 'SELECT DISTINCT ';
        $this->export_sql_end[$r] = ' FROM (' . MAIN_DB_PREFIX . 'facture as f, ' . MAIN_DB_PREFIX . 'facturedet as fd, ' . MAIN_DB_PREFIX . 'societe as s)';
        $this->export_sql_end[$r] .= ' LEFT JOIN ' . MAIN_DB_PREFIX . 'product as p on (fd.fk_product = p.rowid)';
        $this->export_sql_end[$r] .= ' WHERE f.fk_soc = s.rowid AND f.rowid = fd.fk_facture';
    }

    /**
     * Fonction appelée lors de l'activation du module
     *
     * @param string $options Options (non utilisées)
     * @return int 1 si OK, 0 si KO
     */
    public function init($options = '')
    {
        $sql = array();
        $result = $this->load_tables();
        return $this->_init($sql);
    }

    /**
     * Fonction appelée lors de la désactivation du module
     *
     * @param string $options Options (non utilisées)
     * @return int 1 si OK, 0 si KO
     */
    public function remove($options = '')
    {
        $sql = array();
        return $this->_remove($sql);
    }

    /**
     * Chargement des tables SQL
     *
     * @return int 1 si OK, 0 si KO
     */
    private function load_tables()
    {
        return $this->_load_tables('/emcontract/sql/');
    }
}
?>