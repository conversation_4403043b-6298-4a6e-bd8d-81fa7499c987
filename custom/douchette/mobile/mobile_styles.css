/* Mobile styles for Douchette module */

/* Base styles */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
    margin: 0;
    padding: 10px;
    background-color: #f8f9fa;
    font-size: 16px;
    line-height: 1.5;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 10px;
}

/* Cards */
.card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.card-header {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    transition: all 0.2s ease;
    min-height: 44px; /* Touch target size */
}

.btn:hover, .btn:focus {
    background: #005a87;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,124,186,0.3);
}

.btn:active {
    transform: translateY(0);
}

.btn-success {
    background: #28a745;
}

.btn-success:hover {
    background: #218838;
}

.btn-danger {
    background: #dc3545;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #ced4da;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s ease;
    min-height: 44px;
}

.form-control:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0,124,186,0.1);
}

/* Quality control specific */
.measurement-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #3498db;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.measurement-result {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.conform-indicator {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    min-width: 80px;
}

.conform-yes {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.conform-no {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Navigation */
.nav-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    overflow-x: auto;
}

.nav-tab {
    padding: 12px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: #6c757d;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.nav-tab.active {
    color: #007cba;
    border-bottom: 2px solid #007cba;
}

/* Lists */
.list-group {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-item:last-child {
    margin-bottom: 0;
}

/* Status indicators */
.status {
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-warning {
    background: #fff3cd;
    color: #856404;
}

.status-danger {
    background: #f8d7da;
    color: #721c24;
}

/* Loading spinner */
.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007cba;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    body {
        padding: 5px;
        font-size: 14px;
    }
    
    .container {
        padding: 0 5px;
    }
    
    .card {
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 8px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 14px;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .form-control {
        padding: 10px 12px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .measurement-result {
        flex-direction: column;
        align-items: stretch;
    }
    
    .conform-indicator {
        text-align: center;
        margin-top: 10px;
    }
    
    .nav-tabs {
        margin: 0 -5px 20px -5px;
        padding: 0 5px;
    }
    
    .nav-tab {
        padding: 10px 15px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .card-header {
        font-size: 16px;
    }
    
    .measurement-card {
        padding: 15px;
    }
    
    .list-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card, .measurement-card, .list-item {
        background: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }
    
    .form-control {
        background: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }
    
    .form-control:focus {
        border-color: #007cba;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }
    
    .form-control {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
}
