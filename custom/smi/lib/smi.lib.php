<?php
/* Module SMI - Système de Management Intégré
 * Copyright (C) 2025 Sinedtyi
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 */

/**
 * \file        lib/smi.lib.php
 * \ingroup     smi
 * \brief       Library functions for SMI module
 */

/**
 * Prepare admin pages header
 *
 * @return array
 */
function smiAdminPrepareHead()
{
    global $langs, $conf;

    $langs->load("smi@smi");

    $h = 0;
    $head = array();

    $head[$h][0] = dol_buildpath("/smi/admin/setup.php", 1);
    $head[$h][1] = $langs->trans("GeneralSettings");
    $head[$h][2] = 'settings';
    $h++;

    $head[$h][0] = dol_buildpath("/smi/admin/integration.php", 1);
    $head[$h][1] = $langs->trans("ModuleIntegration");
    $head[$h][2] = 'integration';
    $h++;

    $head[$h][0] = dol_buildpath("/smi/admin/iso.php", 1);
    $head[$h][1] = $langs->trans("ISOStandards");
    $head[$h][2] = 'iso';
    $h++;

    $head[$h][0] = dol_buildpath("/smi/admin/iso_document_management.php", 1);
    $head[$h][1] = $langs->trans("ISODocumentManagement");
    $head[$h][2] = 'iso_management';
    $h++;

    $head[$h][0] = dol_buildpath("/smi/admin/about.php", 1);
    $head[$h][1] = $langs->trans("About");
    $head[$h][2] = 'about';
    $h++;

    // Show more tabs from modules
    complete_head_from_modules($conf, $langs, null, $head, $h, 'smi');

    return $head;
}

/**
 * Check if a module is enabled
 *
 * @param string $module Module name
 * @return bool
 */
function smiIsModuleEnabled($module)
{
    global $conf;
    
    $module = strtolower($module);
    return !empty($conf->$module->enabled);
}

/**
 * Get available modules for integration
 *
 * @return array
 */
function smiGetAvailableModules()
{
    $modules = array();
    
    // Quality modules
    if (smiIsModuleEnabled('quality')) {
        $modules['quality'] = array(
            'name' => 'Quality',
            'description' => 'Module de contrôle qualité',
            'tables' => array('llx_quality_control', 'llx_quality_conformity_declaration'),
            'enabled' => true
        );
    }
    
    // DigiRisk
    if (smiIsModuleEnabled('digiriskdolibarr')) {
        $modules['digirisk'] = array(
            'name' => 'DigiRisk',
            'description' => 'Gestion des risques SST',
            'tables' => array('llx_digiriskdolibarr_risk', 'llx_digiriskdolibarr_riskassessment'),
            'enabled' => true
        );
    }
    
    // Plan Formation
    if (smiIsModuleEnabled('planformation')) {
        $modules['formation'] = array(
            'name' => 'Plan Formation',
            'description' => 'Gestion des formations',
            'tables' => array('llx_planform_session', 'llx_planform_formation'),
            'enabled' => true
        );
    }
    
    // DoliMeet
    if (smiIsModuleEnabled('dolimeet')) {
        $modules['dolimeet'] = array(
            'name' => 'DoliMeet',
            'description' => 'Gestion des réunions et audits',
            'tables' => array('llx_dolimeet_meeting', 'llx_dolimeet_meetingobject'),
            'enabled' => true
        );
    }
    
    // Tickets
    if (smiIsModuleEnabled('ticket')) {
        $modules['ticket'] = array(
            'name' => 'Tickets',
            'description' => 'Gestion des tickets et actions correctives',
            'tables' => array('llx_ticket'),
            'enabled' => true
        );
    }
    
    // HRM/DoliSIRH
    if (smiIsModuleEnabled('dolisirh')) {
        $modules['hrm'] = array(
            'name' => 'DoliSIRH',
            'description' => 'Gestion des ressources humaines',
            'tables' => array('llx_dolisirh_timesheet', 'llx_dolisirh_certificate'),
            'enabled' => true
        );
    } elseif (smiIsModuleEnabled('hrm')) {
        $modules['hrm'] = array(
            'name' => 'HRM',
            'description' => 'Gestion des ressources humaines',
            'tables' => array('llx_hrm_evaluation', 'llx_hrm_skill'),
            'enabled' => true
        );
    }
    
    return $modules;
}

/**
 * Get SMI statistics
 *
 * @return array
 */
function smiGetStatistics()
{
    global $db;
    
    $stats = array();
    
    // Processus
    $sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . "smi_process WHERE status = 1";
    $resql = $db->query($sql);
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $stats['active_processes'] = $obj->nb;
    }
    
    // Indicateurs KPI
    $sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . "smi_indicator WHERE status = 1";
    $resql = $db->query($sql);
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $stats['active_indicators'] = $obj->nb;
    }
    
    // Actions en cours
    $sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . "smi_action WHERE status = 'inprogress'";
    $resql = $db->query($sql);
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $stats['pending_actions'] = $obj->nb;
    }
    
    // Actions en retard
    $sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . "smi_action 
            WHERE status = 'inprogress' AND deadline < NOW()";
    $resql = $db->query($sql);
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $stats['overdue_actions'] = $obj->nb;
    }
    
    return $stats;
}

/**
 * Get KPI data from integrated modules
 *
 * @return array
 */
function smiGetKPIData()
{
    global $db, $conf;

    $kpis = array();

    // Quality KPIs
    if (smiIsModuleEnabled('quality')) {
        // Taux de conformité - mois actuel
        $sql = "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN conformity_status = 'conforme' THEN 1 ELSE 0 END) as conforme
                FROM " . MAIN_DB_PREFIX . "quality_conformity_declaration
                WHERE MONTH(date_creation) = MONTH(NOW()) AND YEAR(date_creation) = YEAR(NOW())";
        $resql = $db->query($sql);
        if ($resql) {
            $obj = $db->fetch_object($resql);
            $kpis['quality_conformity_rate'] = $obj->total > 0 ? round(($obj->conforme / $obj->total) * 100, 2) : 0;
        }

        // Taux de conformité - mois précédent pour calculer la tendance
        $sql = "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN conformity_status = 'conforme' THEN 1 ELSE 0 END) as conforme
                FROM " . MAIN_DB_PREFIX . "quality_conformity_declaration
                WHERE MONTH(date_creation) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))
                AND YEAR(date_creation) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
        $resql = $db->query($sql);
        if ($resql) {
            $obj = $db->fetch_object($resql);
            $prev_rate = $obj->total > 0 ? round(($obj->conforme / $obj->total) * 100, 2) : 0;
            if ($prev_rate > 0) {
                $kpis['quality_conformity_trend'] = round($kpis['quality_conformity_rate'] - $prev_rate, 1);
            } else {
                $kpis['quality_conformity_trend'] = 0;
            }
        }
    }

    // Safety KPIs
    if (smiIsModuleEnabled('digiriskdolibarr')) {
        // Nombre d'incidents - mois actuel
        $sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . "ticket
                WHERE type_code = 'INCIDENT'
                AND MONTH(datec) = MONTH(NOW()) AND YEAR(datec) = YEAR(NOW())";
        $resql = $db->query($sql);
        if ($resql) {
            $obj = $db->fetch_object($resql);
            $kpis['safety_incidents'] = $obj->nb;
        }

        // Nombre d'incidents - mois précédent pour calculer la tendance
        $sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . "ticket
                WHERE type_code = 'INCIDENT'
                AND MONTH(datec) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))
                AND YEAR(datec) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
        $resql = $db->query($sql);
        if ($resql) {
            $obj = $db->fetch_object($resql);
            $kpis['safety_incidents_trend'] = $kpis['safety_incidents'] - $obj->nb;
        }
    }

    // Training KPIs
    if (smiIsModuleEnabled('planformation')) {
        // Taux de formation réalisée - année actuelle
        $sql = "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                FROM " . MAIN_DB_PREFIX . "planform_session
                WHERE YEAR(date_session) = YEAR(NOW())";
        $resql = $db->query($sql);
        if ($resql) {
            $obj = $db->fetch_object($resql);
            $kpis['training_completion_rate'] = $obj->total > 0 ? round(($obj->completed / $obj->total) * 100, 2) : 0;
        }

        // Taux de formation réalisée - mois actuel vs mois précédent pour la tendance
        $sql = "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                FROM " . MAIN_DB_PREFIX . "planform_session
                WHERE MONTH(date_session) = MONTH(NOW()) AND YEAR(date_session) = YEAR(NOW())";
        $resql = $db->query($sql);
        $current_month_rate = 0;
        if ($resql) {
            $obj = $db->fetch_object($resql);
            $current_month_rate = $obj->total > 0 ? round(($obj->completed / $obj->total) * 100, 2) : 0;
        }

        $sql = "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                FROM " . MAIN_DB_PREFIX . "planform_session
                WHERE MONTH(date_session) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))
                AND YEAR(date_session) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
        $resql = $db->query($sql);
        if ($resql) {
            $obj = $db->fetch_object($resql);
            $prev_month_rate = $obj->total > 0 ? round(($obj->completed / $obj->total) * 100, 2) : 0;
            if ($prev_month_rate > 0) {
                $kpis['training_completion_trend'] = round($current_month_rate - $prev_month_rate, 1);
            } else {
                $kpis['training_completion_trend'] = 0;
            }
        }
    }

    return $kpis;
}

/**
 * Create inter-module links
 *
 * @param string $source_module Source module
 * @param int $source_id Source object ID
 * @param string $target_module Target module
 * @param int $target_id Target object ID
 * @param string $link_type Type of link
 * @return bool
 */
function smiCreateModuleLink($source_module, $source_id, $target_module, $target_id, $link_type)
{
    global $db;
    
    $sql = "INSERT INTO " . MAIN_DB_PREFIX . "smi_module_links 
            (source_module, source_id, target_module, target_id, link_type, date_creation)
            VALUES ('" . $db->escape($source_module) . "', " . (int)$source_id . ", 
                    '" . $db->escape($target_module) . "', " . (int)$target_id . ", 
                    '" . $db->escape($link_type) . "', NOW())";
    
    return $db->query($sql);
}

/**
 * Get module links
 *
 * @param string $module Module name
 * @param int $object_id Object ID
 * @return array
 */
function smiGetModuleLinks($module, $object_id)
{
    global $db;
    
    $links = array();
    
    $sql = "SELECT * FROM " . MAIN_DB_PREFIX . "smi_module_links 
            WHERE (source_module = '" . $db->escape($module) . "' AND source_id = " . (int)$object_id . ")
            OR (target_module = '" . $db->escape($module) . "' AND target_id = " . (int)$object_id . ")
            ORDER BY date_creation DESC";
    
    $resql = $db->query($sql);
    if ($resql) {
        while ($obj = $db->fetch_object($resql)) {
            $links[] = $obj;
        }
    }
    
    return $links;
}

/**
 * Check SMI module health
 *
 * @return array
 */
function smiCheckHealth()
{
    global $db, $conf;
    
    $health = array(
        'status' => 'ok',
        'checks' => array(),
        'errors' => array()
    );
    
    // Check required tables
    $required_tables = array(
        'smi_process',
        'smi_indicator',
        'smi_action',
        'smi_module_links'
    );
    
    foreach ($required_tables as $table) {
        $sql = "SHOW TABLES LIKE '" . MAIN_DB_PREFIX . $table . "'";
        $resql = $db->query($sql);
        $exists = $resql && $db->num_rows($resql) > 0;
        
        $health['checks'][$table] = $exists;
        if (!$exists) {
            $health['errors'][] = "Table $table manquante";
            $health['status'] = 'error';
        }
    }
    
    // Check configuration
    $required_config = array(
        'SMI_ENABLE_DASHBOARD',
        'SMI_ENABLE_PROCESSES',
        'SMI_ENABLE_INDICATORS'
    );
    
    foreach ($required_config as $config) {
        $value = $conf->global->$config ?? null;
        $health['checks'][$config] = !empty($value);
        if (empty($value)) {
            $health['errors'][] = "Configuration $config manquante";
            if ($health['status'] == 'ok') {
                $health['status'] = 'warning';
            }
        }
    }
    
    return $health;
}

/**
 * Get enabled ISO standards
 *
 * @return array Array of enabled ISO standards with their details
 */
function smiGetEnabledISOStandards()
{
    global $conf;

    // Define all available ISO standards
    $iso_standards = array(
        'ISO_9001' => array(
            'code' => 'ISO 9001',
            'label' => 'ISO 9001:2015',
            'description' => 'Systèmes de management de la qualité',
            'domain' => 'Qualité',
            'color' => '#2196F3',
            'short' => '9001'
        ),
        'ISO_14001' => array(
            'code' => 'ISO 14001',
            'label' => 'ISO 14001:2015',
            'description' => 'Systèmes de management environnemental',
            'domain' => 'Environnement',
            'color' => '#4CAF50',
            'short' => '14001'
        ),
        'ISO_45001' => array(
            'code' => 'ISO 45001',
            'label' => 'ISO 45001:2018',
            'description' => 'Systèmes de management de la santé et sécurité au travail',
            'domain' => 'Sécurité',
            'color' => '#FF9800',
            'short' => '45001'
        ),
        'ISO_22000' => array(
            'code' => 'ISO 22000',
            'label' => 'ISO 22000:2018',
            'description' => 'Systèmes de management de la sécurité alimentaire',
            'domain' => 'Sécurité alimentaire',
            'color' => '#E91E63',
            'short' => '22000'
        ),
        'ISO_50001' => array(
            'code' => 'ISO 50001',
            'label' => 'ISO 50001:2018',
            'description' => 'Systèmes de management de l\'énergie',
            'domain' => 'Énergie',
            'color' => '#9C27B0',
            'short' => '50001'
        ),
        'ISO_27001' => array(
            'code' => 'ISO 27001',
            'label' => 'ISO 27001:2022',
            'description' => 'Systèmes de management de la sécurité de l\'information',
            'domain' => 'Sécurité informatique',
            'color' => '#607D8B',
            'short' => '27001'
        ),
        'ISO_26000' => array(
            'code' => 'ISO 26000',
            'label' => 'ISO 26000:2010',
            'description' => 'Responsabilité sociétale',
            'domain' => 'Responsabilité sociétale',
            'color' => '#795548',
            'short' => '26000'
        )
    );

    $enabled_standards = array();

    // Check which standards are enabled
    foreach ($iso_standards as $key => $standard) {
        $const_name = 'SMI_' . $key;
        if (getDolGlobalString($const_name, '1')) {
            $enabled_standards[$key] = $standard;
        }
    }

    return $enabled_standards;
}

/**
 * Get enabled ISO standards as a formatted string
 *
 * @param string $format Format: 'short' (9001/14001), 'full' (ISO 9001/ISO 14001), 'codes' (ISO 9001, ISO 14001)
 * @param string $separator Separator between standards
 * @return string Formatted string of enabled standards
 */
function smiGetEnabledISOStandardsString($format = 'codes', $separator = '/')
{
    $enabled_standards = smiGetEnabledISOStandards();

    if (empty($enabled_standards)) {
        return '';
    }

    $standards_array = array();

    foreach ($enabled_standards as $standard) {
        switch ($format) {
            case 'short':
                $standards_array[] = $standard['short'];
                break;
            case 'full':
                $standards_array[] = $standard['label'];
                break;
            case 'codes':
            default:
                $standards_array[] = $standard['code'];
                break;
        }
    }

    return implode($separator, $standards_array);
}

/**
 * Check if a specific ISO standard is enabled
 *
 * @param string $iso_code ISO code (e.g., '9001', 'ISO_9001', 'ISO 9001')
 * @return bool True if enabled, false otherwise
 */
function smiIsISOStandardEnabled($iso_code)
{
    // Normalize the ISO code
    $iso_code = str_replace(array('ISO ', 'ISO_'), '', $iso_code);
    $const_name = 'SMI_ISO_' . $iso_code;

    return getDolGlobalString($const_name, '1') ? true : false;
}

/**
 * Prepare array of tabs for Process
 *
 * @param	SMIProcess	$object		SMIProcess
 * @return 	array					Array of tabs
 */
function process_prepare_head($object)
{
	global $db, $langs, $conf;

	$langs->load("smi@smi");

	$h = 0;
	$head = array();

	$head[$h][0] = dol_buildpath("/smi/processes/card.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Card");
	$head[$h][2] = 'card';
	$h++;

	// Indicators tab
	$head[$h][0] = dol_buildpath("/smi/processes/indicators.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Indicators");
	// Count indicators for badge
	$sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . "smi_indicator WHERE fk_process = " . $object->id;
	$resql = $db->query($sql);
	if ($resql) {
		$obj = $db->fetch_object($resql);
		if ($obj->nb > 0) {
			$head[$h][1] .= '<span class="badge marginleftonlyshort">'.$obj->nb.'</span>';
		}
	}
	$head[$h][2] = 'indicators';
	$h++;

	if (isset($object->fields['note_public']) || isset($object->fields['note_private'])) {
		$nbNote = 0;
		if (!empty($object->note_private)) {
			$nbNote++;
		}
		if (!empty($object->note_public)) {
			$nbNote++;
		}
		$head[$h][0] = dol_buildpath('/smi/processes/note.php', 1).'?id='.$object->id;
		$head[$h][1] = $langs->trans('Notes');
		if ($nbNote > 0) {
			$head[$h][1] .= '<span class="badge marginleftonlyshort">'.$nbNote.'</span>';
		}
		$head[$h][2] = 'note';
		$h++;
	}

	require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
	require_once DOL_DOCUMENT_ROOT.'/core/class/link.class.php';
	$upload_dir = $conf->smi->dir_output . "/process/" . dol_sanitizeFileName($object->ref);
	$nbFiles = count(dol_dir_list($upload_dir, 'files', 0, '', '(\.meta|_preview.*\.png)$'));
	$nbLinks = Link::count($db, $object->element, $object->id);
	$head[$h][0] = dol_buildpath("/smi/processes/document.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans('Documents');
	if (($nbFiles + $nbLinks) > 0) {
		$head[$h][1] .= '<span class="badge marginleftonlyshort">'.($nbFiles + $nbLinks).'</span>';
	}
	$head[$h][2] = 'document';
	$h++;

	$head[$h][0] = dol_buildpath("/smi/processes/agenda.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Events");
	$head[$h][2] = 'agenda';
	$h++;

	// Show more tabs from modules
	complete_head_from_modules($conf, $langs, $object, $head, $h, 'process@smi');
	complete_head_from_modules($conf, $langs, $object, $head, $h, 'process@smi', 'remove');

	return $head;
}

/**
 * Prepare array of tabs for Indicator
 *
 * @param	SMIIndicator	$object		SMIIndicator
 * @return 	array					Array of tabs
 */
function indicator_prepare_head($object)
{
	global $db, $langs, $conf;

	$langs->load("smi@smi");

	$h = 0;
	$head = array();

	$head[$h][0] = dol_buildpath("/smi/indicators/card.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Card");
	$head[$h][2] = 'card';
	$h++;

	// Values tab
	$head[$h][0] = dol_buildpath("/smi/indicators/values.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Values");
	// Count values for badge
	$sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . "smi_indicator_value WHERE fk_indicator = " . $object->id;
	$resql = $db->query($sql);
	if ($resql) {
		$obj = $db->fetch_object($resql);
		if ($obj->nb > 0) {
			$head[$h][1] .= '<span class="badge marginleftonlyshort">'.$obj->nb.'</span>';
		}
	}
	$head[$h][2] = 'values';
	$h++;

	if (isset($object->fields['note_public']) || isset($object->fields['note_private'])) {
		$nbNote = 0;
		if (!empty($object->note_private)) {
			$nbNote++;
		}
		if (!empty($object->note_public)) {
			$nbNote++;
		}
		$head[$h][0] = dol_buildpath('/smi/indicators/note.php', 1).'?id='.$object->id;
		$head[$h][1] = $langs->trans('Notes');
		if ($nbNote > 0) {
			$head[$h][1] .= '<span class="badge marginleftonlyshort">'.$nbNote.'</span>';
		}
		$head[$h][2] = 'note';
		$h++;
	}

	require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
	require_once DOL_DOCUMENT_ROOT.'/core/class/link.class.php';
	$upload_dir = $conf->smi->dir_output . "/indicator/" . dol_sanitizeFileName($object->ref);
	$nbFiles = count(dol_dir_list($upload_dir, 'files', 0, '', '(\.meta|_preview.*\.png)$'));
	$nbLinks = Link::count($db, $object->element, $object->id);
	$head[$h][0] = dol_buildpath("/smi/indicators/document.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans('Documents');
	if (($nbFiles + $nbLinks) > 0) {
		$head[$h][1] .= '<span class="badge marginleftonlyshort">'.($nbFiles + $nbLinks).'</span>';
	}
	$head[$h][2] = 'document';
	$h++;

	$head[$h][0] = dol_buildpath("/smi/indicators/agenda.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Events");
	$head[$h][2] = 'agenda';
	$h++;

	// Show more tabs from modules
	complete_head_from_modules($conf, $langs, $object, $head, $h, 'indicator@smi');
	complete_head_from_modules($conf, $langs, $object, $head, $h, 'indicator@smi', 'remove');

	return $head;
}
