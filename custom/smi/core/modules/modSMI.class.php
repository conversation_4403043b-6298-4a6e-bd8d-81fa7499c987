<?php
/* Module SMI - Système de Management Intégré
 * Copyright (C) 2025 Sinedtyi
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

/**
 * \file        core/modules/modSMI.class.php
 * \ingroup     smi
 * \brief       Description and activation file for module SMI
 */

include_once DOL_DOCUMENT_ROOT . '/core/modules/DolibarrModules.class.php';

/**
 * Description and activation class for module SMI
 */
class modSMI extends DolibarrModules
{
    /**
     * Constructor. Define names, constants, directories, boxes, permissions
     *
     * @param DoliDB $db Database handler
     */
    public function __construct($db)
    {
        global $langs, $conf;

        $this->db = $db;

        // Id for module (must be unique).
        // Use here a free id (See in Home -> System information -> Dolibarr for list of used modules id).
        $this->numero = 700300;
        // Key text used to identify module (for permissions, menus, etc...)
        $this->rights_class = 'smi';

        // Family can be 'base' (core modules),'crm','financial','hr','projects','products','ecm','technic' (transverse modules),'interface' (interface modules),'other','...'
        // It is used to group modules by family in module setup page
        $this->family = "technic";
        // Module position in the family on 2 digits ('01', '10', '20', ...)
        $this->module_position = '90';
        // Gives the possibility for the module, to provide his own family info and position of this family (Overwrite $this->family and $this->module_position. Avoid this)
        //$this->familyinfo = array('myownfamily' => array('position' => '01', 'label' => $langs->trans("MyOwnFamily")));
        // Module label (no space allowed), used if translation string 'ModuleSMIName' not found (SMI is name of module).
        $this->name = preg_replace('/^mod/i', '', get_class($this));
        // Module description, used if translation string 'ModuleSMIDesc' not found (SMI is name of module).
        $this->description = "Système de Management Intégré QSE (ISO 9001, 14001, 45001, 22000, 50001, 27001, 26000)";
        // Used only if file README.md and README-LL.md not found.
        $this->descriptionlong = "Module de gestion intégrée du système de management qualité, sécurité et environnement conforme aux normes ISO 9001 (Qualité), 14001 (Environnement), 45001 (SST), 22000 (Sécurité alimentaire), 50001 (Énergie), 27001 (Sécurité informatique) et 26000 (Responsabilité sociétale). Centralise les processus, indicateurs et documentation QSE.";

        // Possible values for version are: 'development', 'experimental', 'dolibarr', 'dolibarr_deprecated' or a version string like 'x.y.z'
        $this->version = '1.0.0';
        // Url to the file with your last numberversion of this module
        //$this->url_last_version = 'http://www.example.com/versionmodule.txt';

        // Key used in llx_const table to save module status enabled/disabled (where SMI is value of property name of module in uppercase)
        $this->const_name = 'MAIN_MODULE_' . strtoupper($this->name);

        // Name of image file used for this module.
        // If file is in theme/yourtheme/img directory under name object_pictovalue.png, use this->picto='pictovalue'
        // If file is in module/img directory under name object_pictovalue.png, use this->picto='pictovalue@module'
        // To use a supported fa-xxx css style of font awesome, use this->picto='xxx'
        $this->picto = 'smi@smi';

        // Define some features supported by module (triggers, login, substitutions, menus, css, etc...)
        $this->module_parts = array(
            // Set this to 1 if module has its own trigger directory (core/triggers)
            'triggers' => 1,
            // Set this to 1 if module has its own login method file (core/login)
            'login' => 0,
            // Set this to 1 if module has its own substitution function file (core/substitutions)
            'substitutions' => 0,
            // Set this to 1 if module has its own menus handler directory (core/menus)
            'menus' => 0,
            // Set this to 1 if module overwrite template dir (core/tpl)
            'tpl' => 0,
            // Set this to 1 if module has its own barcode directory (core/modules/barcode)
            'barcode' => 0,
            // Set this to 1 if module has its own models directory (core/modules/xxx)
            'models' => 1,
            // Set this to 1 if module has its own printing directory (core/modules/printing)
            'printing' => 0,
            // Set this to 1 if module has its own theme directory (theme)
            'theme' => 0,
            // Set this to relative path of css file if module has its own css file
            'css' => array(
                '/smi/css/smi.css.php',
            ),
            // Set this to relative path of js file if module must load a js on all pages
            'js' => array(
                '/smi/js/smi.js.php',
            ),
            // Set here all hooks context managed by module. To find available hook, make a "grep -r '>executeHooks(' *" on source code. You can also set hook context to 'all'
            'hooks' => array(
                'projectcard',
                'ticketcard',
                'thirdpartycard',
                'productcard',
                'invoicecard',
                'ordercard',
                'propalcard',
                'contractcard',
                'interventioncard',
                'usercard',
                'globalcard',
                'main'
            ),
            // Set this to 1 if features of module are opened to external users
            'moduleforexternal' => 0,
        );

        // Data directories to create when module is enabled.
        $this->dirs = array(
            '/smi/temp',
            '/ecm/smi',
            '/ecm/smi/processus',
            '/ecm/smi/politiques',
            '/ecm/smi/manuels',
            '/ecm/smi/procedures',
            '/ecm/smi/procedures/controle_qualite',
            '/ecm/smi/procedures/maintenance',
            '/ecm/smi/procedures/logistique',
            '/ecm/smi/procedures/formation',
            '/ecm/smi/audits',
            '/ecm/smi/formations',
            '/ecm/smi/non_conformites',
            '/ecm/smi/capa',
            '/ecm/smi/risques',
            '/ecm/smi/indicateurs',
            '/ecm/smi/revues_direction'
        );

        // Config pages. Put here list of php page, stored into smi/admin directory, to use to setup module.
        $this->config_page_url = array("setup.php@smi");

        // Dependencies
        // A condition to hide module
        $this->hidden = false;
        // List of module class names as string that must be enabled if this module is enabled. Example: array('always1'=>'modModuleToEnable1','always2'=>'modModuleToEnable2', 'FR1'=>'modModuleToEnableFR'...)
        $this->depends = array();
        $this->requiredby = array(); // List of module class names as string to disable if this one is disabled. Example: array('modModuleToDisable1', ...)
        $this->conflictwith = array(); // List of module class names as string this module is in conflict with. Example: array('modModuleToDisable1', ...)

        // The language file dedicated to your module
        $this->langfiles = array("smi@smi");

        // Prerequisites
        $this->phpmin = array(7, 0); // Minimum version of PHP required by module
        $this->need_dolibarr_version = array(10, 0); // Minimum version of Dolibarr required by module

        // Messages at activation
        $this->warnings_activation = array(); // Warning to show when we activate module. array('always'='text') or array('FR'='textfr','ES'='textes'...)
        $this->warnings_activation_ext = array(); // Warning to show when we activate an external module. array('always'='text') or array('FR'='textfr','ES'='textes'...)
        //$this->automatic_activation = array('FR'=>'SMIWasAutomaticallyActivatedBecauseOfYourCountryChoice');
        //$this->always_enabled = true;								// If true, can't be disabled

        // Constants
        // List of particular constants to add when module is enabled (key, 'chaine', value, desc, visible, 'current' or 'allentities', deleteonunactive)
        // Example: $this->const=array(1 => array('SMI_MYNEWCONST1', 'chaine', 'myvalue', 'This is a constant to add', 1),
        //                             2 => array('SMI_MYNEWCONST2', 'chaine', 'myvalue', 'This is another constant to add', 0, 'current', 1)
        // );
        $this->const = array(
            // Configuration générale
            1 => array('SMI_ENABLE_DASHBOARD', 'chaine', '1', 'Activer le tableau de bord SMI', 1),
            2 => array('SMI_ENABLE_PROCESSES', 'chaine', '1', 'Activer la gestion des processus', 1),
            3 => array('SMI_ENABLE_INDICATORS', 'chaine', '1', 'Activer les indicateurs KPI', 1),
            4 => array('SMI_ENABLE_DOCUMENTS', 'chaine', '1', 'Activer la gestion documentaire QSE', 1),
            5 => array('SMI_ENABLE_PLANNING', 'chaine', '1', 'Activer la planification', 1),
            6 => array('SMI_ENABLE_REPORTING', 'chaine', '1', 'Activer les rapports de conformité', 1),
            // Intégrations modules
            7 => array('SMI_INTEGRATE_QUALITY', 'chaine', '1', 'Intégrer avec le module Quality', 1),
            8 => array('SMI_INTEGRATE_DIGIRISK', 'chaine', '1', 'Intégrer avec DigiRisk', 1),
            9 => array('SMI_INTEGRATE_FORMATION', 'chaine', '1', 'Intégrer avec Plan Formation', 1),
            10 => array('SMI_INTEGRATE_DOLIMEET', 'chaine', '1', 'Intégrer avec DoliMeet', 1),
            11 => array('SMI_INTEGRATE_TICKETS', 'chaine', '1', 'Intégrer avec les Tickets', 1),
            12 => array('SMI_INTEGRATE_HRM', 'chaine', '1', 'Intégrer avec HRM/DoliSIRH', 1),
            // Normes ISO
            13 => array('SMI_ISO_9001', 'chaine', '1', 'Activer ISO 9001 (Qualité)', 1),
            14 => array('SMI_ISO_14001', 'chaine', '1', 'Activer ISO 14001 (Environnement)', 1),
            15 => array('SMI_ISO_45001', 'chaine', '1', 'Activer ISO 45001 (SST)', 1),
            16 => array('SMI_ISO_22000', 'chaine', '1', 'Activer ISO 22000 (Sécurité alimentaire)', 1),
            17 => array('SMI_ISO_50001', 'chaine', '1', 'Activer ISO 50001 (Management de l\'énergie)', 1),
            18 => array('SMI_ISO_27001', 'chaine', '1', 'Activer ISO 27001 (Sécurité informatique)', 1),
            19 => array('SMI_ISO_26000', 'chaine', '1', 'Activer ISO 26000 (Responsabilité sociétale)', 1),
        );

        // Array to add new pages in new tabs
        $this->tabs = array();

        // Array to add constants to create when module is enabled
        $this->const = array(
            1 => array('SMI_ENABLE_DASHBOARD', 'chaine', '1', 'Enable SMI Dashboard', 0),
            2 => array('SMI_ENABLE_PROCESSES', 'chaine', '1', 'Enable SMI Processes', 0),
            3 => array('SMI_ENABLE_INDICATORS', 'chaine', '1', 'Enable SMI Indicators', 0),
            4 => array('SMI_ENABLE_DOCUMENTS', 'chaine', '1', 'Enable SMI Documents', 0),
            5 => array('SMI_ENABLE_PLANNING', 'chaine', '1', 'Enable SMI Planning', 0),
            6 => array('SMI_ENABLE_REPORTING', 'chaine', '1', 'Enable SMI Reporting', 0),
        );

        // Dictionaries
        $this->dictionaries = array();

        // Boxes/Widgets
        // Add here list of php file(s) stored in smi/core/boxes that contains a class to show a widget.
        $this->boxes = array(
            0 => array(
                'file' => 'smiwidgetdashboard.php@smi',
                'note' => 'Widget tableau de bord SMI',
                'enabledbydefaulton' => 'Home'
            ),
            1 => array(
                'file' => 'smiwidgetindicators.php@smi',
                'note' => 'Widget indicateurs KPI',
                'enabledbydefaulton' => 'Home'
            ),
            2 => array(
                'file' => 'smiwidgetactions.php@smi',
                'note' => 'Widget actions en cours',
                'enabledbydefaulton' => 'Home'
            ),
        );

        // Cronjobs (List of cron jobs entries to add when module is enabled)
        $this->cronjobs = array(
            0 => array(
                'label' => 'SMI - Calcul des indicateurs KPI',
                'jobtype' => 'method',
                'class' => '/smi/class/smi.class.php',
                'objectname' => 'SMI',
                'method' => 'calculateKPIs',
                'parameters' => '',
                'comment' => 'Calcule automatiquement les indicateurs KPI du système de management',
                'frequency' => 1,
                'unitfrequency' => 86400,
                'status' => 0,
                'test' => '$conf->smi->enabled'
            ),
            1 => array(
                'label' => 'SMI - Synchronisation inter-modules',
                'jobtype' => 'method',
                'class' => '/smi/class/smi.class.php',
                'objectname' => 'SMI',
                'method' => 'syncModules',
                'parameters' => '',
                'comment' => 'Synchronise les données entre les différents modules QSE',
                'frequency' => 1,
                'unitfrequency' => 3600,
                'status' => 0,
                'test' => '$conf->smi->enabled'
            ),
            2 => array(
                'label' => 'SMI - Notifications échéances',
                'jobtype' => 'method',
                'class' => '/smi/class/smi.class.php',
                'objectname' => 'SMI',
                'method' => 'checkDeadlines',
                'parameters' => '',
                'comment' => 'Vérifie les échéances des audits, formations et actions',
                'frequency' => 1,
                'unitfrequency' => 86400,
                'status' => 0,
                'test' => '$conf->smi->enabled'
            ),
        );

        // Permissions provided by this module
        $this->rights = array();
        $r = 0;

        // Permission to read SMI
        $this->rights[$r][0] = $this->numero + $r;
        $this->rights[$r][1] = 'Consulter le système de management';
        $this->rights[$r][3] = 1;
        $this->rights[$r][4] = 'lire';
        $r++;

        // Permission to create/modify SMI
        $this->rights[$r][0] = $this->numero + $r;
        $this->rights[$r][1] = 'Créer/modifier les éléments du système de management';
        $this->rights[$r][3] = 1;
        $this->rights[$r][4] = 'creer';
        $r++;

        // Permission to delete SMI
        $this->rights[$r][0] = $this->numero + $r;
        $this->rights[$r][1] = 'Supprimer les éléments du système de management';
        $this->rights[$r][3] = 1;
        $this->rights[$r][4] = 'supprimer';
        $r++;

        // Permission to manage processes
        $this->rights[$r][0] = $this->numero + $r;
        $this->rights[$r][1] = 'Gérer les processus métier';
        $this->rights[$r][3] = 1;
        $this->rights[$r][4] = 'processus';
        $r++;

        // Permission to manage indicators
        $this->rights[$r][0] = $this->numero + $r;
        $this->rights[$r][1] = 'Gérer les indicateurs KPI';
        $this->rights[$r][3] = 1;
        $this->rights[$r][4] = 'indicateurs';
        $r++;

        // Permission to manage documents
        $this->rights[$r][0] = $this->numero + $r;
        $this->rights[$r][1] = 'Gérer la documentation QSE';
        $this->rights[$r][3] = 1;
        $this->rights[$r][4] = 'documents';
        $r++;

        // Permission to generate reports
        $this->rights[$r][0] = $this->numero + $r;
        $this->rights[$r][1] = 'Générer les rapports de conformité';
        $this->rights[$r][3] = 1;
        $this->rights[$r][4] = 'rapports';
        $r++;

        // Permission to configure SMI
        $this->rights[$r][0] = $this->numero + $r;
        $this->rights[$r][1] = 'Configurer le système de management';
        $this->rights[$r][3] = 1;
        $this->rights[$r][4] = 'configurer';
        $r++;

        // Main menu entries to add
        $this->menu = array();
        $r = 0;

        // Menu principal SMI
        $this->menu[$r] = array(
            'fk_menu' => '',
            'type' => 'top',
            'titre' => 'SMI',
            'prefix' => img_picto('', 'smi@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => '',
            'url' => '/custom/smi/dashboard.php',
            'langs' => 'smi@smi',
            'position' => 1000,
            'enabled' => '$conf->smi->enabled',
            'perms' => '$user->rights->smi->lire',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Tableau de bord
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi',
            'type' => 'left',
            'titre' => 'Tableau de bord',
            'prefix' => img_picto('', 'dashboard@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_dashboard',
            'url' => '/custom/smi/dashboard.php',
            'langs' => 'smi@smi',
            'position' => 1001,
            'enabled' => '1',
            'perms' => '$user->rights->smi->lire',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Processus
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi',
            'type' => 'left',
            'titre' => 'Processus',
            'prefix' => img_picto('', 'processes@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_processes',
            'url' => '/custom/smi/processes/list.php',
            'langs' => 'smi@smi',
            'position' => 1002,
            'enabled' => '1',
            'perms' => '$user->rights->smi->processus',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Indicateurs KPI
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi',
            'type' => 'left',
            'titre' => 'Indicateurs KPI',
            'prefix' => img_picto('', 'indicators@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_indicators',
            'url' => '/custom/smi/indicators/list.php',
            'langs' => 'smi@smi',
            'position' => 1003,
            'enabled' => '1',
            'perms' => '$user->rights->smi->indicateurs',
            'target' => '',
            'user' => 2
        );
        $r++;

        // === SOUS-MENUS INDICATEURS KPI ===

        // Nouvel indicateur (sous-menu d'Indicateurs KPI)
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi,fk_leftmenu=smi_indicators',
            'type' => 'left',
            'titre' => 'Nouvel indicateur',
            'prefix' => img_picto('', 'add@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_indicators_new',
            'url' => '/custom/smi/indicators/card.php?action=create',
            'langs' => 'smi@smi',
            'position' => 1301,
            'enabled' => '1',
            'perms' => '$user->rights->smi->creer',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Saisie indicateur (sous-menu d'Indicateurs KPI)
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi,fk_leftmenu=smi_indicators',
            'type' => 'left',
            'titre' => 'Saisie indicateur',
            'prefix' => img_picto('', 'edit@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_indicators_values',
            'url' => '/custom/smi/indicators/values.php',
            'langs' => 'smi@smi',
            'position' => 1302,
            'enabled' => '1',
            'perms' => '$user->rights->smi->creer',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Tableau de bord (sous-menu d'Indicateurs KPI)
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi,fk_leftmenu=smi_indicators',
            'type' => 'left',
            'titre' => 'Tableau de bord',
            'prefix' => img_picto('', 'dashboard@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_indicators_dashboard',
            'url' => '/custom/smi/indicators/dashboard.php',
            'langs' => 'smi@smi',
            'position' => 1303,
            'enabled' => '1',
            'perms' => '$user->rights->smi->lire',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Documentation QSE
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi',
            'type' => 'left',
            'titre' => 'Documentation QSE',
            'prefix' => img_picto('', 'documents@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_documents',
            'url' => '/custom/smi/documents/index.php',
            'langs' => 'smi@smi',
            'position' => 1004,
            'enabled' => '1',
            'perms' => '$user->rights->smi->documents',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Rapports de conformité
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi',
            'type' => 'left',
            'titre' => 'Rapports de conformité',
            'prefix' => img_picto('', 'reports@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_reports',
            'url' => '/custom/smi/reports/index.php',
            'langs' => 'smi@smi',
            'position' => 1005,
            'enabled' => '1',
            'perms' => '$user->rights->smi->rapports',
            'target' => '',
            'user' => 2
        );
        $r++;

        // === SOUS-MENUS DE DOCUMENTATION QSE ===

        // Workflow Documentaire (sous-menu de Documentation QSE)
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi,fk_leftmenu=smi_documents',
            'type' => 'left',
            'titre' => 'Workflow Documentaire',
            'prefix' => img_picto('', 'workflow@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_iso_workflow',
            'url' => '/custom/smi/iso_workflow_interface.php',
            'langs' => 'smi@smi',
            'position' => 1101,
            'enabled' => '1',
            'perms' => '$user->rights->smi->documents',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Tableau de Bord Documentaire (sous-menu de Documentation QSE)
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi,fk_leftmenu=smi_documents',
            'type' => 'left',
            'titre' => 'Tableau de Bord Documentaire',
            'prefix' => img_picto('', 'dashboard@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_iso_dashboard',
            'url' => '/custom/smi/iso_document_dashboard.php',
            'langs' => 'smi@smi',
            'position' => 1102,
            'enabled' => '1',
            'perms' => '$user->rights->smi->documents',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Vérificateur Conformité (sous-menu de Documentation QSE)
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=smi,fk_leftmenu=smi_documents',
            'type' => 'left',
            'titre' => 'Vérificateur Conformité',
            'prefix' => img_picto('', 'compliance@smi', 'class="paddingright pictofixedwidth"'),
            'mainmenu' => 'smi',
            'leftmenu' => 'smi_iso_compliance',
            'url' => '/custom/smi/iso_compliance_checker.php',
            'langs' => 'smi@smi',
            'position' => 1103,
            'enabled' => '1',
            'perms' => '$user->rights->smi->configurer',
            'target' => '',
            'user' => 2
        );
        $r++;
    }

    /**
     * Get list of available ISO standards
     *
     * @return array Array of ISO standards
     */
    public function getISOStandards()
    {
        return array(
            array(
                'code' => 'ISO 9001',
                'label' => 'ISO 9001:2015',
                'description' => 'Systèmes de management de la qualité - Exigences',
                'domain' => 'Qualité'
            ),
            array(
                'code' => 'ISO 14001',
                'label' => 'ISO 14001:2015',
                'description' => 'Systèmes de management environnemental - Exigences et lignes directrices pour son utilisation',
                'domain' => 'Environnement'
            ),
            array(
                'code' => 'ISO 45001',
                'label' => 'ISO 45001:2018',
                'description' => 'Systèmes de management de la santé et de la sécurité au travail - Exigences et lignes directrices pour leur utilisation',
                'domain' => 'Sécurité'
            ),
            array(
                'code' => 'ISO 22000',
                'label' => 'ISO 22000:2018',
                'description' => 'Systèmes de management de la sécurité des denrées alimentaires - Exigences pour tout organisme appartenant à la chaîne alimentaire',
                'domain' => 'Sécurité alimentaire'
            ),
            array(
                'code' => 'ISO 50001',
                'label' => 'ISO 50001:2018',
                'description' => 'Systèmes de management de l\'énergie - Exigences et recommandations de mise en œuvre',
                'domain' => 'Énergie'
            ),
            array(
                'code' => 'ISO 27001',
                'label' => 'ISO 27001:2022',
                'description' => 'Technologies de l\'information - Techniques de sécurité - Systèmes de management de la sécurité de l\'information - Exigences',
                'domain' => 'Sécurité informatique'
            ),
            array(
                'code' => 'ISO 26000',
                'label' => 'ISO 26000:2010',
                'description' => 'Lignes directrices relatives à la responsabilité sociétale',
                'domain' => 'Responsabilité sociétale'
            )
        );
    }

    /**
     * Function called when module is enabled.
     * The init function add constants, boxes, permissions and menus (defined in constructor) into Dolibarr database.
     * It also creates data directories
     *
     * @param string $options Options when enabling module ('', 'noboxes')
     * @return int             1 if OK, 0 if KO
     */
    public function init($options = '')
    {
        global $conf, $langs;

        // Remove permissions and default values
        $this->remove($options);

        $sql = array();

        // Document templates
        $moduledir = dol_buildpath('/custom/smi', 0);
        $myTmpObjects = array();
        $myTmpObjects['SMIIndicator'] = array('includerefgeneration' => 0, 'includedocgeneration' => 0);
        $myTmpObjects['SMIProcess'] = array('includerefgeneration' => 0, 'includedocgeneration' => 0);
        $myTmpObjects['SMIAction'] = array('includerefgeneration' => 0, 'includedocgeneration' => 0);

        foreach ($myTmpObjects as $myTmpObjectKey => $myTmpObjectArray) {
            if ($myTmpObjectKey == 'SMIIndicator') {
                $src = DOL_DOCUMENT_ROOT.'/install/doctemplates/smiindicators/template_smiindicators.odt';
                $dirodt = DOL_DATA_ROOT.'/doctemplates/smiindicators';
                $dest = $dirodt.'/template_smiindicators.odt';
            }
            if ($myTmpObjectKey == 'SMIProcess') {
                $src = DOL_DOCUMENT_ROOT.'/install/doctemplates/smiprocesses/template_smiprocesses.odt';
                $dirodt = DOL_DATA_ROOT.'/doctemplates/smiprocesses';
                $dest = $dirodt.'/template_smiprocesses.odt';
            }
            if ($myTmpObjectKey == 'SMIAction') {
                $src = DOL_DOCUMENT_ROOT.'/install/doctemplates/smiactions/template_smiactions.odt';
                $dirodt = DOL_DATA_ROOT.'/doctemplates/smiactions';
                $dest = $dirodt.'/template_smiactions.odt';
            }

            if (file_exists($src) && !file_exists($dest)) {
                require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
                dol_mkdir($dirodt);
                $result = dol_copy($src, $dest, 0, 0);
                if ($result < 0) {
                    $langs->load("errors");
                    $this->error = $langs->trans('ErrorFailToCopyFile', $src, $dest);
                    return 0;
                }
            }
        }

        return $this->_init($sql, $options);
    }

    /**
     * Function called when module is disabled.
     * Remove from database constants, boxes and permissions from Dolibarr database.
     * Data directories are not deleted
     *
     * @param string $options Options when enabling module ('', 'noboxes')
     * @return int             1 if OK, 0 if KO
     */
    public function remove($options = '')
    {
        $sql = array();
        return $this->_remove($sql, $options);
    }

}