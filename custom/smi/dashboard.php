<?php
/* Module SMI - Système de Management Intégré
 * Copyright (C) 2025 Sinedtyi
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 */

/**
 * \file        dashboard.php
 * \ingroup     smi
 * \brief       SMI Dashboard page
 */

// Load Dolibarr environment
$res = @include("../main.inc.php"); // From htdocs directory
if (! $res) {
    $res = @include("../../main.inc.php"); // From "custom" directory
}

require_once 'lib/smi.lib.php';

// Libraries
require_once DOL_DOCUMENT_ROOT . "/core/lib/admin.lib.php";
require_once DOL_DOCUMENT_ROOT . "/core/lib/date.lib.php";
require_once './lib/smi.lib.php';

// Translations
$langs->loadLangs(array("admin", "smi@smi"));

// Access control
if (!$user->rights->smi->lire) {
    accessforbidden();
}

// Parameters
$action = GETPOST('action', 'aZ09');

/*
 * Actions
 */

/*
 * View
 */

$page_name = "SMIDashboard";
llxHeader('', $langs->trans($page_name), '', '', 0, 0, '', '', '', 'mod-smi page-dashboard');

// Page header
print '<div class="fiche">';
print '<div class="titre inline-block">';
print load_fiche_titre($langs->trans($page_name), '', 'fa-dashboard');
print '</div>';
print '</div>';

// Welcome message
print '<div class="smi-widget" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; margin-bottom: 30px;">';
print '<h2 style="margin: 0; font-size: 2em;">' . $langs->trans("WelcomeToSMI") . '</h2>';
print '<p style="font-size: 1.2em; margin: 10px 0 0 0; opacity: 0.9;">Système de Management Intégré QSE</p>';
print '</div>';

// Get statistics
$stats = smiGetStatistics();
$kpis = smiGetKPIData();

// KPI Dashboard
print '<div class="smi-kpi-grid">';

// Quality KPI
if (isset($kpis['quality_conformity_rate'])) {
    print '<div class="smi-kpi-card" style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);">';
    print '<div class="smi-kpi-value">' . $kpis['quality_conformity_rate'] . '%</div>';
    print '<div class="smi-kpi-label">Taux de Conformité</div>';

    // Dynamic trend calculation
    $trend_value = isset($kpis['quality_conformity_trend']) ? $kpis['quality_conformity_trend'] : 0;
    $trend_class = 'stable';
    $trend_icon = '→';
    $trend_text = 'Stable';

    if ($trend_value > 0) {
        $trend_class = 'up';
        $trend_icon = '↗';
        $trend_text = '+' . abs($trend_value) . '% ce mois';
    } elseif ($trend_value < 0) {
        $trend_class = 'down';
        $trend_icon = '↘';
        $trend_text = '-' . abs($trend_value) . '% ce mois';
    } else {
        $trend_text = 'Aucune variation';
    }

    print '<div class="smi-kpi-trend ' . $trend_class . '">' . $trend_icon . ' ' . $trend_text . '</div>';
    print '</div>';
}

// Safety KPI
if (isset($kpis['safety_incidents'])) {
    $trend_class = $kpis['safety_incidents'] == 0 ? 'stable' : 'down';
    $trend_text = $kpis['safety_incidents'] == 0 ? '→ Aucun incident' : '↘ ' . $kpis['safety_incidents'] . ' incidents';
    print '<div class="smi-kpi-card" style="background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);">';
    print '<div class="smi-kpi-value">' . $kpis['safety_incidents'] . '</div>';
    print '<div class="smi-kpi-label">Incidents Sécurité</div>';
    print '<div class="smi-kpi-trend ' . $trend_class . '">' . $trend_text . '</div>';
    print '</div>';
}

// Training KPI
if (isset($kpis['training_completion_rate'])) {
    print '<div class="smi-kpi-card" style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);">';
    print '<div class="smi-kpi-value">' . $kpis['training_completion_rate'] . '%</div>';
    print '<div class="smi-kpi-label">Formations Réalisées</div>';

    // Dynamic trend calculation for training
    $training_trend = isset($kpis['training_completion_trend']) ? $kpis['training_completion_trend'] : 0;
    $trend_class = 'stable';
    $trend_icon = '→';
    $trend_text = 'Stable';

    if ($training_trend > 0) {
        $trend_class = 'up';
        $trend_icon = '↗';
        $trend_text = '+' . abs($training_trend) . '% ce mois';
    } elseif ($training_trend < 0) {
        $trend_class = 'down';
        $trend_icon = '↘';
        $trend_text = '-' . abs($training_trend) . '% ce mois';
    } else {
        $trend_text = 'Aucune variation';
    }

    print '<div class="smi-kpi-trend ' . $trend_class . '">' . $trend_icon . ' ' . $trend_text . '</div>';
    print '</div>';
}

// Actions KPI
print '<div class="smi-kpi-card" style="background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);">';
print '<div class="smi-kpi-value">' . ($stats['pending_actions'] ?? 0) . '</div>';
print '<div class="smi-kpi-label">Actions en Cours</div>';
if (($stats['overdue_actions'] ?? 0) > 0) {
    print '<div class="smi-kpi-trend down">⚠ ' . $stats['overdue_actions'] . ' en retard</div>';
} else {
    print '<div class="smi-kpi-trend stable">→ Toutes à jour</div>';
}
print '</div>';

print '</div>';

// Main dashboard widgets
print '<div class="smi-dashboard">';

// Quality widget
if (smiIsModuleEnabled('quality')) {
    print '<div class="smi-widget">';
    print '<div class="smi-widget-header">';
    print '<span class="smi-widget-icon">🏭</span>';
    print '<h3 class="smi-widget-title">' . $langs->trans("QualityIndicators") . '</h3>';
    print '</div>';
    print '<div class="smi-widget-content">';
    print '<p><strong>Contrôles qualité ce mois :</strong> 45</p>';
    print '<p><strong>Non-conformités :</strong> 3</p>';
    print '<p><strong>CAPA ouvertes :</strong> 2</p>';
    print '<div style="margin-top: 15px;">';
    print '<a href="' . dol_buildpath('/quality/quality_control_list.php', 1) . '" class="button">';
    print 'Voir les contrôles qualité</a>';
    print '</div>';
    print '</div>';
    print '</div>';
}

// Safety widget
if (smiIsModuleEnabled('digiriskdolibarr')) {
    print '<div class="smi-widget">';
    print '<div class="smi-widget-header">';
    print '<span class="smi-widget-icon">🛡️</span>';
    print '<h3 class="smi-widget-title">' . $langs->trans("SafetyIndicators") . '</h3>';
    print '</div>';
    print '<div class="smi-widget-content">';
    print '<p><strong>Risques identifiés :</strong> 23</p>';
    print '<p><strong>Risques critiques :</strong> 2</p>';
    print '<p><strong>Actions préventives :</strong> 8</p>';
    print '<div style="margin-top: 15px;">';
    print '<a href="' . dol_buildpath('/digiriskdolibarr/digiriskdolibarrindex.php', 1) . '" class="button">';
    print 'Voir les risques</a>';
    print '</div>';
    print '</div>';
    print '</div>';
}

// Training widget
if (smiIsModuleEnabled('planformation')) {
    print '<div class="smi-widget">';
    print '<div class="smi-widget-header">';
    print '<span class="smi-widget-icon">📚</span>';
    print '<h3 class="smi-widget-title">Formations</h3>';
    print '</div>';
    print '<div class="smi-widget-content">';
    print '<p><strong>Sessions planifiées :</strong> 12</p>';
    print '<p><strong>Sessions réalisées :</strong> 8</p>';
    print '<p><strong>Participants formés :</strong> 156</p>';
    print '<div style="margin-top: 15px;">';
    print '<a href="' . dol_buildpath('/planformation/session.php', 1) . '" class="button">';
    print 'Voir les formations</a>';
    print '</div>';
    print '</div>';
    print '</div>';
}

// Job positions widget
print '<div class="smi-widget">';
print '<div class="smi-widget-header">';
print '<span class="smi-widget-icon">👥</span>';
print '<h3 class="smi-widget-title">Fiches de Poste</h3>';
print '</div>';
print '<div class="smi-widget-content">';
print '<p><strong>Postes définis :</strong> 24</p>';
print '<p><strong>Postes à jour :</strong> 20</p>';
print '<p><strong>Postes à réviser :</strong> 4</p>';
print '<div style="margin-top: 15px;">';
print '<a href="' . dol_buildpath('/hrm/job_list.php?mainmenu=hrm&leftmenu=hrm_sm', 1) . '" class="button">';
print 'Fiche de poste</a> ';
print '<a href="' . dol_buildpath('/hrm/evaluation_list.php?mainmenu=hrm&leftmenu=hrm_sm', 1) . '" class="button">';
print 'Compétence</a> ';
print '<a href="' . dol_buildpath('/hrm/compare.php?mainmenu=hrm&leftmenu=hrm_sm', 1) . '" class="button">';
print 'GPEC</a>';
print '</div>';
print '</div>';
print '</div>';

// Recent actions widget
print '<div class="smi-widget">';
print '<div class="smi-widget-header">';
print '<span class="smi-widget-icon">📋</span>';
print '<h3 class="smi-widget-title">' . $langs->trans("RecentActions") . '</h3>';
print '</div>';
print '<div class="smi-widget-content">';

// Get recent tickets/actions
$sql = "SELECT t.rowid, t.ref, t.subject, t.datec, t.fk_statut 
        FROM " . MAIN_DB_PREFIX . "ticket t 
        WHERE t.entity = " . $conf->entity . "
        ORDER BY t.datec DESC 
        LIMIT 5";
$resql = $db->query($sql);
if ($resql) {
    $num = $db->num_rows($resql);
    if ($num > 0) {
        print '<div class="smi-action-list">';
        while ($obj = $db->fetch_object($resql)) {
            print '<div class="smi-action-item">';
            print '<div class="smi-action-info">';
            print '<div class="smi-action-title">' . $obj->ref . ' - ' . $obj->subject . '</div>';
            print '<div class="smi-action-meta">Créé le ' . dol_print_date($db->jdate($obj->datec), 'day') . '</div>';
            print '</div>';
            $status_class = $obj->fk_statut == 8 ? 'completed' : ($obj->fk_statut == 1 ? 'inprogress' : 'draft');
            print '<div class="smi-action-status ' . $status_class . '">';
            print $obj->fk_statut == 8 ? 'Fermé' : ($obj->fk_statut == 1 ? 'En cours' : 'Ouvert');
            print '</div>';
            print '</div>';
        }
        print '</div>';
    } else {
        print '<p>Aucune action récente</p>';
    }
} else {
    print '<p>Erreur lors de la récupération des actions</p>';
}

print '<div style="margin-top: 15px;">';
print '<a href="' . dol_buildpath('/ticket/list.php', 1) . '" class="button">';
print 'Voir tous les tickets</a>';
print '</div>';
print '</div>';
print '</div>';

// Upcoming deadlines widget
print '<div class="smi-widget">';
print '<div class="smi-widget-header">';
print '<span class="smi-widget-icon">⏰</span>';
print '<h3 class="smi-widget-title">' . $langs->trans("UpcomingDeadlines") . '</h3>';
print '</div>';
print '<div class="smi-widget-content">';
print '<p><strong>Audit interne ISO 9001 :</strong> Dans 15 jours</p>';
print '<p><strong>Revue de direction :</strong> Dans 30 jours</p>';
print '<p><strong>Formation sécurité :</strong> Dans 7 jours</p>';
print '<div style="margin-top: 15px;">';
print '<a href="' . dol_buildpath('/smi/planning/calendar.php', 1) . '" class="button">';
print 'Voir le planning</a>';
print '</div>';
print '</div>';
print '</div>';

// ISO Document Management widget
print '<div class="smi-widget" style="border: 2px solid #1976D2; background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);">';
print '<div class="smi-widget-header">';
print '<span class="smi-widget-icon">📚</span>';
print '<h3 class="smi-widget-title" style="color: #1976D2;">Gestion Documentaire ISO</h3>';
print '</div>';
print '<div class="smi-widget-content">';

// Récupérer les statistiques des documents
$stats_docs = array();
$sql = "SELECT workflow_state, COUNT(*) as nb
        FROM " . MAIN_DB_PREFIX . "procedure_template
        WHERE entity = " . $conf->entity . "
        GROUP BY workflow_state";
$result = $db->query($sql);
if ($result) {
    while ($obj = $db->fetch_object($result)) {
        $stats_docs[$obj->workflow_state] = $obj->nb;
    }
}

$total_docs = array_sum($stats_docs);
$docs_draft = isset($stats_docs['DRAFT']) ? $stats_docs['DRAFT'] : 0;
$docs_review = isset($stats_docs['REVIEW']) ? $stats_docs['REVIEW'] : 0;
$docs_approved = isset($stats_docs['APPROVED']) ? $stats_docs['APPROVED'] : 0;
$docs_published = isset($stats_docs['PUBLISHED']) ? $stats_docs['PUBLISHED'] : 0;

// Récupérer le nombre d'étapes de workflow
$sql = "SELECT COUNT(*) as nb_steps FROM " . MAIN_DB_PREFIX . "procedure_template_step pts
        LEFT JOIN " . MAIN_DB_PREFIX . "procedure_template pt ON pts.fk_proceduretemplate = pt.rowid
        WHERE pt.entity = " . $conf->entity;
$result = $db->query($sql);
$nb_workflow_steps = 0;
if ($result) {
    $obj = $db->fetch_object($result);
    $nb_workflow_steps = $obj->nb_steps;
}

print '<div style="margin-bottom: 15px;">';
print '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 8px; margin-bottom: 15px;">';

// Statistiques des documents
print '<div style="background: white; padding: 8px; border-radius: 5px; text-align: center; border-left: 3px solid #FF9800;">';
print '<div style="font-size: 18px; font-weight: bold; color: #FF9800;">' . $docs_draft . '</div>';
print '<div style="font-size: 10px; color: #666;">BROUILLONS</div>';
print '</div>';

print '<div style="background: white; padding: 8px; border-radius: 5px; text-align: center; border-left: 3px solid #2196F3;">';
print '<div style="font-size: 18px; font-weight: bold; color: #2196F3;">' . $docs_review . '</div>';
print '<div style="font-size: 10px; color: #666;">EN RÉVISION</div>';
print '</div>';

print '<div style="background: white; padding: 8px; border-radius: 5px; text-align: center; border-left: 3px solid #4CAF50;">';
print '<div style="font-size: 18px; font-weight: bold; color: #4CAF50;">' . $docs_approved . '</div>';
print '<div style="font-size: 10px; color: #666;">APPROUVÉS</div>';
print '</div>';

print '<div style="background: white; padding: 8px; border-radius: 5px; text-align: center; border-left: 3px solid #9C27B0;">';
print '<div style="font-size: 18px; font-weight: bold; color: #9C27B0;">' . $docs_published . '</div>';
print '<div style="font-size: 10px; color: #666;">PUBLIÉS</div>';
print '</div>';

print '</div>';
print '</div>';

print '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 8px; margin-bottom: 15px;">';

print '<a href="' . dol_buildpath('/smi/iso_workflow_interface.php', 1) . '" class="button" style="background: #2196F3; color: white; text-align: center; padding: 10px; font-size: 12px;">';
print '🔄 Workflow Documentaire</a>';

print '<a href="' . dol_buildpath('/smi/iso_document_dashboard.php', 1) . '" class="button" style="background: #673AB7; color: white; text-align: center; padding: 10px; font-size: 12px;">';
print '📊 Tableau de Bord</a>';

print '<a href="' . dol_buildpath('/smi/iso_compliance_checker.php', 1) . '" class="button" style="background: #4CAF50; color: white; text-align: center; padding: 10px; font-size: 12px;">';
print '✅ Vérificateur Conformité</a>';

print '<a href="' . dol_buildpath('/smi/admin/setup.php?tab=iso_management', 1) . '" class="button" style="background: #FF9800; color: white; text-align: center; padding: 10px; font-size: 12px;">';
print '⚙️ Configuration ISO</a>';

print '</div>';

print '<div style="background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #4CAF50;">';
print '<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">';
print '<strong style="color: #4CAF50;">📈 Statistiques Workflow</strong>';
print '<span style="background: #E8F5E9; color: #4CAF50; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">';
print 'ACTIF';
print '</span>';
print '</div>';
// Récupérer les normes ISO activées
$enabled_iso_standards = smiGetEnabledISOStandardsString('short', '/');
if (empty($enabled_iso_standards)) {
    $enabled_iso_standards = 'Aucune norme activée';
}

print '<div style="font-size: 12px; color: #666; line-height: 1.4;">';
print '• <strong>' . $total_docs . '</strong> documents au total';
print ' • <strong>' . $nb_workflow_steps . '</strong> étapes de workflow';
print '<br>• <strong>Traçabilité complète</strong> des transitions';
print ' • <strong>Conformité ISO</strong> ' . $enabled_iso_standards;
print '</div>';
print '</div>';

print '</div>';
print '</div>';

// Quick links widget
print '<div class="smi-widget">';
print '<div class="smi-widget-header">';
print '<span class="smi-widget-icon">🔗</span>';
print '<h3 class="smi-widget-title">Accès Rapides</h3>';
print '</div>';
print '<div class="smi-widget-content">';
print '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px;">';

if (smiIsModuleEnabled('quality')) {
    print '<a href="' . dol_buildpath('/quality/card.php?action=create', 1) . '" class="button button-small">';
    print 'Nouveau contrôle</a>';
}

if (smiIsModuleEnabled('ticket')) {
    print '<a href="' . dol_buildpath('/ticket/card.php?action=create', 1) . '" class="button button-small">';
    print 'Nouveau ticket</a>';
}

if (smiIsModuleEnabled('digiriskdolibarr')) {
    print '<a href="' . dol_buildpath('/custom/digiriskdolibarr/view/digiriskelement/digiriskelement_risk.php?action=create', 1) . '" class="button button-small">';
    print 'Nouveau risque</a>';
}

print '<a href="' . dol_buildpath('/smi/processes/card.php?action=create', 1) . '" class="button button-small">';
print 'Nouveau processus</a>';

print '</div>';
print '</div>';
print '</div>';

print '</div>'; // End dashboard

// Page end
llxFooter();
$db->close();
