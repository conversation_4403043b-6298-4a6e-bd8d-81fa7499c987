<?php
/* Consomation Gazoil 
 * Copyright (C) 2013 florian <PERSON> <<EMAIL>>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

/**
 *	\file		lib/consogazoil.lib.php
 *	\ingroup	consogazoil
 *	\brief		This file is an example module library
 *				Put some comments here
 */

function consogazoilAdminPrepareHead()
{
    global $langs, $conf;

    $langs->load("consogazoil@consogazoil");

    $h = 0;
    $head = array();

    $head[$h][0] = dol_buildpath("/consogazoil/admin/admin_consogazoil.php", 1);
    $head[$h][1] = $langs->trans("ConsoGazoilSettings");
    $head[$h][2] = 'settings';
    $h++;
    $head[$h][0] = dol_buildpath("/consogazoil/admin/gazoilvehicule_extrafields.php", 1);
    $head[$h][1] = $langs->trans("ExtraFields").' '.$langs->trans('ConsoGazVehicule');
    $head[$h][2] = 'extrafieldsvehicule';
    $h++;
    $head[$h][0] = dol_buildpath("/consogazoil/admin/gazoilvehiculeservice_extrafields.php", 1);
    $head[$h][1] = $langs->trans("ExtraFields").' '.$langs->trans('ConsoGazVehiculeService');
    $head[$h][2] = 'extrafieldsvehiculeservice';
    $h++;
    $head[$h][0] = dol_buildpath("/consogazoil/admin/gazoilstation_extrafields.php", 1);
    $head[$h][1] = $langs->trans("ExtraFields").' '.$langs->trans('ConsoGazStation');
    $head[$h][2] = 'extrafieldsstation';
    $h++;
    $head[$h][0] = dol_buildpath("/consogazoil/admin/gazoilservice_extrafields.php", 1);
    $head[$h][1] = $langs->trans("ExtraFields").' '.$langs->trans('ConsoGazService');
    $head[$h][2] = 'extrafieldsservice';
    $h++;
    $head[$h][0] = dol_buildpath("/consogazoil/admin/gazoildriver_extrafields.php", 1);
    $head[$h][1] = $langs->trans("ExtraFields").' '.$langs->trans('ConsoGazDriver');
    $head[$h][2] = 'extrafieldsdriver';
    $h++;
    $head[$h][0] = dol_buildpath("/consogazoil/admin/gazoilvehtake_extrafields.php", 1);
    $head[$h][1] = $langs->trans("ExtraFields").' '.$langs->trans('ConsoGazTake');
    $head[$h][2] = 'extrafieldsvehtake';
    $h++;
    $head[$h][0] = dol_buildpath("/consogazoil/admin/admin_consogazoil.php", 1) . '?tab=integrations';
    $head[$h][1] = $langs->trans("ConsoGazIntegrationsConfig");
    $head[$h][2] = 'integrations';
    $h++;
    $head[$h][0] = dol_buildpath("/consogazoil/admin/about.php", 1);
    $head[$h][1] = $langs->trans("About");
    $head[$h][2] = 'about';
    $h++;

    // Show more tabs from modules
    // Entries must be declared in modules descriptor with line
    //$this->tabs = array(
    //	'entity:+tabname:Title:@consogazoil:/consogazoil/mypage.php?id=__ID__'
    //); // to add new tab
    //$this->tabs = array(
    //	'entity:-tabname:Title:@consogazoil:/consogazoil/mypage.php?id=__ID__'
    //); // to remove a tab
    complete_head_from_modules($conf, $langs, $object, $head, $h, 'consogazoiladmin');

    return $head;
}

function vehicule_prepare_head($object) {
	
	global $langs, $conf;
	
	$langs->load("consogazoil@consogazoil");
	
	$h = 0;
	$head = array();
	
	$head[$h][0] = dol_buildpath("/consogazoil/vehicule/card.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("ConsoGazManageVeh");
	$head[$h][2] = 'card';
	$h++;
	
	$head[$h][0] = dol_buildpath('/consogazoil/vehicule/info.php',1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Info");
	$head[$h][2] = 'info';
	$hselected = $h;
	$h++;
	
	complete_head_from_modules($conf, $langs, $object, $head, $h, 'consogazoilvehicule');
	
	return $head;
}

function service_prepare_head($object) {

	global $langs, $conf;

	$langs->load("consogazoil@consogazoil");

	$h = 0;
	$head = array();

	$head[$h][0] = dol_buildpath("/consogazoil/service/card.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("ConsoGazManageServ");
	$head[$h][2] = 'card';
	$h++;

	$head[$h][0] = dol_buildpath('/consogazoil/service/info.php',1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Info");
	$head[$h][2] = 'info';
	$hselected = $h;
	$h++;

	complete_head_from_modules($conf, $langs, $object, $head, $h, 'consogazoilservice');

	return $head;
}

function station_prepare_head($object) {

	global $langs, $conf;

	$langs->load("consogazoil@consogazoil");

	$h = 0;
	$head = array();

	$head[$h][0] = dol_buildpath("/consogazoil/station/card.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("ConsoGazManageSta");
	$head[$h][2] = 'card';
	$h++;

	$head[$h][0] = dol_buildpath('/consogazoil/station/info.php',1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Info");
	$head[$h][2] = 'info';
	$hselected = $h;
	$h++;

	complete_head_from_modules($conf, $langs, $object, $head, $h, 'consogazoilstation');

	return $head;
}

function driver_prepare_head($object) {

	global $langs, $conf;

	$langs->load("consogazoil@consogazoil");

	$h = 0;
	$head = array();

	$head[$h][0] = dol_buildpath("/consogazoil/driver/card.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("ConsoGazManageDriv");
	$head[$h][2] = 'card';
	$h++;

	$head[$h][0] = dol_buildpath('/consogazoil/driver/info.php',1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Info");
	$head[$h][2] = 'info';
	$hselected = $h;
	$h++;

	complete_head_from_modules($conf, $langs, $object, $head, $h, 'consogazoildriver');

	return $head;
}

function take_prepare_head($object) {

	global $langs, $conf;

	$langs->load("consogazoil@consogazoil");

	$h = 0;
	$head = array();

	$head[$h][0] = dol_buildpath("/consogazoil/take/card.php", 1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("ConsoGazManageTake");
	$head[$h][2] = 'card';
	$h++;

	$head[$h][0] = dol_buildpath('/consogazoil/take/info.php',1).'?id='.$object->id;
	$head[$h][1] = $langs->trans("Info");
	$head[$h][2] = 'info';
	$hselected = $h;
	$h++;

	complete_head_from_modules($conf, $langs, $object, $head, $h, 'consogazoiltake');

	return $head;
}

/**
 * Get CO2 emission factor from configuration
 *
 * @return float CO2 emission factor in kg per liter
 */
function getConsogazoilCO2Factor()
{
    global $conf;

    // Default value: 2.3 kg CO2 per liter of diesel
    $default_factor = 2.3;

    // Check if custom factor is configured
    if (!empty($conf->global->CONSOGAZOIL_CO2_FACTOR)) {
        return (float) $conf->global->CONSOGAZOIL_CO2_FACTOR;
    }

    return $default_factor;
}

/**
 * Get reference consumption for efficiency calculation
 *
 * @return float Reference consumption in L/100km
 */
function getConsogazoilReferenceConsumption()
{
    global $conf;

    // Default value: 7 L/100km
    $default_consumption = 7.0;

    // Check if custom reference is configured
    if (!empty($conf->global->CONSOGAZOIL_REFERENCE_CONSUMPTION)) {
        return (float) $conf->global->CONSOGAZOIL_REFERENCE_CONSUMPTION;
    }

    return $default_consumption;
}

/**
 * Get efficiency thresholds for classification
 *
 * @return array Array with 'excellent', 'good', 'average', 'poor' thresholds
 */
function getConsogazoilEfficiencyThresholds()
{
    global $conf;

    // Default thresholds (L/100km)
    $default_thresholds = array(
        'excellent' => 6.0,  // <= 6.0 L/100km
        'good' => 8.0,       // <= 8.0 L/100km
        'average' => 10.0,   // <= 10.0 L/100km
        'poor' => 999        // > 10.0 L/100km
    );

    // Check if custom thresholds are configured
    $thresholds = array();
    $thresholds['excellent'] = !empty($conf->global->CONSOGAZOIL_THRESHOLD_EXCELLENT) ?
        (float) $conf->global->CONSOGAZOIL_THRESHOLD_EXCELLENT : $default_thresholds['excellent'];
    $thresholds['good'] = !empty($conf->global->CONSOGAZOIL_THRESHOLD_GOOD) ?
        (float) $conf->global->CONSOGAZOIL_THRESHOLD_GOOD : $default_thresholds['good'];
    $thresholds['average'] = !empty($conf->global->CONSOGAZOIL_THRESHOLD_AVERAGE) ?
        (float) $conf->global->CONSOGAZOIL_THRESHOLD_AVERAGE : $default_thresholds['average'];
    $thresholds['poor'] = $default_thresholds['poor'];

    return $thresholds;
}

/**
 * Classify efficiency based on consumption
 *
 * @param float $consumption Consumption in L/100km
 * @return string Classification: 'excellent', 'good', 'average', 'poor'
 */
function classifyEfficiency($consumption)
{
    $thresholds = getConsogazoilEfficiencyThresholds();

    if ($consumption <= $thresholds['excellent']) {
        return 'excellent';
    } elseif ($consumption <= $thresholds['good']) {
        return 'good';
    } elseif ($consumption <= $thresholds['average']) {
        return 'average';
    } else {
        return 'poor';
    }
}

