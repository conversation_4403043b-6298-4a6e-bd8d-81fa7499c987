<?php
/* Module ConsoGazoil - Analytics Report
 * Copyright (C) 2025 Sinedtyi
 * Advanced analytics with charts and KPIs
 */

$res = @include("../../main.inc.php");
if (!$res) $res = @include("../../../main.inc.php");
if (!$res) die("Include of main fails");

require_once '../class/consogazoilvehtake.class.php';
require_once '../class/consogazoilvehicule.class.php';
require_once '../class/energyefficiency.class.php';
require_once '../class/co2_calculator.class.php';
require_once '../class/html.formconsogazoil.class.php';

// Security check
if (empty($user->rights->consogazoil->lire)) accessforbidden();

$langs->load('consogazoil@consogazoil');

// Get parameters
$period = GETPOST('period', 'alpha') ?: 'year';
$year = GETPOST('year', 'int') ?: date('Y');
$vehicle_filter = GETPOST('vehicle_filter', 'int');
$export = GETPOST('export', 'alpha');

// Initialize objects
$take = new ConsogazoilVehTake($db);
$vehicle = new Consogazoilvehicule($db);
$efficiency = new EnergyEfficiency($db);
$co2calc = new CO2Calculator($db);
$form = new FormConsoGazoil($db);

/*
 * Actions
 */

if ($export == 'excel') {
    // Export Excel logic here
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="analytics_' . date('Y-m-d') . '.xls"');
    // Export data...
    exit;
}

/*
 * View
 */

llxHeader('', $langs->trans("ConsoGazAnalytics"), '', '', '', '', array(), array(
    '/consogazoil/css/modern-design.css',
    '/consogazoil/css/gazoil.css'
));

?>

<div class="consogazoil-container">
    <!-- Header -->
    <div class="consogazoil-header fade-in">
        <h1>📊 <?php echo $langs->trans("ConsoGazAnalytics"); ?></h1>
        <div class="subtitle">Analytics avancés et indicateurs de performance</div>
    </div>

    <!-- Filtres -->
    <div class="modern-card fade-in">
        <div class="modern-card-header">
            <h3 class="modern-card-title">🔍 Filtres d'analyse</h3>
        </div>
        <div class="modern-card-body">
            <form method="GET" action="<?php echo $_SERVER['PHP_SELF']; ?>" class="d-flex gap-3 align-items-center flex-wrap">
                <div class="form-group mb-0">
                    <label class="form-label">Période</label>
                    <select name="period" class="form-control form-select">
                        <option value="month" <?php echo ($period == 'month') ? 'selected' : ''; ?>>Mensuel</option>
                        <option value="quarter" <?php echo ($period == 'quarter') ? 'selected' : ''; ?>>Trimestriel</option>
                        <option value="year" <?php echo ($period == 'year') ? 'selected' : ''; ?>>Annuel</option>
                    </select>
                </div>
                
                <div class="form-group mb-0">
                    <label class="form-label">Année</label>
                    <select name="year" class="form-control form-select">
                        <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                            <option value="<?php echo $y; ?>" <?php echo ($y == $year) ? 'selected' : ''; ?>>
                                <?php echo $y; ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                
                <div class="form-group mb-0">
                    <label class="form-label">Véhicule</label>
                    <?php echo $form->select_vehicule('vehicle_filter', $vehicle_filter, 1, 'Tous les véhicules'); ?>
                </div>
                
                <button type="submit" class="btn-modern btn-primary">
                    📈 Analyser
                </button>
                
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>?export=excel&period=<?php echo $period; ?>&year=<?php echo $year; ?>&vehicle_filter=<?php echo $vehicle_filter; ?>" 
                   class="btn-modern btn-success">
                    📊 Export Excel
                </a>
            </form>
        </div>
    </div>

    <?php
    // Get analytics data
    $analytics = getAnalyticsData($db, $period, $year, $vehicle_filter);
    ?>

    <!-- KPIs principaux -->
    <div class="dashboard-grid fade-in">
        <div class="dashboard-metric">
            <div class="metric-icon">⛽</div>
            <div class="metric-value"><?php echo number_format($analytics['total_fuel'], 0, ',', ' '); ?></div>
            <div class="metric-label">Litres consommés</div>
            <div class="metric-change <?php echo $analytics['fuel_trend'] >= 0 ? 'negative' : 'positive'; ?>">
                <?php echo ($analytics['fuel_trend'] >= 0 ? '+' : '') . number_format($analytics['fuel_trend'], 1); ?>%
            </div>
        </div>

        <div class="dashboard-metric">
            <div class="metric-icon">💰</div>
            <div class="metric-value"><?php echo number_format($analytics['total_cost'], 0, ',', ' '); ?></div>
            <div class="metric-label">Coût total (DT)</div>
            <div class="metric-change <?php echo $analytics['cost_trend'] >= 0 ? 'negative' : 'positive'; ?>">
                <?php echo ($analytics['cost_trend'] >= 0 ? '+' : '') . number_format($analytics['cost_trend'], 1); ?>%
            </div>
        </div>

        <div class="dashboard-metric">
            <div class="metric-icon">🌱</div>
            <div class="metric-value"><?php echo number_format($analytics['co2_emissions'], 0, ',', ' '); ?></div>
            <div class="metric-label">kg CO₂ émis</div>
            <div class="metric-change <?php echo $analytics['co2_trend'] <= 0 ? 'positive' : 'negative'; ?>">
                <?php echo ($analytics['co2_trend'] >= 0 ? '+' : '') . number_format($analytics['co2_trend'], 1); ?>%
            </div>
        </div>

        <div class="dashboard-metric">
            <div class="metric-icon">⚡</div>
            <div class="metric-value"><?php echo number_format($analytics['efficiency_score'], 0); ?>%</div>
            <div class="metric-label">Score efficacité</div>
            <div class="metric-change <?php echo $analytics['efficiency_trend'] >= 0 ? 'positive' : 'negative'; ?>">
                <?php echo ($analytics['efficiency_trend'] >= 0 ? '+' : '') . number_format($analytics['efficiency_trend'], 1); ?>%
            </div>
        </div>
    </div>

    <!-- Graphiques -->
    <div class="row fade-in">
        <div class="col-md-6">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">📈 Évolution de la consommation</h3>
                </div>
                <div class="modern-card-body">
                    <canvas id="consumptionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">💰 Évolution des coûts</h3>
                </div>
                <div class="modern-card-body">
                    <canvas id="costChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row fade-in">
        <div class="col-md-6">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">🌍 Émissions CO₂ par véhicule</h3>
                </div>
                <div class="modern-card-body">
                    <canvas id="co2Chart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">⚡ Efficacité énergétique</h3>
                </div>
                <div class="modern-card-body">
                    <canvas id="efficiencyChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau détaillé -->
    <div class="modern-card fade-in">
        <div class="modern-card-header">
            <h3 class="modern-card-title">📋 Analyse détaillée par véhicule</h3>
        </div>
        <div class="modern-card-body">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>Véhicule</th>
                        <th>Consommation (L)</th>
                        <th>Coût (DT)</th>
                        <th>L/100km</th>
                        <th>CO₂ (kg)</th>
                        <th>Efficacité</th>
                        <th>Tendance</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($analytics['vehicle_details'] as $detail): ?>
                        <tr>
                            <td>
                                <strong><?php echo $detail['immat']; ?></strong><br>
                                <small class="text-muted"><?php echo $detail['marque']; ?></small>
                            </td>
                            <td><?php echo number_format($detail['fuel'], 1); ?></td>
                            <td><?php echo number_format($detail['cost'], 0); ?></td>
                            <td>
                                <span class="badge badge-<?php echo $detail['efficiency_class']; ?>">
                                    <?php echo number_format($detail['consumption_per_100km'], 1); ?>
                                </span>
                            </td>
                            <td><?php echo number_format($detail['co2'], 0); ?></td>
                            <td>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: <?php echo $detail['efficiency_score']; ?>%"></div>
                                    <span class="progress-text"><?php echo $detail['efficiency_score']; ?>%</span>
                                </div>
                            </td>
                            <td>
                                <span class="trend-indicator <?php echo $detail['trend_class']; ?>">
                                    <?php echo $detail['trend_icon']; ?> <?php echo $detail['trend']; ?>%
                                </span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recommandations -->
    <?php if (!empty($analytics['recommendations'])): ?>
        <div class="modern-card fade-in">
            <div class="modern-card-header">
                <h3 class="modern-card-title">💡 Recommandations d'optimisation</h3>
            </div>
            <div class="modern-card-body">
                <?php foreach ($analytics['recommendations'] as $recommendation): ?>
                    <div class="alert alert-<?php echo $recommendation['type']; ?>">
                        <strong><?php echo $recommendation['title']; ?></strong><br>
                        <?php echo $recommendation['message']; ?>
                        <?php if (!empty($recommendation['action'])): ?>
                            <br><a href="<?php echo $recommendation['action_url']; ?>" class="btn-modern btn-sm btn-primary">
                                <?php echo $recommendation['action']; ?>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Chart.js pour les graphiques -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Configuration des graphiques
const chartConfig = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
        }
    },
    scales: {
        y: {
            beginAtZero: true
        }
    }
};

// Graphique de consommation
const consumptionCtx = document.getElementById('consumptionChart').getContext('2d');
new Chart(consumptionCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($analytics['chart_labels']); ?>,
        datasets: [{
            label: 'Consommation (L)',
            data: <?php echo json_encode($analytics['consumption_data']); ?>,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4
        }]
    },
    options: chartConfig
});

// Graphique des coûts
const costCtx = document.getElementById('costChart').getContext('2d');
new Chart(costCtx, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($analytics['chart_labels']); ?>,
        datasets: [{
            label: 'Coût (DT)',
            data: <?php echo json_encode($analytics['cost_data']); ?>,
            backgroundColor: 'rgba(34, 197, 94, 0.8)',
            borderColor: 'rgb(34, 197, 94)',
            borderWidth: 1
        }]
    },
    options: chartConfig
});

// Graphique CO2
const co2Ctx = document.getElementById('co2Chart').getContext('2d');
new Chart(co2Ctx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($analytics['vehicle_labels']); ?>,
        datasets: [{
            data: <?php echo json_encode($analytics['co2_data']); ?>,
            backgroundColor: [
                '#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6',
                '#f97316', '#06b6d4', '#84cc16', '#ec4899', '#6b7280'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'right',
            }
        }
    }
});

// Graphique d'efficacité
const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
new Chart(efficiencyCtx, {
    type: 'radar',
    data: {
        labels: <?php echo json_encode($analytics['efficiency_labels']); ?>,
        datasets: [{
            label: 'Score d\'efficacité',
            data: <?php echo json_encode($analytics['efficiency_data']); ?>,
            borderColor: 'rgb(168, 85, 247)',
            backgroundColor: 'rgba(168, 85, 247, 0.2)',
            pointBackgroundColor: 'rgb(168, 85, 247)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgb(168, 85, 247)'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            r: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});
</script>

<?php

/**
 * Get analytics data for the dashboard
 */
function getAnalyticsData($db, $period, $year, $vehicle_filter = 0)
{
    global $conf;

    // Initialize return data
    $data = array(
        'total_fuel' => 0,
        'fuel_trend' => 0,
        'total_cost' => 0,
        'cost_trend' => 0,
        'co2_emissions' => 0,
        'co2_trend' => 0,
        'efficiency_score' => 0,
        'efficiency_trend' => 0,
        'chart_labels' => array(),
        'consumption_data' => array(),
        'cost_data' => array(),
        'vehicle_labels' => array(),
        'co2_data' => array(),
        'efficiency_labels' => array(),
        'efficiency_data' => array(),
        'vehicle_details' => array(),
        'recommendations' => array()
    );

    // Get current period data
    $current_data = getAnalyticsPeriodData($db, $period, $year, $vehicle_filter);
    $data['total_fuel'] = $current_data['total_fuel'];
    $data['total_cost'] = $current_data['total_cost'];

    // Get previous period for trend calculation
    $prev_year = ($period == 'year') ? $year - 1 : $year;
    $prev_data = getAnalyticsPeriodData($db, $period, $prev_year, $vehicle_filter);

    // Calculate trends
    if ($prev_data['total_fuel'] > 0) {
        $data['fuel_trend'] = (($current_data['total_fuel'] - $prev_data['total_fuel']) / $prev_data['total_fuel']) * 100;
    }
    if ($prev_data['total_cost'] > 0) {
        $data['cost_trend'] = (($current_data['total_cost'] - $prev_data['total_cost']) / $prev_data['total_cost']) * 100;
    }

    // Calculate CO2 emissions using configurable factor
    require_once DOL_DOCUMENT_ROOT.'/custom/consogazoil/lib/consogazoil.lib.php';
    $co2_factor = getConsogazoilCO2Factor();
    $data['co2_emissions'] = $current_data['total_fuel'] * $co2_factor;
    $prev_co2 = $prev_data['total_fuel'] * $co2_factor;
    if ($prev_co2 > 0) {
        $data['co2_trend'] = (($data['co2_emissions'] - $prev_co2) / $prev_co2) * 100;
    }

    // Calculate efficiency score (based on consumption vs theoretical)
    $data['efficiency_score'] = calculateEfficiencyScore($db, $year, $vehicle_filter);

    // Get monthly/quarterly data for charts
    $chart_data = getChartData($db, $period, $year, $vehicle_filter);
    $data['chart_labels'] = $chart_data['labels'];
    $data['consumption_data'] = $chart_data['consumption'];
    $data['cost_data'] = $chart_data['cost'];

    // Get vehicle comparison data
    $vehicle_data = getVehicleComparisonData($db, $year, $vehicle_filter);
    $data['vehicle_labels'] = array_column($vehicle_data, 'ref');
    $data['co2_data'] = array_column($vehicle_data, 'co2');
    $data['vehicle_details'] = $vehicle_data;

    // Generate recommendations
    $data['recommendations'] = generateRecommendations($db, $year, $vehicle_filter);

    return $data;
}

function getAnalyticsPeriodData($db, $period, $year, $vehicle_filter = 0)
{
    global $conf;

    $sql = "SELECT SUM(vt.qty_taken) as total_fuel, SUM(vt.price_total) as total_cost";
    $sql .= " FROM ".MAIN_DB_PREFIX."consogazoil_vehtake vt";
    $sql .= " WHERE YEAR(vt.date_take) = ".$year;
    $sql .= " AND vt.entity IN (".getEntity('consogazoil').")";

    if ($vehicle_filter > 0) {
        $sql .= " AND vt.fk_vehicule = ".$vehicle_filter;
    }

    $resql = $db->query($sql);
    if ($resql) {
        $obj = $db->fetch_object($resql);
        return array(
            'total_fuel' => $obj->total_fuel ?: 0,
            'total_cost' => $obj->total_cost ?: 0
        );
    }

    return array('total_fuel' => 0, 'total_cost' => 0);
}

function calculateEfficiencyScore($db, $year, $vehicle_filter = 0)
{
    // Simple efficiency calculation based on average consumption
    global $conf;

    $sql = "SELECT AVG(vt.qty_taken / NULLIF(vt.km_veh, 0)) as avg_consumption";
    $sql .= " FROM ".MAIN_DB_PREFIX."consogazoil_vehtake vt";
    $sql .= " WHERE YEAR(vt.date_take) = ".$year;
    $sql .= " AND vt.km_veh > 0";
    $sql .= " AND vt.entity IN (".getEntity('consogazoil').")";

    if ($vehicle_filter > 0) {
        $sql .= " AND vt.fk_vehicule = ".$vehicle_filter;
    }

    $resql = $db->query($sql);
    if ($resql) {
        $obj = $db->fetch_object($resql);
        $avg_consumption = $obj->avg_consumption ?: 0;

        // Score based on consumption (lower is better)
        // Using configurable reference consumption
        require_once DOL_DOCUMENT_ROOT.'/custom/consogazoil/lib/consogazoil.lib.php';
        $reference_consumption = getConsogazoilReferenceConsumption() / 100; // Convert to L/km
        if ($avg_consumption > 0) {
            $score = max(0, min(100, 100 - (($avg_consumption - $reference_consumption) * 1000)));
            return round($score);
        }
    }

    return 75; // Default score if no data
}

function getChartData($db, $period, $year, $vehicle_filter = 0)
{
    global $conf;

    $labels = array();
    $consumption = array();
    $cost = array();

    if ($period == 'month') {
        // Monthly data for current year
        for ($month = 1; $month <= 12; $month++) {
            $labels[] = date('M', mktime(0, 0, 0, $month, 1));

            $sql = "SELECT SUM(vt.qty_taken) as fuel, SUM(vt.price_total) as cost";
            $sql .= " FROM ".MAIN_DB_PREFIX."consogazoil_vehtake vt";
            $sql .= " WHERE YEAR(vt.date_take) = ".$year;
            $sql .= " AND MONTH(vt.date_take) = ".$month;
            $sql .= " AND vt.entity IN (".getEntity('consogazoil').")";

            if ($vehicle_filter > 0) {
                $sql .= " AND vt.fk_vehicule = ".$vehicle_filter;
            }

            $resql = $db->query($sql);
            if ($resql) {
                $obj = $db->fetch_object($resql);
                $consumption[] = $obj->fuel ?: 0;
                $cost[] = $obj->cost ?: 0;
            } else {
                $consumption[] = 0;
                $cost[] = 0;
            }
        }
    } else {
        // Quarterly data
        for ($quarter = 1; $quarter <= 4; $quarter++) {
            $labels[] = 'T'.$quarter;

            $start_month = ($quarter - 1) * 3 + 1;
            $end_month = $quarter * 3;

            $sql = "SELECT SUM(vt.qty_taken) as fuel, SUM(vt.price_total) as cost";
            $sql .= " FROM ".MAIN_DB_PREFIX."consogazoil_vehtake vt";
            $sql .= " WHERE YEAR(vt.date_take) = ".$year;
            $sql .= " AND MONTH(vt.date_take) BETWEEN ".$start_month." AND ".$end_month;
            $sql .= " AND vt.entity IN (".getEntity('consogazoil').")";

            if ($vehicle_filter > 0) {
                $sql .= " AND vt.fk_vehicule = ".$vehicle_filter;
            }

            $resql = $db->query($sql);
            if ($resql) {
                $obj = $db->fetch_object($resql);
                $consumption[] = $obj->fuel ?: 0;
                $cost[] = $obj->cost ?: 0;
            } else {
                $consumption[] = 0;
                $cost[] = 0;
            }
        }
    }

    return array(
        'labels' => $labels,
        'consumption' => $consumption,
        'cost' => $cost
    );
}

function getVehicleComparisonData($db, $year, $vehicle_filter = 0)
{
    global $conf;

    $vehicles = array();

    $sql = "SELECT v.rowid, v.ref, v.immat_veh, v.brand_veh, v.variant_veh,";
    $sql .= " SUM(vt.qty_taken) as total_fuel, SUM(vt.price_total) as total_cost,";
    $sql .= " COUNT(vt.rowid) as nb_takes";
    $sql .= " FROM ".MAIN_DB_PREFIX."consogazoil_vehicule v";
    $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."consogazoil_vehtake vt ON vt.fk_vehicule = v.rowid AND YEAR(vt.date_take) = ".$year;
    $sql .= " WHERE v.entity IN (".getEntity('consogazoil').")";
    $sql .= " AND v.activ = 1";

    if ($vehicle_filter > 0) {
        $sql .= " AND v.rowid = ".$vehicle_filter;
    }

    $sql .= " GROUP BY v.rowid";
    $sql .= " ORDER BY total_fuel DESC";
    $sql .= " LIMIT 10";

    $resql = $db->query($sql);
    if ($resql) {
        require_once DOL_DOCUMENT_ROOT.'/custom/consogazoil/lib/consogazoil.lib.php';
        $co2_factor = getConsogazoilCO2Factor();

        while ($obj = $db->fetch_object($resql)) {
            $co2 = ($obj->total_fuel ?: 0) * $co2_factor; // kg CO2 using configurable factor

            // Calculate consumption per 100km for efficiency classification
            $consumption_per_100km = 0;
            if ($obj->total_fuel > 0) {
                // Get km data for this vehicle
                $sql_km = "SELECT MIN(km_veh) as min_km, MAX(km_veh) as max_km
                          FROM ".MAIN_DB_PREFIX."consogazoil_vehtake
                          WHERE fk_vehicule = ".$obj->rowid." AND YEAR(date_take) = ".$year." AND km_veh > 0";
                $resql_km = $db->query($sql_km);
                if ($resql_km) {
                    $obj_km = $db->fetch_object($resql_km);
                    $km_diff = ($obj_km->max_km ?: 0) - ($obj_km->min_km ?: 0);
                    if ($km_diff > 0) {
                        $consumption_per_100km = ($obj->total_fuel / $km_diff) * 100;
                    }
                }
            }

            // Use configurable efficiency classification
            $efficiency_class_name = classifyEfficiency($consumption_per_100km);
            $efficiency_class_map = array(
                'excellent' => 'success',
                'good' => 'info',
                'average' => 'warning',
                'poor' => 'danger'
            );
            $efficiency_class = $efficiency_class_map[$efficiency_class_name];

            // Calculate efficiency score based on consumption
            $efficiency_score = 0;
            if ($consumption_per_100km > 0) {
                $reference = getConsogazoilReferenceConsumption();
                $efficiency_score = max(0, min(100, 100 - (($consumption_per_100km - $reference) * 5)));
            }

            $vehicles[] = array(
                'ref' => $obj->ref,
                'immat' => $obj->immat_veh,
                'marque' => $obj->brand_veh . ' ' . $obj->variant_veh,
                'fuel' => $obj->total_fuel ?: 0,
                'cost' => $obj->total_cost ?: 0,
                'co2' => $co2,
                'nb_takes' => $obj->nb_takes ?: 0,
                'consumption_per_100km' => $consumption_per_100km,
                'efficiency_score' => round($efficiency_score),
                'efficiency_class' => $efficiency_class,
                'trend_class' => 'neutral',
                'trend_icon' => '→',
                'trend' => '0'
            );
        }
    }

    return $vehicles;
}

function generateRecommendations($db, $year, $vehicle_filter = 0)
{
    // Generate basic recommendations based on data
    $recommendations = array();

    // Check for high consumption vehicles
    global $conf;

    $sql = "SELECT v.ref, v.immat_veh, SUM(vt.qty_taken) as total_fuel";
    $sql .= " FROM ".MAIN_DB_PREFIX."consogazoil_vehicule v";
    $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."consogazoil_vehtake vt ON vt.fk_vehicule = v.rowid AND YEAR(vt.date_take) = ".$year;
    $sql .= " WHERE v.entity IN (".getEntity('consogazoil').")";
    $sql .= " AND v.activ = 1";
    $sql .= " GROUP BY v.rowid";
    $sql .= " HAVING total_fuel > 1000"; // High consumption threshold
    $sql .= " ORDER BY total_fuel DESC";
    $sql .= " LIMIT 3";

    $resql = $db->query($sql);
    if ($resql) {
        while ($obj = $db->fetch_object($resql)) {
            $recommendations[] = array(
                'type' => 'warning',
                'title' => 'Consommation élevée détectée',
                'message' => 'Le véhicule ' . $obj->immat_veh . ' (' . $obj->ref . ') présente une consommation élevée de ' . number_format($obj->total_fuel, 0) . ' litres.',
                'action' => 'Vérifier maintenance',
                'action_url' => '#'
            );
        }
    }

    // Add general recommendations if no specific issues
    if (empty($recommendations)) {
        $recommendations[] = array(
            'type' => 'info',
            'title' => 'Performance normale',
            'message' => 'Aucun problème majeur détecté. Continuez le suivi régulier.',
            'action' => 'Voir détails',
            'action_url' => '#'
        );
    }

    return $recommendations;
}

llxFooter();
$db->close();
