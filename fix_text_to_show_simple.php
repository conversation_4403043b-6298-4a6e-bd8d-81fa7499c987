<?php
/**
 * Script simple pour corriger le problème "A text to show"
 * À exécuter via l'interface web de Dolibarr
 */

// Vérification de sécurité
if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

// Vérification des droits admin
if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Correction A text to show</title></head><body>';
print '<h1>Correction du problème "A text to show"</h1>';

// 1. Diagnostic rapide
print '<h2>1. Diagnostic</h2>';

// Test de traduction
print '<p><strong>Test de traductions :</strong></p>';
print 'Product: ' . $langs->trans('Product') . '<br>';
print 'ThirdParty: ' . $langs->trans('ThirdParty') . '<br>';
print 'Reference: ' . $langs->trans('Reference') . '<br>';

// Vérifier les tables de traduction
print '<p><strong>Vérification des tables de traduction :</strong></p>';
$tables_to_check = array('overwrite_trans', 'c_translations');
foreach ($tables_to_check as $table) {
    $sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . $table . " WHERE transvalue LIKE '%text to show%'";
    $result = $db->query($sql);
    if ($result) {
        $obj = $db->fetch_object($result);
        print "Table {$table}: {$obj->nb} entrées problématiques<br>";
    } else {
        print "Table {$table}: n'existe pas ou erreur<br>";
    }
}

// 2. Correction automatique
print '<h2>2. Correction automatique</h2>';

if (isset($_GET['fix']) && $_GET['fix'] == '1') {
    print '<p><strong>Correction en cours...</strong></p>';
    
    // Supprimer les traductions corrompues
    foreach ($tables_to_check as $table) {
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . $table . " WHERE transvalue LIKE '%text to show%'";
        $result = $db->query($sql);
        if ($result) {
            $affected = $db->affected_rows($result);
            print "Table {$table}: {$affected} entrées supprimées<br>";
        }
    }
    
    // Corriger les produits avec libellés vides
    $sql = "UPDATE " . MAIN_DB_PREFIX . "product SET label = CONCAT('Produit ', ref) WHERE label = '' OR label IS NULL OR label = 'A text to show'";
    $result = $db->query($sql);
    if ($result) {
        $affected = $db->affected_rows($result);
        print "Produits corrigés: {$affected}<br>";
    }
    
    // Corriger les sociétés avec noms vides
    $sql = "UPDATE " . MAIN_DB_PREFIX . "societe SET nom = CONCAT('Société ', COALESCE(code_client, rowid)) WHERE nom = '' OR nom IS NULL OR nom = 'A text to show'";
    $result = $db->query($sql);
    if ($result) {
        $affected = $db->affected_rows($result);
        print "Sociétés corrigées: {$affected}<br>";
    }
    
    // Corriger les contacts avec noms vides
    $sql = "UPDATE " . MAIN_DB_PREFIX . "socpeople SET lastname = CONCAT('Contact ', rowid) WHERE (lastname = '' OR lastname IS NULL OR lastname = 'A text to show') AND firstname = ''";
    $result = $db->query($sql);
    if ($result) {
        $affected = $db->affected_rows($result);
        print "Contacts corrigés: {$affected}<br>";
    }
    
    // Vider le cache
    if (function_exists('dol_delete_dir_recursive')) {
        $cache_dir = DOL_DATA_ROOT . '/admin/temp';
        if (is_dir($cache_dir)) {
            dol_delete_dir_recursive($cache_dir);
            print "Cache vidé<br>";
        }
    }
    
    print '<p><strong style="color: green;">Correction terminée !</strong></p>';
    print '<p>Veuillez vous déconnecter et vous reconnecter pour voir les changements.</p>';
    
} else {
    print '<p><a href="?fix=1" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Lancer la correction</a></p>';
    print '<p><em>Cette action va :</em></p>';
    print '<ul>';
    print '<li>Supprimer les traductions corrompues contenant "text to show"</li>';
    print '<li>Corriger les libellés vides des produits, sociétés et contacts</li>';
    print '<li>Vider le cache de traductions</li>';
    print '</ul>';
}

// 3. Vérification post-correction
if (isset($_GET['fix']) && $_GET['fix'] == '1') {
    print '<h2>3. Vérification</h2>';
    
    // Vérifier quelques enregistrements
    $sql = "SELECT rowid, ref, label FROM " . MAIN_DB_PREFIX . "product WHERE label LIKE '%text to show%' LIMIT 5";
    $result = $db->query($sql);
    if ($result && $db->num_rows($result) > 0) {
        print '<p style="color: red;">Attention: Des produits ont encore des libellés problématiques:</p>';
        while ($obj = $db->fetch_object($result)) {
            print "Produit {$obj->rowid}: {$obj->ref} - {$obj->label}<br>";
        }
    } else {
        print '<p style="color: green;">Aucun produit avec libellé problématique trouvé.</p>';
    }
    
    $sql = "SELECT rowid, nom FROM " . MAIN_DB_PREFIX . "societe WHERE nom LIKE '%text to show%' LIMIT 5";
    $result = $db->query($sql);
    if ($result && $db->num_rows($result) > 0) {
        print '<p style="color: red;">Attention: Des sociétés ont encore des noms problématiques:</p>';
        while ($obj = $db->fetch_object($result)) {
            print "Société {$obj->rowid}: {$obj->nom}<br>";
        }
    } else {
        print '<p style="color: green;">Aucune société avec nom problématique trouvé.</p>';
    }
}

print '</body></html>';
?>
