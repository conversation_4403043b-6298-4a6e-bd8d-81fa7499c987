<?php
/**
 * Diagnostic ultra-profond du problème "A text to show"
 * Identification précise de la source du problème
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Debug Ultra-Profond "A text to show"</title></head><body>';
print '<h1>Diagnostic Ultra-Profond "A text to show"</h1>';

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'trace_translation_system':
            print '<h2>Traçage du Système de Traduction</h2>';
            
            // 1. Analyser le système de traduction en temps réel
            print '<h3>1. Test du système de traduction Dolibarr</h3>';
            
            global $langs, $conf;
            
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Test</th><th>Résultat</th><th>Détail</th></tr>';
            
            // Test de base
            $company_trans = $langs->trans('Company');
            $status = ($company_trans == 'A text to show') ? '❌ PROBLÈME' : '✅ OK';
            print "<tr><td>langs->trans('Company')</td><td>{$company_trans}</td><td>{$status}</td></tr>";
            
            // Test avec différentes clés
            $test_keys = array(
                'Product' => $langs->trans('Product'),
                'Invoice' => $langs->trans('Invoice'),
                'Order' => $langs->trans('Order'),
                'ThirdParty' => $langs->trans('ThirdParty'),
                'Contact' => $langs->trans('Contact'),
                'User' => $langs->trans('User'),
                'Menu' => $langs->trans('Menu'),
                'Home' => $langs->trans('Home')
            );
            
            $problematic_keys = array();
            foreach ($test_keys as $key => $translation) {
                $status = ($translation == 'A text to show') ? '❌ PROBLÈME' : '✅ OK';
                if ($translation == 'A text to show') {
                    $problematic_keys[] = $key;
                }
                print "<tr><td>langs->trans('{$key}')</td><td>{$translation}</td><td>{$status}</td></tr>";
            }
            print '</table>';
            
            if (!empty($problematic_keys)) {
                print '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>❌ Clés problématiques trouvées :</h4>';
                print '<p>' . implode(', ', $problematic_keys) . '</p>';
                print '</div>';
            }
            
            // 2. Analyser les fichiers de langue chargés
            print '<h3>2. Fichiers de langue chargés</h3>';
            
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Propriété</th><th>Valeur</th></tr>';
            print "<tr><td>Langue par défaut (\$langs->defaultlang)</td><td>{$langs->defaultlang}</td></tr>";
            print "<tr><td>Langue courante (\$conf->global->MAIN_LANG_DEFAULT)</td><td>" . ($conf->global->MAIN_LANG_DEFAULT ?? 'Non définie') . "</td></tr>";
            print "<tr><td>Entité courante (\$conf->entity)</td><td>{$conf->entity}</td></tr>";
            print "<tr><td>Entité utilisateur (\$user->entity)</td><td>{$user->entity}</td></tr>";
            print '</table>';
            
            // 3. Vérifier les fichiers de langue physiques
            print '<h3>3. Vérification des fichiers de langue</h3>';
            
            $lang_dirs = array(
                DOL_DOCUMENT_ROOT . '/langs/fr_FR',
                DOL_DOCUMENT_ROOT . '/langs/en_US'
            );
            
            foreach ($lang_dirs as $lang_dir) {
                if (is_dir($lang_dir)) {
                    $files = scandir($lang_dir);
                    $lang_files = array_filter($files, function($file) {
                        return pathinfo($file, PATHINFO_EXTENSION) === 'lang';
                    });
                    
                    print "<h4>Répertoire : {$lang_dir}</h4>";
                    print '<p>Fichiers .lang trouvés : ' . count($lang_files) . '</p>';
                    
                    // Vérifier le fichier main.lang
                    $main_lang_file = $lang_dir . '/main.lang';
                    if (file_exists($main_lang_file)) {
                        print '<p>✅ Fichier main.lang existe</p>';
                        
                        // Lire quelques lignes du fichier
                        $content = file_get_contents($main_lang_file);
                        if (strpos($content, 'Company=') !== false) {
                            preg_match('/Company=(.*)/', $content, $matches);
                            if (isset($matches[1])) {
                                print "<p>Contenu Company dans main.lang : <strong>{$matches[1]}</strong></p>";
                            }
                        } else {
                            print '<p>❌ Clé "Company" non trouvée dans main.lang</p>';
                        }
                    } else {
                        print '<p>❌ Fichier main.lang manquant</p>';
                    }
                } else {
                    print "<p>❌ Répertoire {$lang_dir} n'existe pas</p>";
                }
            }
            
            // 4. Analyser la table de traductions personnalisées
            print '<h3>4. Traductions personnalisées en base</h3>';
            
            $sql = "SELECT * FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE lang = 'fr_FR' ORDER BY transkey";
            $result = $db->query($sql);
            
            if ($result && $db->num_rows($result) > 0) {
                print '<div style="max-height: 300px; overflow-y: auto;">';
                print '<table border="1" style="border-collapse: collapse; width: 100%; font-size: 12px;">';
                print '<tr><th>Clé</th><th>Valeur</th><th>Entité</th><th>Problème</th></tr>';
                
                $overwrite_problems = 0;
                while ($obj = $db->fetch_object($result)) {
                    $problem = '';
                    if ($obj->value == 'A text to show') {
                        $problem = '❌ Valeur problématique';
                        $overwrite_problems++;
                    } elseif (empty($obj->value)) {
                        $problem = '⚠️ Valeur vide';
                        $overwrite_problems++;
                    } else {
                        $problem = '✅ OK';
                    }
                    
                    print "<tr><td>{$obj->transkey}</td><td>{$obj->value}</td><td>{$obj->entity}</td><td>{$problem}</td></tr>";
                }
                print '</table>';
                print '</div>';
                
                if ($overwrite_problems > 0) {
                    print "<p><strong>❌ {$overwrite_problems} traductions personnalisées problématiques trouvées</strong></p>";
                }
            } else {
                print '<p>✅ Aucune traduction personnalisée trouvée</p>';
            }
            
            // 5. Test de la fonction getTradFromKey
            print '<h3>5. Test de la fonction getTradFromKey</h3>';
            
            if (function_exists('getTradFromKey')) {
                $test_result = getTradFromKey($db, 'Company', 'fr_FR', $conf->entity);
                $status = ($test_result == 'A text to show') ? '❌ PROBLÈME' : '✅ OK';
                print "<p>getTradFromKey(\$db, 'Company', 'fr_FR', {$conf->entity}) = <strong>{$test_result}</strong> {$status}</p>";
            } else {
                print '<p>❌ Fonction getTradFromKey non disponible</p>';
            }
            
            // 6. Analyser les hooks et modules qui pourraient interférer
            print '<h3>6. Modules pouvant interférer avec les traductions</h3>';
            
            $interfering_modules = array(
                'MAIN_MODULE_MULTISOCIETE',
                'MAIN_MODULE_MULTILANGS',
                'MAIN_MODULE_TRANSLATION'
            );
            
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Module</th><th>Statut</th><th>Impact potentiel</th></tr>';
            
            foreach ($interfering_modules as $module) {
                $sql = "SELECT value FROM " . MAIN_DB_PREFIX . "const WHERE name = '{$module}' AND entity IN (0, {$conf->entity})";
                $result = $db->query($sql);
                $status = '❌ Non installé';
                $impact = '';
                
                if ($result && $obj = $db->fetch_object($result)) {
                    if ($obj->value == '1') {
                        $status = '✅ Activé';
                        if ($module == 'MAIN_MODULE_MULTISOCIETE') {
                            $impact = '⚠️ Peut causer des conflits d\'entités';
                        } elseif ($module == 'MAIN_MODULE_MULTILANGS') {
                            $impact = '⚠️ Peut affecter le système de traduction';
                        }
                    } else {
                        $status = '❌ Désactivé';
                    }
                }
                
                print "<tr><td>{$module}</td><td>{$status}</td><td>{$impact}</td></tr>";
            }
            print '</table>';
            
            break;
            
        case 'fix_translation_deep':
            print '<h2>Correction Profonde du Système de Traduction</h2>';
            
            $fixes = array();
            $errors = array();
            
            try {
                // 1. Nettoyer complètement les traductions corrompues
                print '<h3>1. Nettoyage des traductions corrompues</h3>';
                
                // Supprimer toutes les traductions "A text to show"
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE value = 'A text to show'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Traductions 'A text to show' supprimées ({$affected} entrées)";
                }
                
                // Supprimer les traductions vides
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE value = '' OR value IS NULL";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Traductions vides supprimées ({$affected} entrées)";
                }
                
                // 2. Forcer la langue par défaut dans toutes les entités
                print '<h3>2. Configuration forcée de la langue</h3>';
                
                $entities = array(0, 1, 2);
                foreach ($entities as $entity) {
                    $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                            VALUES ('MAIN_LANG_DEFAULT', 'fr_FR', 'chaine', 'Forced default language', '0', {$entity})
                            ON DUPLICATE KEY UPDATE value = 'fr_FR'";
                    if ($db->query($sql)) {
                        $fixes[] = "Langue fr_FR forcée pour entité {$entity}";
                    }
                }
                
                // 3. Désactiver temporairement le cache des traductions
                print '<h3>3. Désactivation du cache des traductions</h3>';
                
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%CACHE%TRANSLATION%'";
                if ($db->query($sql)) {
                    $fixes[] = "Cache des traductions désactivé";
                }
                
                // Forcer la désactivation du cache
                $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                        VALUES ('MAIN_DISABLE_TRANSLATION_CACHE', '1', 'chaine', 'Disable translation cache', '0', 0)
                        ON DUPLICATE KEY UPDATE value = '1'";
                if ($db->query($sql)) {
                    $fixes[] = "Cache des traductions désactivé de force";
                }
                
                // 4. Recréer les traductions de base si nécessaire
                print '<h3>4. Recréation des traductions de base</h3>';
                
                $base_translations = array(
                    'Company' => 'Société',
                    'Product' => 'Produit',
                    'Invoice' => 'Facture',
                    'Order' => 'Commande',
                    'Contact' => 'Contact',
                    'ThirdParty' => 'Tiers',
                    'User' => 'Utilisateur',
                    'Menu' => 'Menu',
                    'Home' => 'Accueil'
                );
                
                foreach ($base_translations as $key => $value) {
                    // Vérifier si la traduction existe et est correcte
                    global $langs;
                    $current_trans = $langs->trans($key);
                    
                    if ($current_trans == 'A text to show' || $current_trans == $key) {
                        // Créer une traduction personnalisée
                        foreach ($entities as $entity) {
                            $sql = "INSERT INTO " . MAIN_DB_PREFIX . "overwrite_trans (lang, transkey, value, entity) 
                                    VALUES ('fr_FR', '{$key}', '{$value}', {$entity})
                                    ON DUPLICATE KEY UPDATE value = '{$value}'";
                            if ($db->query($sql)) {
                                $fixes[] = "Traduction {$key} = {$value} créée pour entité {$entity}";
                            }
                        }
                    }
                }
                
                // 5. Forcer le rechargement des traductions
                print '<h3>5. Rechargement forcé des traductions</h3>';
                
                // Vider tous les caches liés aux traductions
                $cache_patterns = array(
                    '%CACHE%',
                    '%TRANSLATION%',
                    '%LANG%'
                );
                
                foreach ($cache_patterns as $pattern) {
                    $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '{$pattern}' AND name NOT LIKE 'MAIN_LANG_DEFAULT'";
                    if ($db->query($sql)) {
                        $affected = $db->affected_rows();
                        if ($affected > 0) {
                            $fixes[] = "Cache {$pattern} vidé ({$affected} entrées)";
                        }
                    }
                }
                
                // 6. Corriger les conflits d'entités pour les traductions
                print '<h3>6. Correction des conflits d\'entités</h3>';
                
                // S'assurer que l'utilisateur actuel a une entité cohérente
                global $user, $conf;
                
                if ($user->entity != $conf->entity) {
                    $fixes[] = "Conflit détecté : utilisateur entité {$user->entity}, conf entité {$conf->entity}";
                    
                    // Forcer la cohérence
                    $sql = "UPDATE " . MAIN_DB_PREFIX . "user SET entity = {$conf->entity} WHERE rowid = {$user->id}";
                    if ($db->query($sql)) {
                        $fixes[] = "Entité utilisateur synchronisée avec conf";
                    }
                }
                
                // 7. Test final des traductions
                print '<h3>7. Test final après correction</h3>';
                
                // Recharger les traductions
                $langs->load("main");
                
                $test_results = array();
                foreach ($base_translations as $key => $expected) {
                    $actual = $langs->trans($key);
                    $status = ($actual == 'A text to show') ? '❌ ENCORE PROBLÉMATIQUE' : '✅ CORRIGÉ';
                    $test_results[] = "{$key}: {$actual} {$status}";
                    
                    if ($actual == 'A text to show') {
                        $errors[] = "Traduction {$key} retourne encore 'A text to show'";
                    }
                }
                
                $fixes = array_merge($fixes, $test_results);
                
            } catch (Exception $e) {
                $errors[] = "Erreur: " . $e->getMessage();
            }
            
            // Affichage des résultats
            if (!empty($fixes)) {
                print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>✅ Corrections profondes effectuées :</h4>';
                print '<ul>';
                foreach ($fixes as $fix) {
                    print '<li>' . $fix . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            if (!empty($errors)) {
                print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>❌ Problèmes persistants :</h4>';
                print '<ul>';
                foreach ($errors as $error) {
                    print '<li>' . $error . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
            print '<h4 style="color: white;">🔄 Actions Suivantes</h4>';
            print '<ol style="color: white;">';
            print '<li><strong>Redémarrez MAMP complètement</strong></li>';
            print '<li><strong>Videz le cache de votre navigateur</strong></li>';
            print '<li><strong>Reconnectez-vous à Dolibarr</strong></li>';
            print '<li><strong>Testez le dashboard</strong></li>';
            print '<li><strong>Si le problème persiste, nous analyserons plus profondément</strong></li>';
            print '</ol>';
            print '</div>';
            
            break;
    }
} else {
    // Menu principal
    print '<h2>Diagnostic Ultra-Profond "A text to show"</h2>';
    
    print '<div style="background: #dc3545; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🔍 Le Problème Persiste</h3>';
    print '<p style="color: white;">Malgré les réparations, "A text to show" apparaît encore. Nous devons aller plus profond :</p>';
    print '<ul style="color: white;">';
    print '<li>❌ <strong>Système de traduction</strong> : Analyser le mécanisme interne</li>';
    print '<li>❌ <strong>Fichiers de langue</strong> : Vérifier l\'intégrité physique</li>';
    print '<li>❌ <strong>Cache des traductions</strong> : Identifier les corruptions</li>';
    print '<li>❌ <strong>Conflits d\'entités</strong> : Tracer les incohérences</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🔍 Traçage du Système de Traduction</h3>';
    print '<p style="color: white;">Diagnostic ultra-profond pour identifier la source exacte :</p>';
    print '<ul style="color: white;">';
    print '<li>🔧 <strong>Test en temps réel</strong> : Traductions problématiques</li>';
    print '<li>🔧 <strong>Fichiers de langue</strong> : Vérification physique</li>';
    print '<li>🔧 <strong>Traductions personnalisées</strong> : Analyse en base</li>';
    print '<li>🔧 <strong>Fonction getTradFromKey</strong> : Test direct</li>';
    print '<li>🔧 <strong>Modules interférents</strong> : Identification des conflits</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #28a745; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🛠️ Correction Profonde</h3>';
    print '<p style="color: white;">Solution radicale basée sur le diagnostic :</p>';
    print '<ul style="color: white;">';
    print '<li>🔧 <strong>Nettoyer traductions corrompues</strong> complètement</li>';
    print '<li>🔧 <strong>Forcer langue par défaut</strong> dans toutes les entités</li>';
    print '<li>🔧 <strong>Désactiver cache traductions</strong> temporairement</li>';
    print '<li>🔧 <strong>Recréer traductions de base</strong> si nécessaire</li>';
    print '<li>🔧 <strong>Corriger conflits d\'entités</strong> définitivement</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 20px 0;">';
    print '<a href="?action=trace_translation_system" style="background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin-right: 10px;">🔍 TRACER LE SYSTÈME</a>';
    print '<a href="?action=fix_translation_deep" style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px;">🔧 CORRECTION PROFONDE</a>';
    print '</div>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>💡 Stratégie de Diagnostic</h4>';
    print '<p>Puisque les corrections précédentes n\'ont pas résolu le problème, nous devons :</p>';
    print '<ul>';
    print '<li>✅ <strong>Tracer exactement</strong> où "A text to show" est généré</li>';
    print '<li>✅ <strong>Identifier les clés</strong> qui retournent cette valeur</li>';
    print '<li>✅ <strong>Vérifier l\'intégrité</strong> des fichiers de langue</li>';
    print '<li>✅ <strong>Analyser les conflits</strong> entre entités et cache</li>';
    print '</ul>';
    print '<p><strong>Commencez par le traçage</strong> pour voir exactement ce qui se passe.</p>';
    print '</div>';
}

print '</body></html>';
?>
