<?php
/**
 * Diagnostic et correction du module Multisociete
 * Résolution du problème "A text to show" causé par multisociete
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Correction Multisociete - A text to show</title></head><body>';
print '<h1>Diagnostic et Correction du Module Multisociete</h1>';

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'analyze_multisociete':
            print '<h2>Analyse du Module Multisociete</h2>';
            
            // 1. Vérifier le statut du module
            print '<h3>1. Statut du module Multisociete</h3>';
            
            $sql = "SELECT value FROM " . MAIN_DB_PREFIX . "const WHERE name = 'MAIN_MODULE_MULTISOCIETE'";
            $result = $db->query($sql);
            $module_status = 'Non installé';
            if ($result && $obj = $db->fetch_object($result)) {
                $module_status = ($obj->value == '1') ? '⚠️ ACTIVÉ (cause du problème)' : '✅ Désactivé';
            }
            print "<p><strong>Statut :</strong> {$module_status}</p>";
            
            // 2. Analyser les entités
            print '<h3>2. Analyse des entités</h3>';
            
            $sql = "SELECT DISTINCT entity FROM " . MAIN_DB_PREFIX . "const ORDER BY entity";
            $result = $db->query($sql);
            $entities = array();
            if ($result) {
                while ($obj = $db->fetch_object($result)) {
                    $entities[] = $obj->entity;
                }
            }
            
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Entité</th><th>Nombre de configurations</th><th>Problème potentiel</th></tr>';
            
            foreach ($entities as $entity) {
                $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "const WHERE entity = {$entity}";
                $result = $db->query($sql);
                $count = 0;
                if ($result && $obj = $db->fetch_object($result)) {
                    $count = $obj->count;
                }
                
                $problem = '';
                if ($entity == 0) {
                    $problem = '⚠️ Entité globale (peut causer des conflits)';
                } elseif ($entity > 1) {
                    $problem = '❌ Entité multi-société (cause "A text to show")';
                } else {
                    $problem = '✅ Entité normale';
                }
                
                print "<tr><td>Entité {$entity}</td><td>{$count}</td><td>{$problem}</td></tr>";
            }
            print '</table>';
            
            // 3. Vérifier les configurations problématiques
            print '<h3>3. Configurations problématiques</h3>';
            
            // Chercher les configurations avec entity > 1
            $sql = "SELECT name, value, entity FROM " . MAIN_DB_PREFIX . "const WHERE entity > 1 ORDER BY entity, name";
            $result = $db->query($sql);
            
            if ($result && $db->num_rows($result) > 0) {
                print '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;">';
                print '<h4>❌ Configurations multi-entités trouvées (causent "A text to show") :</h4>';
                print '<table border="1" style="border-collapse: collapse; width: 100%; font-size: 12px;">';
                print '<tr><th>Configuration</th><th>Valeur</th><th>Entité</th></tr>';
                
                $problematic_configs = 0;
                while ($obj = $db->fetch_object($result)) {
                    print "<tr><td>{$obj->name}</td><td>{$obj->value}</td><td>{$obj->entity}</td></tr>";
                    $problematic_configs++;
                }
                print '</table>';
                print "<p><strong>{$problematic_configs} configurations problématiques trouvées</strong></p>";
                print '</div>';
            } else {
                print '<p>✅ Aucune configuration multi-entité trouvée</p>';
            }
            
            // 4. Vérifier les tables avec entity
            print '<h3>4. Tables avec gestion multi-entités</h3>';
            
            $tables_with_entity = array(
                'societe', 'socpeople', 'product', 'facture', 'commande', 
                'propal', 'user', 'adherent', 'projet', 'contrat'
            );
            
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Table</th><th>Entités utilisées</th><th>Problème</th></tr>';
            
            foreach ($tables_with_entity as $table) {
                $sql = "SELECT DISTINCT entity FROM " . MAIN_DB_PREFIX . $table . " WHERE entity IS NOT NULL ORDER BY entity";
                $result = $db->query($sql);
                $table_entities = array();
                if ($result) {
                    while ($obj = $db->fetch_object($result)) {
                        $table_entities[] = $obj->entity;
                    }
                }
                
                $entities_str = implode(', ', $table_entities);
                $problem = '';
                
                if (empty($table_entities)) {
                    $problem = '✅ Pas de données';
                } elseif (count($table_entities) == 1 && $table_entities[0] == 1) {
                    $problem = '✅ Mono-entité normale';
                } else {
                    $problem = '❌ Multi-entités (cause "A text to show")';
                }
                
                print "<tr><td>{$table}</td><td>{$entities_str}</td><td>{$problem}</td></tr>";
            }
            print '</table>';
            
            // 5. Vérifier les traductions par entité
            print '<h3>5. Traductions par entité</h3>';
            
            $sql = "SELECT entity, COUNT(*) as count FROM " . MAIN_DB_PREFIX . "overwrite_trans GROUP BY entity ORDER BY entity";
            $result = $db->query($sql);
            
            if ($result && $db->num_rows($result) > 0) {
                print '<table border="1" style="border-collapse: collapse; width: 100%;">';
                print '<tr><th>Entité</th><th>Traductions personnalisées</th><th>Impact</th></tr>';
                
                while ($obj = $db->fetch_object($result)) {
                    $impact = '';
                    if ($obj->entity == 0) {
                        $impact = '⚠️ Traductions globales';
                    } elseif ($obj->entity == 1) {
                        $impact = '✅ Traductions normales';
                    } else {
                        $impact = '❌ Traductions multi-entités (causent "A text to show")';
                    }
                    
                    print "<tr><td>Entité {$obj->entity}</td><td>{$obj->count}</td><td>{$impact}</td></tr>";
                }
                print '</table>';
            } else {
                print '<p>✅ Aucune traduction personnalisée trouvée</p>';
            }
            
            // 6. Analyser les logs d'erreur multisociete
            print '<h3>6. Erreurs Multisociete dans les logs</h3>';
            
            $log_file = DOL_DATA_ROOT . '/dolibarr.log';
            if (file_exists($log_file)) {
                $log_content = file_get_contents($log_file);
                $lines = explode("\n", $log_content);
                $multisociete_errors = array();
                
                foreach ($lines as $line) {
                    if (stripos($line, 'multisociete') !== false || stripos($line, 'entity') !== false) {
                        if (stripos($line, 'ERROR') !== false || stripos($line, 'WARN') !== false) {
                            $multisociete_errors[] = $line;
                        }
                    }
                }
                
                if (!empty($multisociete_errors)) {
                    print '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;">';
                    print '<h4>Erreurs multisociete trouvées :</h4>';
                    foreach (array_slice($multisociete_errors, -10) as $error) {
                        print '<p style="font-size: 12px; margin: 2px 0;">' . htmlspecialchars($error) . '</p>';
                    }
                    print '</div>';
                } else {
                    print '<p>✅ Aucune erreur multisociete récente trouvée</p>';
                }
            }
            
            // 7. Test de "A text to show"
            print '<h3>7. Test de "A text to show"</h3>';
            
            // Simuler une traduction pour voir si elle retourne "A text to show"
            global $langs;
            $test_keys = array('Company', 'Product', 'Invoice', 'Order', 'Contact');
            
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Clé de traduction</th><th>Résultat</th><th>Problème</th></tr>';
            
            foreach ($test_keys as $key) {
                $translation = $langs->trans($key);
                $problem = '';
                
                if ($translation == 'A text to show') {
                    $problem = '❌ Retourne "A text to show"';
                } elseif ($translation == $key) {
                    $problem = '⚠️ Traduction manquante';
                } else {
                    $problem = '✅ Traduction correcte';
                }
                
                print "<tr><td>{$key}</td><td>{$translation}</td><td>{$problem}</td></tr>";
            }
            print '</table>';
            
            break;
            
        case 'fix_multisociete':
            print '<h2>Correction du Module Multisociete</h2>';
            
            $fixes = array();
            $errors = array();
            
            try {
                // 1. Désactiver le module multisociete
                print '<h3>1. Désactivation du module Multisociete</h3>';
                
                $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '0' WHERE name = 'MAIN_MODULE_MULTISOCIETE'";
                if ($db->query($sql)) {
                    $fixes[] = "Module Multisociete désactivé";
                }
                
                // 2. Normaliser toutes les entités à 1
                print '<h3>2. Normalisation des entités</h3>';
                
                // Normaliser les configurations
                $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET entity = 1 WHERE entity != 1";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Configurations normalisées vers entité 1 ({$affected} entrées)";
                }
                
                // Normaliser les tables principales
                $tables_to_normalize = array(
                    'societe', 'socpeople', 'product', 'facture', 'commande', 
                    'propal', 'user', 'adherent', 'projet', 'contrat'
                );
                
                foreach ($tables_to_normalize as $table) {
                    $sql = "UPDATE " . MAIN_DB_PREFIX . $table . " SET entity = 1 WHERE entity != 1";
                    if ($db->query($sql)) {
                        $affected = $db->affected_rows();
                        if ($affected > 0) {
                            $fixes[] = "Table {$table} normalisée ({$affected} entrées)";
                        }
                    }
                }
                
                // 3. Nettoyer les traductions multi-entités
                print '<h3>3. Nettoyage des traductions multi-entités</h3>';
                
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE entity > 1";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Traductions multi-entités supprimées ({$affected} entrées)";
                }
                
                // Normaliser les traductions restantes
                $sql = "UPDATE " . MAIN_DB_PREFIX . "overwrite_trans SET entity = 1 WHERE entity != 1";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Traductions normalisées vers entité 1 ({$affected} entrées)";
                }
                
                // 4. Supprimer les configurations multisociete
                print '<h3>4. Suppression des configurations Multisociete</h3>';
                
                $multisociete_configs = array(
                    'MAIN_MODULE_MULTISOCIETE',
                    'MULTISOCIETE_%',
                    'MULTICOMPANY_%'
                );
                
                foreach ($multisociete_configs as $config_pattern) {
                    $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '{$config_pattern}'";
                    if ($db->query($sql)) {
                        $affected = $db->affected_rows();
                        if ($affected > 0) {
                            $fixes[] = "Configurations {$config_pattern} supprimées ({$affected} entrées)";
                        }
                    }
                }
                
                // 5. Forcer l'entité par défaut
                print '<h3>5. Configuration de l\'entité par défaut</h3>';
                
                // S'assurer que l'entité par défaut est 1
                $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                        VALUES ('MAIN_DEFAULT_ENTITY', '1', 'chaine', 'Default entity', '0', '1') 
                        ON DUPLICATE KEY UPDATE value = '1', entity = '1'";
                if ($db->query($sql)) {
                    $fixes[] = "Entité par défaut configurée à 1";
                }
                
                // 6. Vider tous les caches
                print '<h3>6. Vidage des caches</h3>';
                
                // Caches fichiers
                $cache_dirs = array(
                    DOL_DATA_ROOT . '/admin/temp',
                    DOL_DATA_ROOT . '/admin/temp/module'
                );
                
                foreach ($cache_dirs as $dir) {
                    if (is_dir($dir)) {
                        $files = glob($dir . '/*');
                        foreach ($files as $file) {
                            if (is_file($file)) {
                                unlink($file);
                            }
                        }
                        $fixes[] = "Cache {$dir} vidé";
                    }
                }
                
                // Caches en base
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%CACHE%'";
                if ($db->query($sql)) {
                    $fixes[] = "Caches en base vidés";
                }
                
                // Cache des traductions
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%TRANSLATION%'";
                if ($db->query($sql)) {
                    $fixes[] = "Cache des traductions vidé";
                }
                
                // 7. Nettoyer les traductions corrompues
                print '<h3>7. Nettoyage des traductions corrompues</h3>';
                
                // Supprimer les traductions qui retournent "A text to show"
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE value = 'A text to show'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Traductions 'A text to show' supprimées ({$affected} entrées)";
                }
                
                // Supprimer les traductions vides
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE value = '' OR value IS NULL";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Traductions vides supprimées ({$affected} entrées)";
                }
                
            } catch (Exception $e) {
                $errors[] = "Erreur: " . $e->getMessage();
            }
            
            // Affichage des résultats
            if (!empty($fixes)) {
                print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>✅ Corrections Multisociete effectuées :</h4>';
                print '<ul>';
                foreach ($fixes as $fix) {
                    print '<li>' . $fix . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            if (!empty($errors)) {
                print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>❌ Erreurs :</h4>';
                print '<ul>';
                foreach ($errors as $error) {
                    print '<li>' . $error . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            print '<div style="background: #28a745; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
            print '<h4 style="color: white;">🎉 Multisociete Corrigé !</h4>';
            print '<p style="color: white;"><strong>"A text to show" devrait maintenant disparaître :</strong></p>';
            print '<ol style="color: white;">';
            print '<li>✅ <strong>Module Multisociete désactivé</strong></li>';
            print '<li>✅ <strong>Toutes les entités normalisées à 1</strong></li>';
            print '<li>✅ <strong>Traductions multi-entités supprimées</strong></li>';
            print '<li>✅ <strong>Configurations corrompues nettoyées</strong></li>';
            print '<li>✅ <strong>Caches vidés complètement</strong></li>';
            print '</ol>';
            print '</div>';
            
            print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
            print '<h4 style="color: white;">🔄 Test Final</h4>';
            print '<ol style="color: white;">';
            print '<li><strong>Redémarrez MAMP</strong></li>';
            print '<li><strong>Reconnectez-vous à Dolibarr</strong></li>';
            print '<li><strong>Vérifiez le dashboard</strong></li>';
            print '<li><strong>"A text to show" devrait avoir disparu</strong></li>';
            print '<li><strong>NE RÉACTIVEZ PLUS le module Multisociete</strong></li>';
            print '</ol>';
            print '</div>';
            
            break;
    }
} else {
    // Menu principal
    print '<h2>Correction du Module Multisociete</h2>';
    
    print '<div style="background: #dc3545; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🎯 Problème Identifié</h3>';
    print '<p style="color: white;"><strong>Le module Multisociete est le coupable !</strong></p>';
    print '<ul style="color: white;">';
    print '<li>❌ <strong>Quand activé</strong> : "A text to show" apparaît partout</li>';
    print '<li>❌ <strong>Cause</strong> : Gestion multi-entités corrompue</li>';
    print '<li>❌ <strong>Impact</strong> : Traductions cassées dans tout Dolibarr</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🔍 Diagnostic Multisociete</h3>';
    print '<p style="color: white;">Analyse complète pour comprendre pourquoi Multisociete cause "A text to show" :</p>';
    print '<ul style="color: white;">';
    print '<li>🔧 <strong>Statut du module</strong> : Vérification activation</li>';
    print '<li>🔧 <strong>Entités</strong> : Analyse des entités multiples</li>';
    print '<li>🔧 <strong>Configurations</strong> : Configs multi-entités problématiques</li>';
    print '<li>🔧 <strong>Tables</strong> : Données réparties sur plusieurs entités</li>';
    print '<li>🔧 <strong>Traductions</strong> : Traductions par entité corrompues</li>';
    print '<li>🔧 <strong>Test en temps réel</strong> : Vérification "A text to show"</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #28a745; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🛠️ Correction Multisociete</h3>';
    print '<p style="color: white;">Solution définitive pour éliminer "A text to show" :</p>';
    print '<ul style="color: white;">';
    print '<li>🔧 <strong>Désactiver Multisociete</strong> définitivement</li>';
    print '<li>🔧 <strong>Normaliser toutes les entités</strong> vers entité 1</li>';
    print '<li>🔧 <strong>Supprimer traductions multi-entités</strong></li>';
    print '<li>🔧 <strong>Nettoyer configurations corrompues</strong></li>';
    print '<li>🔧 <strong>Vider tous les caches</strong></li>';
    print '<li>🔧 <strong>Forcer mode mono-entité</strong></li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 20px 0;">';
    print '<a href="?action=analyze_multisociete" style="background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin-right: 10px;">🔍 DIAGNOSTIC MULTISOCIETE</a>';
    print '<a href="?action=fix_multisociete" style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px;">🔧 CORRIGER MULTISOCIETE</a>';
    print '</div>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>⚠️ Pourquoi Multisociete cause "A text to show" ?</h4>';
    print '<p>Le module Multisociete divise Dolibarr en plusieurs "entités" (sociétés). Quand il est mal configuré :</p>';
    print '<ul>';
    print '<li>❌ <strong>Les traductions</strong> sont réparties sur plusieurs entités</li>';
    print '<li>❌ <strong>Les configurations</strong> sont dupliquées et corrompues</li>';
    print '<li>❌ <strong>Le système de traduction</strong> ne trouve plus les bonnes valeurs</li>';
    print '<li>❌ <strong>Résultat</strong> : "A text to show" s\'affiche partout</li>';
    print '</ul>';
    print '<p><strong>Solution :</strong> Désactiver Multisociete et revenir en mode mono-entité.</p>';
    print '</div>';
}

print '</body></html>';
?>
