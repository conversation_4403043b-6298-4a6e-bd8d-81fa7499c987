<?php
// Changement de répertoire pour être dans le bon contexte
chdir('custom/of');
require_once 'config.php';

echo "=== Configuration Debug ===\n";
echo "OF_DELIVERABILITY_REPORT_ORDER_DATE_EXTRAFIELD: " . getDolGlobalString('OF_DELIVERABILITY_REPORT_ORDER_DATE_EXTRAFIELD') . "\n";
echo "OF_DELIVERABILITY_REPORT_SUPPLIERORDER_DATE_EXTRAFIELD: " . getDolGlobalString('OF_DELIVERABILITY_REPORT_SUPPLIERORDER_DATE_EXTRAFIELD') . "\n";
echo "OF_DELIVERABILITY_REPORT_PROPAL_DATE_EXTRAFIELD: " . getDolGlobalString('OF_DELIVERABILITY_REPORT_PROPAL_DATE_EXTRAFIELD') . "\n";

// Vérifier si les configurations sont vides
if(empty(getDolGlobalString('OF_DELIVERABILITY_REPORT_ORDER_DATE_EXTRAFIELD'))) {
    echo "ERROR: OF_DELIVERABILITY_REPORT_ORDER_DATE_EXTRAFIELD is empty\n";
}
if(empty(getDolGlobalString('OF_DELIVERABILITY_REPORT_SUPPLIERORDER_DATE_EXTRAFIELD'))) {
    echo "ERROR: OF_DELIVERABILITY_REPORT_SUPPLIERORDER_DATE_EXTRAFIELD is empty\n";
}
if(empty(getDolGlobalString('OF_DELIVERABILITY_REPORT_PROPAL_DATE_EXTRAFIELD'))) {
    echo "ERROR: OF_DELIVERABILITY_REPORT_PROPAL_DATE_EXTRAFIELD is empty\n";
}

// Vérifier les extrafields disponibles
dol_include_once('/core/class/extrafields.class.php');
$extra = new ExtraFields($db);

echo "\n=== Extrafields pour commandedet ===\n";
$extra->fetch_name_optionals_label('commandedet');
if(!empty($extra->attributes['commandedet']['label'])) {
    foreach($extra->attributes['commandedet']['label'] as $key => $label) {
        echo "- $key: $label\n";
    }
} else {
    echo "Aucun extrafield trouvé pour commandedet\n";
}

echo "\n=== Extrafields pour propaldet ===\n";
$extra->fetch_name_optionals_label('propaldet');
if(!empty($extra->attributes['propaldet']['label'])) {
    foreach($extra->attributes['propaldet']['label'] as $key => $label) {
        echo "- $key: $label\n";
    }
} else {
    echo "Aucun extrafield trouvé pour propaldet\n";
}

echo "\n=== Extrafields pour commande_fournisseurdet ===\n";
$extra->fetch_name_optionals_label('commande_fournisseurdet');
if(!empty($extra->attributes['commande_fournisseurdet']['label'])) {
    foreach($extra->attributes['commande_fournisseurdet']['label'] as $key => $label) {
        echo "- $key: $label\n";
    }
} else {
    echo "Aucun extrafield trouvé pour commande_fournisseurdet\n";
}

// Test de la requête principale
echo "\n=== Test de la requête principale ===\n";
$sqlOrder = "SELECT COUNT(*) as total FROM " . MAIN_DB_PREFIX . "commandedet as cd";
$sqlOrder .= " LEFT JOIN " . MAIN_DB_PREFIX . "commande as c ON (cd.fk_commande = c.rowid)";
$sqlOrder .= " LEFT JOIN " . MAIN_DB_PREFIX . "product as prod ON (prod.rowid = cd.fk_product)";
$sqlOrder .= " WHERE c.fk_statut IN (" . Commande::STATUS_VALIDATED . "," . Commande::STATUS_SHIPMENTONPROCESS. ") AND prod.fk_product_type=0 AND prod.rowid IS NOT NULL";

$result = $db->query($sqlOrder);
if($result) {
    $obj = $db->fetch_object($result);
    echo "Nombre de lignes de commande trouvées: " . $obj->total . "\n";
} else {
    echo "Erreur dans la requête: " . $db->lasterror() . "\n";
}

$sqlPropal = "SELECT COUNT(*) as total FROM " . MAIN_DB_PREFIX . "propaldet as pd";
$sqlPropal .= " LEFT JOIN " . MAIN_DB_PREFIX . "propal as p ON (pd.fk_propal = p.rowid)";
$sqlPropal .= " LEFT JOIN " . MAIN_DB_PREFIX . "propal_extrafields as pe ON (pe.fk_object = p.rowid)";
$sqlPropal .= " LEFT JOIN " . MAIN_DB_PREFIX . "product as prod ON (prod.rowid = pd.fk_product)";
$sqlPropal .= " WHERE p.fk_statut IN (".Propal::STATUS_VALIDATED.",".Propal::STATUS_SIGNED.")";
$sqlPropal .= " AND prod.fk_product_type=0 AND prod.rowid IS NOT NULL AND pe.of_check_prev = 1";

$result = $db->query($sqlPropal);
if($result) {
    $obj = $db->fetch_object($result);
    echo "Nombre de lignes de propal trouvées: " . $obj->total . "\n";
} else {
    echo "Erreur dans la requête propal: " . $db->lasterror() . "\n";
}
?>
