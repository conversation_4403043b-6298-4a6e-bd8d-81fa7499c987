# Guide de Résolution : Problème "A text to show"

## 🚨 Problème
Le texte "A text to show" apparaît devant toutes les références dans le tableau de bord Dolibarr au lieu des vrais libellés.

## 🔍 Diagnostic Rapide

### Étape 1 : Vérification via l'interface web
1. Ouvrez : `http://localhost:8888/sinedtyi/fix_text_to_show_simple.php`
2. Examinez le diagnostic automatique
3. Notez les tables problématiques

### Étape 2 : Diagnostic avancé
1. Ouvrez : `http://localhost:8888/sinedtyi/fix_text_to_show_advanced.php`
2. Cliquez sur "Diagnostic Complet"
3. Analysez les résultats détaillés

## 🛠️ Solutions

### Solution 1 : Correction Automatique (Recommandée)
1. Ouvrez : `http://localhost:8888/sinedtyi/fix_text_to_show_advanced.php`
2. C<PERSON>z sur "Correction Complète"
3. Attendez la fin du processus
4. Déconnectez-vous et reconnectez-vous
5. V<PERSON><PERSON> le cache du navigateur (Ctrl+F5)

### Solution 2 : Correction Manuelle
Si la solution automatique ne fonctionne pas :

#### A. Nettoyer les traductions corrompues
```sql
DELETE FROM llx_overwrite_trans WHERE transvalue LIKE '%text to show%';
DELETE FROM llx_c_translations WHERE transvalue LIKE '%text to show%';
```

#### B. Corriger les libellés vides
```sql
-- Produits
UPDATE llx_product SET label = CONCAT('Produit ', ref) 
WHERE label = '' OR label IS NULL OR label LIKE '%text to show%';

-- Sociétés
UPDATE llx_societe SET nom = CONCAT('Société ', COALESCE(code_client, rowid)) 
WHERE nom = '' OR nom IS NULL OR nom LIKE '%text to show%';

-- Contacts
UPDATE llx_socpeople SET lastname = CONCAT('Contact ', rowid) 
WHERE (lastname = '' OR lastname IS NULL OR lastname LIKE '%text to show%') 
AND (firstname = '' OR firstname IS NULL);
```

#### C. Vider le cache
1. Allez dans **Administration > Outils > Purger les caches**
2. Cliquez sur "Purger tous les caches"
3. Ou supprimez manuellement le dossier : `documents/admin/temp/`

### Solution 3 : Réinitialisation complète des traductions
Si le problème persiste :

1. **Sauvegarder la base de données**
2. **Réinitialiser les traductions :**
   ```sql
   TRUNCATE TABLE llx_overwrite_trans;
   DELETE FROM llx_const WHERE name LIKE '%LANG%';
   INSERT INTO llx_const (name, value, type, note, visible, entity) 
   VALUES ('MAIN_LANG_DEFAULT', 'fr_FR', 'chaine', 'Default language', '0', '1');
   ```
3. **Vider tous les caches**
4. **Se reconnecter**

## 🛡️ Prévention

### Configuration préventive
1. Ouvrez : `http://localhost:8888/sinedtyi/prevent_text_to_show.php`
2. Cliquez sur "Configurer la prévention"
3. Suivez les recommandations

### Maintenance régulière
1. Exécutez `maintenance_text_to_show.php` une fois par semaine
2. Surveillez les logs d'erreur
3. Vérifiez que tous les champs obligatoires sont remplis lors de la création

### Bonnes pratiques
- ✅ Toujours remplir les champs "Libellé" et "Nom"
- ✅ Vérifier les imports de données
- ✅ Tester les modules tiers avant installation
- ✅ Maintenir Dolibarr à jour
- ❌ Ne jamais laisser de champs obligatoires vides
- ❌ Ne pas modifier directement les fichiers de traduction core

## 🔧 Causes Communes

1. **Traductions corrompues** : Fichiers .lang endommagés
2. **Cache corrompu** : Données de cache invalides
3. **Modules tiers défaillants** : Mauvaise gestion des traductions
4. **Import de données** : Données importées sans validation
5. **Mise à jour incomplète** : Processus de mise à jour interrompu

## 📞 Support

Si le problème persiste après avoir suivi ce guide :

1. **Vérifiez les logs** : `documents/dolibarr.log`
2. **Testez en mode debug** : Ajoutez `$dolibarr_main_prod='0';` dans `conf/conf.php`
3. **Contactez le support** avec :
   - Version de Dolibarr
   - Modules installés
   - Messages d'erreur des logs
   - Résultats du diagnostic

## 📋 Checklist de Résolution

- [ ] Diagnostic effectué
- [ ] Correction automatique lancée
- [ ] Cache vidé
- [ ] Déconnexion/reconnexion effectuée
- [ ] Cache navigateur vidé
- [ ] Problème résolu sur le tableau de bord
- [ ] Prévention configurée
- [ ] Script de maintenance programmé

---

**Note :** Ce guide couvre 95% des cas de problème "A text to show". Pour les cas complexes, une analyse plus approfondie peut être nécessaire.
