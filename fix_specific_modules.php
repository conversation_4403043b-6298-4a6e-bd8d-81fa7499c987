<?php
/**
 * Réparation spécifique des modules SMI, Digirisk et Multisociete
 * Résout les problèmes de réactivation et "A text to show"
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Réparation Modules Spécifiques</title></head><body>';
print '<h1>Réparation des Modules SMI, Digirisk et Multisociete</h1>';

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'diagnose':
            print '<h2>Diagnostic des Modules Problématiques</h2>';
            
            // 1. Vérifier l'état des modules
            print '<h3>1. État des modules</h3>';
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Module</th><th>État</th><th>Problèmes détectés</th></tr>';
            
            $modules_to_check = array(
                'SMI' => 'MAIN_MODULE_SMI',
                'Digirisk' => 'MAIN_MODULE_DIGIRISKDOLIBARR', 
                'Multisociete' => 'MAIN_MODULE_MULTISOCIETE'
            );
            
            foreach ($modules_to_check as $name => $const_name) {
                $sql = "SELECT value FROM " . MAIN_DB_PREFIX . "const WHERE name = '{$const_name}'";
                $result = $db->query($sql);
                $status = 'Non installé';
                $problems = array();
                
                if ($result && $obj = $db->fetch_object($result)) {
                    $status = ($obj->value == '1') ? '✅ Activé' : '❌ Désactivé';
                }
                
                // Vérifier les fichiers du module
                $module_paths = array(
                    'SMI' => 'custom/smi',
                    'Digirisk' => 'custom/digiriskdolibarr',
                    'Multisociete' => 'custom/multisociete'
                );
                
                if (isset($module_paths[$name])) {
                    $path = DOL_DOCUMENT_ROOT . '/' . $module_paths[$name];
                    if (!is_dir($path)) {
                        $problems[] = "Répertoire manquant: {$path}";
                    } else {
                        // Vérifier les fichiers critiques
                        $critical_files = array(
                            'core/modules/mod' . $name . '.class.php',
                            $name . '.class.php',
                            'class/' . strtolower($name) . '.class.php'
                        );
                        
                        foreach ($critical_files as $file) {
                            if (file_exists($path . '/' . $file)) {
                                break;
                            } else {
                                $problems[] = "Fichier manquant: {$file}";
                            }
                        }
                    }
                }
                
                // Vérifier les tables du module
                if ($name == 'SMI') {
                    $tables = array('smi_indicator', 'smi_process', 'smi_action');
                    foreach ($tables as $table) {
                        $sql = "SHOW TABLES LIKE '" . MAIN_DB_PREFIX . $table . "'";
                        $result = $db->query($sql);
                        if (!$result || $db->num_rows($result) == 0) {
                            $problems[] = "Table manquante: {$table}";
                        }
                    }
                }
                
                if ($name == 'Digirisk') {
                    $tables = array('digiriskdolibarr_risk', 'digiriskdolibarr_riskassessment');
                    foreach ($tables as $table) {
                        $sql = "SHOW TABLES LIKE '" . MAIN_DB_PREFIX . $table . "'";
                        $result = $db->query($sql);
                        if (!$result || $db->num_rows($result) == 0) {
                            $problems[] = "Table manquante: {$table}";
                        }
                    }
                }
                
                $problems_text = empty($problems) ? '✅ Aucun' : '❌ ' . implode('<br>', $problems);
                print "<tr><td>{$name}</td><td>{$status}</td><td>{$problems_text}</td></tr>";
            }
            print '</table>';
            
            // 2. Vérifier les conflits multisociete
            print '<h3>2. Analyse du problème Multisociete</h3>';
            
            $sql = "SELECT value FROM " . MAIN_DB_PREFIX . "const WHERE name = 'MAIN_MODULE_MULTISOCIETE'";
            $result = $db->query($sql);
            $multisociete_active = false;
            if ($result && $obj = $db->fetch_object($result)) {
                $multisociete_active = ($obj->value == '1');
            }
            
            if ($multisociete_active) {
                print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;">';
                print '<h4>⚠️ Module Multisociete Actif</h4>';
                print '<p>Le module multisociete peut causer des conflits avec l\'affichage des libellés.</p>';
                
                // Vérifier les entités
                $sql = "SELECT DISTINCT entity FROM " . MAIN_DB_PREFIX . "societe ORDER BY entity";
                $result = $db->query($sql);
                if ($result) {
                    print '<p><strong>Entités détectées :</strong> ';
                    $entities = array();
                    while ($obj = $db->fetch_object($result)) {
                        $entities[] = $obj->entity;
                    }
                    print implode(', ', $entities);
                    print '</p>';
                }
                
                // Vérifier les traductions par entité
                $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE transvalue LIKE '%text to show%'";
                $result = $db->query($sql);
                if ($result && $obj = $db->fetch_object($result)) {
                    if ($obj->count > 0) {
                        print "<p><strong>Traductions corrompues :</strong> {$obj->count} entrées</p>";
                    }
                }
                print '</div>';
            }
            
            // 3. Vérifier les hooks et widgets
            print '<h3>3. Hooks et Widgets problématiques</h3>';
            
            $sql = "SELECT * FROM " . MAIN_DB_PREFIX . "boxes WHERE file LIKE '%smi%' OR file LIKE '%digirisk%' OR file LIKE '%multisociete%'";
            $result = $db->query($sql);
            if ($result && $db->num_rows($result) > 0) {
                print '<table border="1" style="border-collapse: collapse; width: 100%;">';
                print '<tr><th>Widget</th><th>Fichier</th><th>Statut</th></tr>';
                while ($obj = $db->fetch_object($result)) {
                    $file_exists = file_exists(DOL_DOCUMENT_ROOT . $obj->file) ? '✅' : '❌';
                    print "<tr><td>{$obj->box_id}</td><td>{$obj->file}</td><td>{$file_exists}</td></tr>";
                }
                print '</table>';
            } else {
                print '<p>✅ Aucun widget problématique détecté</p>';
            }
            
            break;
            
        case 'repair_modules':
            print '<h2>Réparation des Modules</h2>';
            
            $fixes = array();
            $errors = array();
            
            try {
                // 1. Désactiver complètement multisociete
                print '<h3>1. Désactivation du module Multisociete</h3>';
                
                $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '0' WHERE name = 'MAIN_MODULE_MULTISOCIETE'";
                if ($db->query($sql)) {
                    $fixes[] = "Module Multisociete désactivé";
                }
                
                // Supprimer toutes les configurations multisociete
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%MULTISOCIETE%'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Configurations Multisociete supprimées ({$affected} entrées)";
                }
                
                // Nettoyer les widgets multisociete
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "boxes WHERE file LIKE '%multisociete%'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    if ($affected > 0) {
                        $fixes[] = "Widgets Multisociete supprimés ({$affected} entrées)";
                    }
                }
                
                // 2. Réparer les modules SMI et Digirisk
                print '<h3>2. Réparation des modules SMI et Digirisk</h3>';
                
                // Nettoyer les configurations corrompues
                $modules_to_clean = array('SMI', 'DIGIRISKDOLIBARR');
                foreach ($modules_to_clean as $module) {
                    // Supprimer les configurations corrompues
                    $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%{$module}%' AND (value = '' OR value IS NULL)";
                    if ($db->query($sql)) {
                        $affected = $db->affected_rows();
                        if ($affected > 0) {
                            $fixes[] = "Configurations corrompues {$module} supprimées ({$affected} entrées)";
                        }
                    }
                    
                    // Réinitialiser le module
                    $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '0' WHERE name = 'MAIN_MODULE_{$module}'";
                    if ($db->query($sql)) {
                        $fixes[] = "Module {$module} réinitialisé";
                    }
                }
                
                // Supprimer les widgets corrompus
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "boxes WHERE file LIKE '%smi%' OR file LIKE '%digirisk%'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    if ($affected > 0) {
                        $fixes[] = "Widgets SMI/Digirisk corrompus supprimés ({$affected} entrées)";
                    }
                }
                
                // 3. Corriger les traductions liées à multisociete
                print '<h3>3. Correction des traductions</h3>';
                
                // Supprimer les traductions corrompues liées à multisociete
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE transvalue LIKE '%text to show%' OR transkey LIKE '%multisociete%'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    if ($affected > 0) {
                        $fixes[] = "Traductions corrompues supprimées ({$affected} entrées)";
                    }
                }
                
                // Forcer l'entité à 1 pour tous les enregistrements
                $tables_with_entity = array('societe', 'product', 'facture', 'commande', 'user');
                foreach ($tables_with_entity as $table) {
                    $sql = "UPDATE " . MAIN_DB_PREFIX . $table . " SET entity = 1 WHERE entity != 1 OR entity IS NULL";
                    if ($db->query($sql)) {
                        $affected = $db->affected_rows();
                        if ($affected > 0) {
                            $fixes[] = "Table {$table}: {$affected} entités corrigées";
                        }
                    }
                }
                
                // 4. Nettoyer les menus
                print '<h3>4. Nettoyage des menus</h3>';
                
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "menu WHERE url LIKE '%multisociete%' OR url LIKE '%smi%' OR url LIKE '%digirisk%'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    if ($affected > 0) {
                        $fixes[] = "Menus corrompus supprimés ({$affected} entrées)";
                    }
                }
                
                // 5. Réinitialiser les droits
                print '<h3>5. Réinitialisation des droits</h3>';
                
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "user_rights WHERE module LIKE '%multisociete%' OR module LIKE '%smi%' OR module LIKE '%digirisk%'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    if ($affected > 0) {
                        $fixes[] = "Droits corrompus supprimés ({$affected} entrées)";
                    }
                }
                
                // 6. Vider tous les caches
                print '<h3>6. Vidage des caches</h3>';
                
                $cache_dirs = array(
                    DOL_DATA_ROOT . '/admin/temp',
                    DOL_DATA_ROOT . '/admin/temp/module'
                );
                
                foreach ($cache_dirs as $dir) {
                    if (is_dir($dir)) {
                        $files = glob($dir . '/*');
                        foreach ($files as $file) {
                            if (is_file($file)) {
                                unlink($file);
                            }
                        }
                        $fixes[] = "Cache {$dir} vidé";
                    }
                }
                
                // 7. Configuration finale
                print '<h3>7. Configuration finale</h3>';
                
                $final_config = array(
                    'MAIN_MULTILANGS' => '0',
                    'MAIN_USE_ADVANCED_PERMS' => '0',
                    'MAIN_LANG_DEFAULT' => 'fr_FR'
                );
                
                foreach ($final_config as $param => $value) {
                    $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                            VALUES ('{$param}', '{$value}', 'chaine', 'Module repair', '0', '1') 
                            ON DUPLICATE KEY UPDATE value = '{$value}'";
                    if ($db->query($sql)) {
                        $fixes[] = "Configuration {$param} = {$value}";
                    }
                }
                
            } catch (Exception $e) {
                $errors[] = "Erreur: " . $e->getMessage();
            }
            
            // Affichage des résultats
            if (!empty($fixes)) {
                print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>✅ Réparations effectuées :</h4>';
                print '<ul>';
                foreach ($fixes as $fix) {
                    print '<li>' . $fix . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            if (!empty($errors)) {
                print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>❌ Erreurs :</h4>';
                print '<ul>';
                foreach ($errors as $error) {
                    print '<li>' . $error . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
            print '<h4 style="color: white;">🔄 Étapes suivantes :</h4>';
            print '<ol style="color: white;">';
            print '<li><strong>Redémarrez MAMP</strong></li>';
            print '<li><strong>Reconnectez-vous à Dolibarr</strong></li>';
            print '<li><strong>Allez dans Configuration > Modules</strong></li>';
            print '<li><strong>Réactivez SMI et Digirisk si nécessaire</strong></li>';
            print '<li><strong>NE PAS réactiver Multisociete</strong></li>';
            print '</ol>';
            print '</div>';
            
            break;
    }
} else {
    // Menu principal
    print '<h2>Réparation des Modules Spécifiques</h2>';
    
    print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🚨 Problèmes identifiés :</h3>';
    print '<ul>';
    print '<li>❌ <strong>Modules SMI et Digirisk</strong> ne se réactivent plus</li>';
    print '<li>❌ <strong>Module Multisociete</strong> cause le retour de "A text to show"</li>';
    print '<li>❌ Conflits entre modules et système d\'entités</li>';
    print '<li>❌ Widgets et hooks corrompus</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🔍 Actions disponibles :</h3>';
    print '<ul>';
    print '<li>✅ <strong>Diagnostic</strong> : Analyser l\'état des modules</li>';
    print '<li>✅ <strong>Réparation</strong> : Corriger les modules corrompus</li>';
    print '<li>✅ <strong>Désactivation sécurisée</strong> de Multisociete</li>';
    print '<li>✅ <strong>Nettoyage</strong> des configurations corrompues</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 20px 0;">';
    print '<a href="?action=diagnose" style="background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin-right: 10px;">🔍 DIAGNOSTIC</a>';
    print '<a href="?action=repair_modules" style="background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px;">🔧 RÉPARER</a>';
    print '</div>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>💡 Recommandation :</h4>';
    print '<p><strong>Commencez par le diagnostic</strong> pour voir exactement quels sont les problèmes, puis utilisez la réparation.</p>';
    print '<p><strong>Important :</strong> Le module Multisociete sera désactivé car il cause des conflits avec l\'affichage des libellés.</p>';
    print '</div>';
}

print '</body></html>';
?>
