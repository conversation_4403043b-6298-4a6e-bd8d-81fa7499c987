<?php
/**
 * Diagnostic et réparation spécifique du module SMI
 * Solution ciblée pour le module SMI qui ne se réactive pas
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Réparation SMI Spécifique</title></head><body>';
print '<h1>Diagnostic et Réparation Spécifique du Module SMI</h1>';

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'deep_smi_analysis':
            print '<h2>Analyse Approfondie du Module SMI</h2>';
            
            // 1. Vérifier le fichier de classe SMI
            print '<h3>1. Analyse du fichier de classe</h3>';
            $class_file = DOL_DOCUMENT_ROOT . '/custom/smi/core/modules/modSMI.class.php';
            
            if (file_exists($class_file)) {
                print '<p>✅ Fichier de classe trouvé : ' . $class_file . '</p>';
                
                // Lire le contenu du fichier
                $content = file_get_contents($class_file);
                
                // Vérifier les éléments critiques
                $checks = array(
                    'class modSMI' => strpos($content, 'class modSMI') !== false,
                    'extends DolibarrModules' => strpos($content, 'extends DolibarrModules') !== false,
                    '__construct' => strpos($content, '__construct') !== false,
                    'init()' => strpos($content, 'function init(') !== false,
                    'remove()' => strpos($content, 'function remove(') !== false
                );
                
                print '<table border="1" style="border-collapse: collapse; width: 100%;">';
                print '<tr><th>Élément</th><th>Statut</th></tr>';
                foreach ($checks as $element => $found) {
                    $status = $found ? '✅ Trouvé' : '❌ Manquant';
                    print "<tr><td>{$element}</td><td>{$status}</td></tr>";
                }
                print '</table>';
                
                // Vérifier les erreurs de syntaxe
                $syntax_check = shell_exec("php -l {$class_file} 2>&1");
                if (strpos($syntax_check, 'No syntax errors') !== false) {
                    print '<p>✅ Syntaxe PHP correcte</p>';
                } else {
                    print '<div style="background: #f8d7da; padding: 10px; border-radius: 5px;">';
                    print '<h4>❌ Erreur de syntaxe détectée :</h4>';
                    print '<pre>' . htmlspecialchars($syntax_check) . '</pre>';
                    print '</div>';
                }
            } else {
                print '<p>❌ Fichier de classe manquant : ' . $class_file . '</p>';
            }
            
            // 2. Vérifier les dépendances
            print '<h3>2. Vérification des dépendances</h3>';
            
            // Vérifier si d'autres modules interfèrent
            $conflicting_modules = array(
                'MAIN_MODULE_ABRICOT',
                'MAIN_MODULE_QUALITY',
                'MAIN_MODULE_MULTISOCIETE'
            );
            
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Module potentiellement conflictuel</th><th>Statut</th></tr>';
            
            foreach ($conflicting_modules as $module) {
                $sql = "SELECT value FROM " . MAIN_DB_PREFIX . "const WHERE name = '{$module}'";
                $result = $db->query($sql);
                $status = '❌ Non installé';
                if ($result && $obj = $db->fetch_object($result)) {
                    $status = ($obj->value == '1') ? '⚠️ Activé (conflit possible)' : '✅ Désactivé';
                }
                print "<tr><td>{$module}</td><td>{$status}</td></tr>";
            }
            print '</table>';
            
            // 3. Vérifier les tables SMI en détail
            print '<h3>3. Analyse détaillée des tables SMI</h3>';
            
            $smi_tables = array('smi_indicator', 'smi_process', 'smi_action');
            
            foreach ($smi_tables as $table) {
                print "<h4>Table {$table}</h4>";
                
                // Vérifier la structure
                $sql = "DESCRIBE " . MAIN_DB_PREFIX . $table;
                $result = $db->query($sql);
                if ($result) {
                    print '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
                    print '<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th></tr>';
                    while ($obj = $db->fetch_object($result)) {
                        print "<tr><td>{$obj->Field}</td><td>{$obj->Type}</td><td>{$obj->Null}</td><td>{$obj->Key}</td></tr>";
                    }
                    print '</table>';
                    
                    // Compter les enregistrements
                    $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . $table;
                    $result2 = $db->query($sql);
                    if ($result2 && $obj2 = $db->fetch_object($result2)) {
                        print "<p>Nombre d'enregistrements : {$obj2->count}</p>";
                    }
                } else {
                    print '<p>❌ Erreur lors de la lecture de la table</p>';
                }
            }
            
            // 4. Vérifier les configurations SMI
            print '<h3>4. Configurations SMI</h3>';
            
            $sql = "SELECT name, value FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%SMI%' ORDER BY name";
            $result = $db->query($sql);
            
            if ($result && $db->num_rows($result) > 0) {
                print '<table border="1" style="border-collapse: collapse; width: 100%;">';
                print '<tr><th>Configuration</th><th>Valeur</th></tr>';
                while ($obj = $db->fetch_object($result)) {
                    print "<tr><td>{$obj->name}</td><td>{$obj->value}</td></tr>";
                }
                print '</table>';
            } else {
                print '<p>❌ Aucune configuration SMI trouvée</p>';
            }
            
            // 5. Vérifier les logs d'erreur spécifiques à SMI
            print '<h3>5. Erreurs SMI récentes</h3>';
            
            $log_file = DOL_DATA_ROOT . '/dolibarr.log';
            if (file_exists($log_file)) {
                $log_content = file_get_contents($log_file);
                $lines = explode("\n", $log_content);
                $smi_errors = array();
                
                foreach ($lines as $line) {
                    if (stripos($line, 'smi') !== false && (stripos($line, 'ERROR') !== false || stripos($line, 'WARN') !== false)) {
                        $smi_errors[] = $line;
                    }
                }
                
                if (!empty($smi_errors)) {
                    print '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;">';
                    print '<h4>Erreurs SMI trouvées :</h4>';
                    foreach (array_slice($smi_errors, -10) as $error) {
                        print '<p style="font-size: 12px; margin: 2px 0;">' . htmlspecialchars($error) . '</p>';
                    }
                    print '</div>';
                } else {
                    print '<p>✅ Aucune erreur SMI récente trouvée</p>';
                }
            }
            
            // 6. Test d'inclusion du fichier de classe
            print '<h3>6. Test d\'inclusion de la classe</h3>';
            
            try {
                if (file_exists($class_file)) {
                    ob_start();
                    $error_occurred = false;
                    
                    // Capturer les erreurs
                    set_error_handler(function($severity, $message, $file, $line) use (&$error_occurred) {
                        $error_occurred = true;
                        return false;
                    });
                    
                    include_once $class_file;
                    
                    restore_error_handler();
                    $output = ob_get_clean();
                    
                    if (!$error_occurred && class_exists('modSMI')) {
                        print '<p>✅ Classe modSMI chargée avec succès</p>';
                        
                        // Tester l'instanciation
                        try {
                            $smi_module = new modSMI($db);
                            print '<p>✅ Instance de modSMI créée avec succès</p>';
                            print '<p>Version du module : ' . $smi_module->version . '</p>';
                            print '<p>Nom du module : ' . $smi_module->name . '</p>';
                        } catch (Exception $e) {
                            print '<p>❌ Erreur lors de l\'instanciation : ' . $e->getMessage() . '</p>';
                        }
                    } else {
                        print '<p>❌ Erreur lors du chargement de la classe</p>';
                        if ($output) {
                            print '<pre>' . htmlspecialchars($output) . '</pre>';
                        }
                    }
                }
            } catch (Exception $e) {
                print '<p>❌ Exception lors du test : ' . $e->getMessage() . '</p>';
            }
            
            break;
            
        case 'fix_smi_force':
            print '<h2>Réparation Forcée du Module SMI</h2>';
            
            $fixes = array();
            $errors = array();
            
            try {
                // 1. Nettoyer complètement SMI
                print '<h3>1. Nettoyage complet SMI</h3>';
                
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%SMI%'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Configurations SMI supprimées ({$affected} entrées)";
                }
                
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "user_rights WHERE module = 'smi'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Droits SMI supprimés ({$affected} entrées)";
                }
                
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "menu WHERE module = 'smi'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes[] = "Menus SMI supprimés ({$affected} entrées)";
                }
                
                // 2. Recréer la configuration de base
                print '<h3>2. Recréation de la configuration SMI</h3>';
                
                $base_configs = array(
                    'MAIN_MODULE_SMI' => '0',
                    'SMI_VERSION' => '1.0.0',
                    'SMI_NUMBERING_INDICATOR' => 'mod_indicator_standard',
                    'SMI_NUMBERING_PROCESS' => 'mod_process_standard',
                    'SMI_NUMBERING_ACTION' => 'mod_action_standard'
                );
                
                foreach ($base_configs as $name => $value) {
                    $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                            VALUES ('{$name}', '{$value}', 'chaine', 'SMI base config', '0', '1')";
                    if ($db->query($sql)) {
                        $fixes[] = "Configuration {$name} créée";
                    }
                }
                
                // 3. Recréer les droits complets
                print '<h3>3. Recréation des droits SMI</h3>';
                
                $smi_rights = array(
                    array('module' => 'smi', 'perms' => 'lire', 'subperms' => ''),
                    array('module' => 'smi', 'perms' => 'creer', 'subperms' => ''),
                    array('module' => 'smi', 'perms' => 'modifier', 'subperms' => ''),
                    array('module' => 'smi', 'perms' => 'supprimer', 'subperms' => ''),
                    array('module' => 'smi', 'perms' => 'configurer', 'subperms' => ''),
                    array('module' => 'smi', 'perms' => 'indicator', 'subperms' => 'lire'),
                    array('module' => 'smi', 'perms' => 'indicator', 'subperms' => 'creer'),
                    array('module' => 'smi', 'perms' => 'indicator', 'subperms' => 'modifier'),
                    array('module' => 'smi', 'perms' => 'indicator', 'subperms' => 'supprimer'),
                    array('module' => 'smi', 'perms' => 'process', 'subperms' => 'lire'),
                    array('module' => 'smi', 'perms' => 'process', 'subperms' => 'creer'),
                    array('module' => 'smi', 'perms' => 'process', 'subperms' => 'modifier'),
                    array('module' => 'smi', 'perms' => 'process', 'subperms' => 'supprimer'),
                    array('module' => 'smi', 'perms' => 'action', 'subperms' => 'lire'),
                    array('module' => 'smi', 'perms' => 'action', 'subperms' => 'creer'),
                    array('module' => 'smi', 'perms' => 'action', 'subperms' => 'modifier'),
                    array('module' => 'smi', 'perms' => 'action', 'subperms' => 'supprimer')
                );
                
                // Appliquer à tous les utilisateurs admin
                $sql = "SELECT rowid FROM " . MAIN_DB_PREFIX . "user WHERE admin = 1";
                $result = $db->query($sql);
                $admin_users = array();
                if ($result) {
                    while ($obj = $db->fetch_object($result)) {
                        $admin_users[] = $obj->rowid;
                    }
                }
                
                foreach ($admin_users as $user_id) {
                    foreach ($smi_rights as $right) {
                        $sql = "INSERT IGNORE INTO " . MAIN_DB_PREFIX . "user_rights (fk_user, module, perms, subperms, entity) 
                                VALUES ({$user_id}, '{$right['module']}', '{$right['perms']}', '{$right['subperms']}', 1)";
                        $db->query($sql);
                    }
                    $fixes[] = "Droits SMI appliqués à l'utilisateur {$user_id}";
                }
                
                // 4. Forcer l'activation
                print '<h3>4. Activation forcée</h3>';
                
                $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '1' WHERE name = 'MAIN_MODULE_SMI'";
                if ($db->query($sql)) {
                    $fixes[] = "Module SMI activé de force";
                }
                
                // 5. Vider tous les caches
                print '<h3>5. Vidage des caches</h3>';
                
                $cache_dirs = array(
                    DOL_DATA_ROOT . '/admin/temp',
                    DOL_DATA_ROOT . '/admin/temp/module'
                );
                
                foreach ($cache_dirs as $dir) {
                    if (is_dir($dir)) {
                        $files = glob($dir . '/*');
                        foreach ($files as $file) {
                            if (is_file($file)) {
                                unlink($file);
                            }
                        }
                        $fixes[] = "Cache {$dir} vidé";
                    }
                }
                
                // Vider les caches en base
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%CACHE%'";
                if ($db->query($sql)) {
                    $fixes[] = "Caches en base vidés";
                }
                
            } catch (Exception $e) {
                $errors[] = "Erreur: " . $e->getMessage();
            }
            
            // Affichage des résultats
            if (!empty($fixes)) {
                print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>✅ Réparations SMI effectuées :</h4>';
                print '<ul>';
                foreach ($fixes as $fix) {
                    print '<li>' . $fix . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            if (!empty($errors)) {
                print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>❌ Erreurs :</h4>';
                print '<ul>';
                foreach ($errors as $error) {
                    print '<li>' . $error . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            print '<div style="background: #28a745; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
            print '<h4 style="color: white;">🔄 Test Final</h4>';
            print '<p style="color: white;"><strong>Maintenant testez :</strong></p>';
            print '<ol style="color: white;">';
            print '<li><strong>Redémarrez MAMP</strong></li>';
            print '<li><strong>Reconnectez-vous à Dolibarr</strong></li>';
            print '<li><strong>Allez dans Configuration > Modules</strong></li>';
            print '<li><strong>Recherchez "SMI"</strong></li>';
            print '<li><strong>Essayez de l\'activer</strong></li>';
            print '</ol>';
            print '</div>';
            
            break;
    }
} else {
    // Menu principal
    print '<h2>Diagnostic et Réparation SMI</h2>';
    
    print '<div style="background: #ffc107; color: black; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🎯 Situation Actuelle</h3>';
    print '<ul>';
    print '<li>✅ <strong>Digirisk</strong> : Réactivé avec succès</li>';
    print '<li>❌ <strong>SMI</strong> : Résiste encore à la réactivation</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🔍 Diagnostic Approfondi SMI</h3>';
    print '<p style="color: white;">Analyse complète pour identifier pourquoi SMI ne se réactive pas :</p>';
    print '<ul style="color: white;">';
    print '<li>🔧 <strong>Fichier de classe</strong> : Vérification syntaxe et structure</li>';
    print '<li>🔧 <strong>Dépendances</strong> : Conflits avec autres modules</li>';
    print '<li>🔧 <strong>Tables</strong> : Structure et intégrité</li>';
    print '<li>🔧 <strong>Configurations</strong> : Paramètres corrompus</li>';
    print '<li>🔧 <strong>Logs d\'erreur</strong> : Messages spécifiques SMI</li>';
    print '<li>🔧 <strong>Test de classe</strong> : Chargement et instanciation</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #dc3545; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">🛠️ Réparation Forcée SMI</h3>';
    print '<p style="color: white;">Si le diagnostic révèle des problèmes, réparation complète :</p>';
    print '<ul style="color: white;">';
    print '<li>🔧 <strong>Nettoyage total</strong> de toutes les traces SMI</li>';
    print '<li>🔧 <strong>Recréation</strong> des configurations de base</li>';
    print '<li>🔧 <strong>Restauration</strong> des droits complets</li>';
    print '<li>🔧 <strong>Activation forcée</strong> du module</li>';
    print '<li>🔧 <strong>Vidage</strong> de tous les caches</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 20px 0;">';
    print '<a href="?action=deep_smi_analysis" style="background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin-right: 10px;">🔍 DIAGNOSTIC SMI</a>';
    print '<a href="?action=fix_smi_force" style="background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px;">🔧 RÉPARATION FORCÉE</a>';
    print '</div>';
    
    print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>💡 Recommandation</h4>';
    print '<p><strong>Commencez par le diagnostic</strong> pour voir exactement ce qui bloque SMI, puis utilisez la réparation forcée si nécessaire.</p>';
    print '</div>';
}

print '</body></html>';
?>
