<?php
/**
 * Debug en temps réel du système de traduction sur une vraie page Dolibarr
 * Pour identifier pourquoi "A text to show" apparaît partout
 */

// Inclusion de Dolibarr
$res = 0;
if (!$res && file_exists("main.inc.php")) {
    $res = include "main.inc.php";
}
if (!$res && file_exists("../main.inc.php")) {
    $res = include "../main.inc.php";
}
if (!$res && file_exists("../../main.inc.php")) {
    $res = include "../../main.inc.php";
}
if (!$res && file_exists("../../../main.inc.php")) {
    $res = include "../../../main.inc.php";
}
if (!$res) {
    die("Include of main fails");
}

// Vérification des droits
if (!$user->admin) {
    accessforbidden();
}

// Début de la page
llxHeader('Debug Live Translation', '', '');

print '<div class="fiche">';
print '<div class="fichetitle">🔍 Debug Live Translation - "A text to show"</div>';

// 1. Informations système actuelles
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<td colspan="2">Informations Système Actuelles</td>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>Utilisateur connecté</td>';
print '<td>' . $user->login . ' (ID: ' . $user->id . ', Entité: ' . $user->entity . ')</td>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>Entité système ($conf->entity)</td>';
print '<td>' . $conf->entity . '</td>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>Langue par défaut ($langs->defaultlang)</td>';
print '<td>' . $langs->defaultlang . '</td>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>Configuration MAIN_LANG_DEFAULT</td>';
print '<td>' . ($conf->global->MAIN_LANG_DEFAULT ?? 'Non définie') . '</td>';
print '</tr>';

print '<tr class="oddeven">';
print '<td>Module Multisociete</td>';
print '<td>' . (($conf->global->MAIN_MODULE_MULTISOCIETE ?? '0') == '1' ? '✅ Activé' : '❌ Désactivé') . '</td>';
print '</tr>';

print '</table>';
print '</div>';

// 2. Test des traductions en contexte Dolibarr
print '<br>';
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<td colspan="3">Test des Traductions en Contexte Dolibarr</td>';
print '</tr>';

$test_translations = array(
    'Company' => 'Société',
    'Product' => 'Produit', 
    'Invoice' => 'Facture',
    'Order' => 'Commande',
    'Contact' => 'Contact',
    'ThirdParty' => 'Tiers',
    'User' => 'Utilisateur',
    'Menu' => 'Menu',
    'Home' => 'Accueil',
    'List' => 'Liste',
    'New' => 'Nouveau',
    'Edit' => 'Modifier',
    'Delete' => 'Supprimer',
    'Save' => 'Enregistrer',
    'Cancel' => 'Annuler',
    'Search' => 'Rechercher',
    'Action' => 'Action',
    'Status' => 'Statut',
    'Date' => 'Date',
    'Reference' => 'Référence'
);

foreach ($test_translations as $key => $expected) {
    $actual = $langs->trans($key);
    $status = '';
    $class = 'oddeven';
    
    if ($actual == 'A text to show') {
        $status = '❌ PROBLÈME';
        $class = 'error';
    } elseif ($actual == $key) {
        $status = '⚠️ Non traduit';
        $class = 'warning';
    } elseif ($actual == $expected) {
        $status = '✅ Correct';
    } else {
        $status = '⚠️ Différent';
        $class = 'warning';
    }
    
    print '<tr class="' . $class . '">';
    print '<td>' . $key . '</td>';
    print '<td>' . htmlspecialchars($actual) . '</td>';
    print '<td>' . $status . '</td>';
    print '</tr>';
}

print '</table>';
print '</div>';

// 3. Test des éléments d'interface Dolibarr
print '<br>';
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<td colspan="3">Test des Éléments d\'Interface Dolibarr</td>';
print '</tr>';

// Simuler des éléments d'interface typiques
$interface_elements = array(
    'Titre de page' => $langs->trans('Companies'),
    'Bouton Nouveau' => $langs->trans('NewCompany'),
    'Colonne Nom' => $langs->trans('Name'),
    'Colonne Ville' => $langs->trans('Town'),
    'Colonne Pays' => $langs->trans('Country'),
    'Colonne Téléphone' => $langs->trans('Phone'),
    'Colonne Email' => $langs->trans('EMail'),
    'Action Modifier' => $langs->trans('Modify'),
    'Action Supprimer' => $langs->trans('Delete'),
    'Statut Actif' => $langs->trans('Active'),
    'Menu Accueil' => $langs->trans('Home'),
    'Menu Sociétés' => $langs->trans('ThirdParties'),
    'Menu Produits' => $langs->trans('Products'),
    'Menu Factures' => $langs->trans('Invoices'),
    'Menu Commandes' => $langs->trans('Orders')
);

foreach ($interface_elements as $element => $translation) {
    $status = '';
    $class = 'oddeven';
    
    if ($translation == 'A text to show') {
        $status = '❌ PROBLÈME CRITIQUE';
        $class = 'error';
    } elseif (empty($translation)) {
        $status = '⚠️ Vide';
        $class = 'warning';
    } else {
        $status = '✅ OK';
    }
    
    print '<tr class="' . $class . '">';
    print '<td>' . $element . '</td>';
    print '<td>' . htmlspecialchars($translation) . '</td>';
    print '<td>' . $status . '</td>';
    print '</tr>';
}

print '</table>';
print '</div>';

// 4. Diagnostic approfondi si problème détecté
$has_text_to_show = false;
foreach (array_merge($test_translations, $interface_elements) as $key => $value) {
    if (is_string($value) && $value == 'A text to show') {
        $has_text_to_show = true;
        break;
    }
}

if ($has_text_to_show) {
    print '<br>';
    print '<div class="error">';
    print '<h3>❌ "A text to show" détecté !</h3>';
    print '<p><strong>Diagnostic approfondi nécessaire...</strong></p>';
    
    // Analyser la fonction de traduction
    print '<h4>Analyse de la fonction $langs->trans()</h4>';
    
    // Test avec une clé qui pose problème
    $problematic_key = '';
    foreach ($test_translations as $key => $expected) {
        if ($langs->trans($key) == 'A text to show') {
            $problematic_key = $key;
            break;
        }
    }
    
    if ($problematic_key) {
        print '<p><strong>Clé problématique identifiée :</strong> ' . $problematic_key . '</p>';
        
        // Analyser étape par étape
        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre"><td colspan="2">Analyse étape par étape</td></tr>';
        
        // Vérifier si la clé existe dans les fichiers de langue
        $lang_file = DOL_DOCUMENT_ROOT . '/langs/' . $langs->defaultlang . '/main.lang';
        if (file_exists($lang_file)) {
            $content = file_get_contents($lang_file);
            $has_key = strpos($content, $problematic_key . '=') !== false;
            
            print '<tr class="oddeven">';
            print '<td>Clé dans ' . $lang_file . '</td>';
            print '<td>' . ($has_key ? '✅ Trouvée' : '❌ Manquante') . '</td>';
            print '</tr>';
            
            if ($has_key) {
                preg_match('/' . $problematic_key . '=(.*)/', $content, $matches);
                if (isset($matches[1])) {
                    print '<tr class="oddeven">';
                    print '<td>Valeur dans le fichier</td>';
                    print '<td>' . htmlspecialchars(trim($matches[1])) . '</td>';
                    print '</tr>';
                }
            }
        }
        
        // Vérifier les traductions personnalisées
        $sql = "SELECT * FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE transkey = '" . $db->escape($problematic_key) . "' AND lang = '" . $langs->defaultlang . "'";
        $result = $db->query($sql);
        
        if ($result && $db->num_rows($result) > 0) {
            print '<tr class="oddeven">';
            print '<td>Traduction personnalisée</td>';
            print '<td>';
            while ($obj = $db->fetch_object($result)) {
                print 'Entité ' . $obj->entity . ': ' . htmlspecialchars($obj->value) . '<br>';
            }
            print '</td>';
            print '</tr>';
        } else {
            print '<tr class="oddeven">';
            print '<td>Traduction personnalisée</td>';
            print '<td>❌ Aucune</td>';
            print '</tr>';
        }
        
        print '</table>';
    }
    
    print '</div>';
} else {
    print '<br>';
    print '<div class="ok">';
    print '<h3>✅ Aucun "A text to show" détecté dans ce contexte</h3>';
    print '<p>Les traductions semblent fonctionner correctement dans cette page de debug.</p>';
    print '<p><strong>Le problème pourrait être :</strong></p>';
    print '<ul>';
    print '<li>Spécifique à certaines pages ou modules</li>';
    print '<li>Lié au cache du navigateur</li>';
    print '<li>Causé par des hooks ou modules tiers</li>';
    print '</ul>';
    print '</div>';
}

// 5. Actions recommandées
print '<br>';
print '<div class="info">';
print '<h3>🔧 Actions Recommandées</h3>';

if ($has_text_to_show) {
    print '<p><strong>Problème confirmé - Actions immédiates :</strong></p>';
    print '<ol>';
    print '<li><strong>Vider complètement le cache</strong> (fichiers + base de données)</li>';
    print '<li><strong>Corriger les fichiers de langue</strong> corrompus</li>';
    print '<li><strong>Synchroniser les entités</strong> utilisateur/système</li>';
    print '<li><strong>Désactiver temporairement Multisociete</strong> pour test</li>';
    print '</ol>';
} else {
    print '<p><strong>Pas de problème détecté ici - Vérifications :</strong></p>';
    print '<ol>';
    print '<li><strong>Videz le cache de votre navigateur</strong> (Ctrl+F5)</li>';
    print '<li><strong>Testez d\'autres pages</strong> (sociétés, produits, factures)</li>';
    print '<li><strong>Vérifiez les modules personnalisés</strong> actifs</li>';
    print '<li><strong>Redémarrez MAMP</strong> complètement</li>';
    print '</ol>';
}

print '<p><a href="' . $_SERVER['PHP_SELF'] . '" class="button">🔄 Actualiser le Test</a></p>';
print '</div>';

print '</div>';

// Fin de la page
llxFooter();
?>
