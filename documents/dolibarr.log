2025-07-31 17:27:14 DEBUG   ::1             Removed file /Applications/MAMP/dolibarr18/sinedtyi/documents/dolibarr.log
2025-07-31 17:27:14 INFO    ::1             functions.lib::dol_mkdir: dir=/Applications/MAMP/dolibarr18/sinedtyi/documents/api/temp
2025-07-31 17:27:14 INFO    ::1             functions.lib::dol_mkdir: dir=/Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp
2025-07-31 17:27:14 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:14 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:14 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=toprightmenu-path=/multisociete/class/actions_multisociete.class.php
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid, label, name, country_code, currency_code, default_lang, active, visible FROM llx_entity WHERE active = 1 AND visible = 1 ORDER BY label ASC
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid, title, url, target FROM llx_bookmark WHERE (fk_user = 1 OR fk_user is NULL OR fk_user = 0) AND entity IN (1) ORDER BY position
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=leftblock-path=/ultimateimmo/class/actions_ultimateimmo.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=searchform-path=/ultimateimmo/class/actions_ultimateimmo.class.php, context=searchform-path=/cabinetmed/class/actions_cabinetmed.class.php, context=searchform-path=/lead/class/actions_lead.class.php, context=searchform-path=/of/class/actions_of.class.php
2025-07-31 17:27:14 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=get_sheet_linkable_objects-path=/dolimeet/class/actions_dolimeet.class.php, context=get_sheet_linkable_objects-path=/dolicar/class/actions_dolicar.class.php
2025-07-31 17:27:14 INFO    ::1             --- End access to /sinedtyi/admin/tools/purge.php
2025-07-31 17:27:14 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:14 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:14 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:14 NOTICE  ::1             --- Access to GET /sinedtyi/custom/discountrules/js/discountrules.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:14 INFO    ::1             --- End access to /sinedtyi/custom/discountrules/js/discountrules.js.php
2025-07-31 17:27:14 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:14 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:14 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:14 NOTICE  ::1             --- Access to GET /sinedtyi/custom/postit/css/postit.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:14 INFO    ::1             --- End access to /sinedtyi/custom/postit/css/postit.css.php
2025-07-31 17:27:14 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:27:14 NOTICE  ::1             --- Access to GET /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:14 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:14 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:14 INFO    ::1             --- End access to /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php
2025-07-31 17:27:14 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:14 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:14 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:14 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:14 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/css/insurance.css.php - action=, massaction=
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:14 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:14 INFO    ::1             --- End access to /sinedtyi/custom/insurance/css/insurance.css.php
2025-07-31 17:27:15 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:27:15 NOTICE  ::1             --- Access to GET /sinedtyi/custom/multisociete/js/multisociete.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:15 INFO    ::1             --- End access to /sinedtyi/custom/multisociete/js/multisociete.js.php
2025-07-31 17:27:15 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:15 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:15 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:15 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:15 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/js/insurance.js.php - action=, massaction=
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:15 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:15 INFO    ::1             --- End access to /sinedtyi/custom/insurance/js/insurance.js.php
2025-07-31 17:27:17 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:17 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:17 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:17 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:17 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:17 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:17 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:17 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:17 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:17 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:17 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:17 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:17 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:17 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:17 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:17 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:17 INFO    ::1             dolnotif_nb_test_for_page=2
2025-07-31 17:27:17 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:27:17' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:27:17 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:27:18 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:18 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:18 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:18 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:18 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:18 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:18 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:18 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:18 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:18 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:18 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:18 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:18 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:18 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:18 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:18 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:18 INFO    ::1             dolnotif_nb_test_for_page=1
2025-07-31 17:27:18 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:27:18' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:27:18 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:27:19 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:19 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:19 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:19 NOTICE  ::1             --- Access to GET /sinedtyi/admin/modules.php - action=, massaction=
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:19 DEBUG   ::1             BEGIN Transaction
2025-07-31 17:27:19 DEBUG   ::1              admin.lib::dolibarr_set_const
2025-07-31 17:27:19 DEBUG   ::1              sql=DELETE FROM llx_const WHERE name = 'MAIN_MODULE_SETUP_ON_LIST_BY_DEFAULT' AND entity = 1
2025-07-31 17:27:19 DEBUG   ::1              admin.lib::dolibarr_set_const
2025-07-31 17:27:19 DEBUG   ::1              sql=INSERT INTO llx_const(name, value, type, visible, note, entity) VALUES ('MAIN_MODULE_SETUP_ON_LIST_BY_DEFAULT', 'common', 'chaine', 0, '', 1)
2025-07-31 17:27:19 DEBUG   ::1             COMMIT Transaction
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=globaladmin-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php
2025-07-31 17:27:19 INFO    ::1             max_execution_time=60 is lower than max_execution_time_for_deploy=300. We try to increase it dynamically.
2025-07-31 17:27:19 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:19 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:19 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=toprightmenu-path=/multisociete/class/actions_multisociete.class.php
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT rowid, label, name, country_code, currency_code, default_lang, active, visible FROM llx_entity WHERE active = 1 AND visible = 1 ORDER BY label ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT rowid, title, url, target FROM llx_bookmark WHERE (fk_user = 1 OR fk_user is NULL OR fk_user = 0) AND entity IN (1) ORDER BY position
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=leftblock-path=/ultimateimmo/class/actions_ultimateimmo.class.php
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=searchform-path=/ultimateimmo/class/actions_ultimateimmo.class.php, context=searchform-path=/cabinetmed/class/actions_cabinetmed.class.php, context=searchform-path=/lead/class/actions_lead.class.php, context=searchform-path=/of/class/actions_of.class.php
2025-07-31 17:27:19 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'mrp_mo' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE elementtype = 'mrp_mo' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'commande' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'commandedet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'product' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'societe' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault, fieldrequired FROM llx_extrafields WHERE elementtype = 'commande' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault, fieldrequired FROM llx_extrafields WHERE elementtype = 'commandedet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 INFO    ::1             Module modFTP not qualified
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'conferenceorboothattendee' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 INFO    ::1             Module modWebServicesClient not qualified
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'ticket' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT code, label, description FROM llx_c_action_trigger ORDER by rang
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'actioncomm' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             useNPR
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.rowid FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.recuperableonly = 1
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'product' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax1_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax2_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'product' AND entity IN (0,1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax1_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax2_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT DISTINCT tc.rowid, tc.code, tc.libelle, tc.position FROM llx_c_type_contact as tc WHERE tc.element='commande' AND tc.source='external' ORDER BY position ASC, code ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT DISTINCT tc.rowid, tc.code, tc.libelle, tc.position FROM llx_c_type_contact as tc WHERE tc.element='commande' AND tc.source='external' ORDER BY position ASC, code ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'expedition' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'expeditiondet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'product' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 INFO    ::1             Module modIntracommreport not qualified
2025-07-31 17:27:19 INFO    ::1             Module modPaybox not qualified
2025-07-31 17:27:19 INFO    ::1             Module modZapier not qualified
2025-07-31 17:27:19 DEBUG   ::1             useNPR
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.rowid FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.recuperableonly = 1
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'product' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax1_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax2_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'product' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax1_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax2_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax1_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax2_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND  elementtype = 'product_fournisseur_price' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax1_type <> '0'
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT t.localtax1, t.localtax2 FROM llx_c_tva as t, llx_c_country as c WHERE t.fk_pays = c.rowid AND c.code = 'TN' AND t.active = 1 AND t.localtax2_type <> '0'
2025-07-31 17:27:19 INFO    ::1             Module modCollab not qualified
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'bom_bom' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'bom_bomline' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE elementtype = 'bom_bom' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE elementtype = 'bom_bomline' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'user' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'user' AND entity IN (0,1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'propal' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'propaldet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'product' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'societe' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'propal' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'propaldet' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'contrat' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'contratdet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'fichinter' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'fichinterdet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'facture_fourn' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'facture_fourn_det' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'facture_fourn' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'commande_fournisseur' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'commande_fournisseurdet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'facture_fourn' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'facture_fourn_det' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'commande_fournisseur' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'commande_fournisseurdet' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'warehouse' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'product' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'product_lot' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'adherent' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'adherent' AND entity IN (0,1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'resource' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'resource' AND entity IN (0,1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE elementtype = 'facture' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE elementtype = 'facture_det' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'facture' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'facturedet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'product' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'societe' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'facture' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'product' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'societe' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'societe' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'adherent' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'socpeople' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'projet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'user' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT DISTINCT tc.rowid, tc.code, tc.libelle, tc.position FROM llx_c_type_contact as tc WHERE tc.element='order_supplier' AND tc.source='external' ORDER BY position ASC, code ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT DISTINCT tc.rowid, tc.code, tc.libelle, tc.position FROM llx_c_type_contact as tc WHERE tc.element='order_supplier' AND tc.source='external' ORDER BY position ASC, code ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'reception' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'commande_fournisseur_dispatch' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'projet' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'projet_task' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'projet' AND entity IN (0,1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'projet_task' AND entity IN (0,1)
2025-07-31 17:27:19 INFO    ::1             Module modWebServices not qualified
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=userdao-path=/absence/class/actions_absence.class.php
2025-07-31 17:27:19 DEBUG   ::1             User::loadParentOf
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT fk_user as id_parent, rowid as id_son FROM llx_user WHERE fk_user <> 0 AND entity IN (0,1)
2025-07-31 17:27:19 DEBUG   ::1             User::get_full_tree get user list
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT DISTINCT u.rowid, u.firstname, u.lastname, u.fk_user, u.fk_soc, u.login, u.email, u.gender, u.admin, u.statut, u.photo, u.entity FROM llx_user as u WHERE u.entity IN (0,1)
2025-07-31 17:27:19 DEBUG   ::1             User::get_full_tree call to build_path_from_id_user
2025-07-31 17:27:19 DEBUG   ::1             User::get_full_tree dol_sort_array
2025-07-31 17:27:19 INFO    ::1             Build childid for id = 1
2025-07-31 17:27:19 INFO    ::1             Module modDeplacement not qualified
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'holiday' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'user' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'societe' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'socpeople' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'societe' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'societe' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, fieldrequired FROM llx_extrafields WHERE type <> 'separate' AND elementtype = 'socpeople' AND entity IN (0, 1)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'expensereport' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'user' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/recruitment/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/knowledgemanagement/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/partnership/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/bankimport/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/fraisdeport/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/planformation/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/numberingpack/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/paymentschedule/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/packaging/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/themespack/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/subtotal/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/lims/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/cabinetmed/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'cabinetmed_cons' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/absence/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/declinaison/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/visitemedicale/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/btp/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/caisse/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/gestionnaireparc/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/holiday/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/saftpt/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Module modSaftPt not qualified
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/smi/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/dolicar/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/hierarchie/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/salesman/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/lcr/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/jouroff/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/concatpdf/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/quality/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/accountingexport/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/fullcalendar/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/invoiceslate/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/grapefruit/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/emcontract/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/advancedproductsearch/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/tvacerfa/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/shippableorder/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/modulebi/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/revertinvoice/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/dolisirh/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/bon_retour/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/mandarin/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/valideur/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/salesobjectives/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/thmtasktime/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/factor/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/discountrules/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/postit/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/dossierimport/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/ecotaxdeee/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/saturne/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/digiriskdolibarr/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'Ticket' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'Ticket' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault, fieldrequired FROM llx_extrafields WHERE elementtype = 'risk' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault, fieldrequired FROM llx_extrafields WHERE elementtype = 'riskassessment' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/free2product/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/gantt/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/ultimateimmo/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/douchette/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/procedure/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/reappromultientrepot/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/lead/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT name, label, type, param, fieldcomputed, fielddefault FROM llx_extrafields WHERE elementtype = 'lead' AND type <> 'separate' AND entity IN (0, 1) ORDER BY pos ASC
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/poppy/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/ordo/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/numberwords/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/scrumboard/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/digiquali/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=get_sheet_linkable_objects-path=/dolimeet/class/actions_dolimeet.class.php, context=get_sheet_linkable_objects-path=/dolicar/class/actions_dolicar.class.php
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/paiedolibarr/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/workstationatm/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/consogazoil/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/dolimeet/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/multisociete/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/nomenclature/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/ship2bill/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/tresorerie/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/abricot/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/of/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/supplierorderfromorder/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/supplierassessment/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/selectbank/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/employerloan/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/scanner/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/insurance/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/gmao/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/compartmentstracking/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Scan directory /Applications/MAMP/dolibarr18/sinedtyi/custom/purchase_request/core/modules/ for module descriptor files (modXXX.class.php)
2025-07-31 17:27:19 INFO    ::1             Save lastsearch_values_tmp_admin/modules.php={"search_keyword":"multisoc"} (systematic recording of last search criterias)
2025-07-31 17:27:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=get_sheet_linkable_objects-path=/dolimeet/class/actions_dolimeet.class.php, context=get_sheet_linkable_objects-path=/dolicar/class/actions_dolicar.class.php
2025-07-31 17:27:19 INFO    ::1             --- End access to /sinedtyi/admin/modules.php
2025-07-31 17:27:19 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:27:19 NOTICE  ::1             --- Access to GET /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:19 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:20 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:20 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:20 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:20 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:20 INFO    ::1             --- End access to /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php
2025-07-31 17:27:20 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:20 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:20 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:20 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:20 NOTICE  ::1             --- Access to GET /sinedtyi/custom/postit/css/postit.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:20 INFO    ::1             --- End access to /sinedtyi/custom/postit/css/postit.css.php
2025-07-31 17:27:20 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:20 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:20 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:20 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:20 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/css/insurance.css.php - action=, massaction=
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:20 INFO    ::1             --- End access to /sinedtyi/custom/insurance/css/insurance.css.php
2025-07-31 17:27:20 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:20 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:20 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:20 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:20 NOTICE  ::1             --- Access to GET /sinedtyi/custom/discountrules/js/discountrules.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:20 INFO    ::1             --- End access to /sinedtyi/custom/discountrules/js/discountrules.js.php
2025-07-31 17:27:20 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:27:20 NOTICE  ::1             --- Access to GET /sinedtyi/custom/multisociete/js/multisociete.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:20 INFO    ::1             --- End access to /sinedtyi/custom/multisociete/js/multisociete.js.php
2025-07-31 17:27:20 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:20 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:20 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:20 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:20 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/js/insurance.js.php - action=, massaction=
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:20 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:20 INFO    ::1             --- End access to /sinedtyi/custom/insurance/js/insurance.js.php
2025-07-31 17:27:21 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:21 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:21 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:21 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:21 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:21 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:21 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:21 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:21 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:21 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:21 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:21 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:21 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:21 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:21 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:21 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:21 INFO    ::1             dolnotif_nb_test_for_page=2
2025-07-31 17:27:21 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:27:21' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:27:21 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:27:22 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:22 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:22 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:22 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:22 NOTICE  ::1             --- Access to GET /sinedtyi/index.php - action=, massaction=
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:22 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:22 DEBUG   ::1             HookManager::initHooks Loading hooks: context=index-path=/purchaserequest/class/actions_purchaserequest.class.php, context=index-path=/ultimateimmo/class/actions_ultimateimmo.class.php, context=index-path=/cabinetmed/class/actions_cabinetmed.class.php, context=index-path=/postit/class/actions_postit.class.php, context=index-path=/grapefruit/class/actions_grapefruit.class.php
2025-07-31 17:27:22 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:22 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:22 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:22 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:22 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:22 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:22 DEBUG   ::1             HookManager::initHooks Loading hooks: context=toprightmenu-path=/multisociete/class/actions_multisociete.class.php
2025-07-31 17:27:22 DEBUG   ::1             sql=SELECT rowid, label, name, country_code, currency_code, default_lang, active, visible FROM llx_entity WHERE active = 1 AND visible = 1 ORDER BY label ASC
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, title, url, target FROM llx_bookmark WHERE (fk_user = 1 OR fk_user is NULL OR fk_user = 0) AND entity IN (1) ORDER BY position
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=leftblock-path=/ultimateimmo/class/actions_ultimateimmo.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=searchform-path=/ultimateimmo/class/actions_ultimateimmo.class.php, context=searchform-path=/cabinetmed/class/actions_cabinetmed.class.php, context=searchform-path=/lead/class/actions_lead.class.php, context=searchform-path=/of/class/actions_of.class.php
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:23 DEBUG   ::1             InfoBox::listBoxes get default box list for mode=activated userid=
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT b.rowid, b.position, b.box_order, b.fk_user, d.rowid as box_id, d.file, d.note, d.tms FROM llx_boxes as b, llx_boxes_def as d WHERE b.box_id = d.rowid AND b.entity IN (0,1) AND b.position = 0 AND b.fk_user = 0 ORDER BY b.box_order
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /multisociete/core/boxes/multisocietewidget1.php
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'multisocietewidget1' into file '/multisociete/core/boxes/multisocietewidget1.php'
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /smi/core/boxes/smiwidgetactions.php
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'smiwidgetactions' into file '/smi/core/boxes/smiwidgetactions.php'
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /smi/core/boxes/smiwidgetindicators.php
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'smiwidgetindicators' into file '/smi/core/boxes/smiwidgetindicators.php'
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /smi/core/boxes/smiwidgetdashboard.php
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'smiwidgetdashboard' into file '/smi/core/boxes/smiwidgetdashboard.php'
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /fraisdeport/core/boxes/MyBox.php
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'MyBox' into file '/fraisdeport/core/boxes/MyBox.php'
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /digiriskdolibarr/core/boxes/box_riskassessmentdocument.php
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'box_riskassessmentdocument' into file '/digiriskdolibarr/core/boxes/box_riskassessmentdocument.php'
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /productautoparts/core/boxes/productautopartswidget1.php
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'productautopartswidget1' into file '/productautoparts/core/boxes/productautopartswidget1.php'
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /core/boxes/box_members.php
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'box_members' into file '/core/boxes/box_members.php'
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'scrumboard_box' into file '/scrumboard/core/boxes/scrumboard_box.php'
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /Declinaison/core/boxes/MyBox.php
2025-07-31 17:27:23 WARNING ::1             Failed to load box 'MyBox' into file '/Declinaison/core/boxes/MyBox.php'
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT sp.rowid as id, sp.lastname, sp.firstname, sp.civility as civility_id, sp.datec, sp.tms, sp.fk_soc, sp.statut as status, sp.address, sp.zip, sp.town, sp.phone, sp.phone_perso, sp.phone_mobile, sp.email as spemail, s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.client, s.code_fournisseur, s.code_compta_fournisseur, s.fournisseur, s.code_compta, s.code_compta_fournisseur, s.logo, s.email, s.entity, co.label as country, co.code as country_code FROM llx_socpeople as sp LEFT JOIN llx_c_country as co ON sp.fk_pays = co.rowid LEFT JOIN llx_societe as s ON sp.fk_soc = s.rowid WHERE sp.entity IN (1) ORDER BY sp.tms DESC LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 28
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 28
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 23
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 23
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 22
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 22
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 21
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 21
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 20
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 20
2025-07-31 17:27:23 INFO    ::1             box_contacts::showBox
2025-07-31 17:27:23 INFO    ::1             FactureStats::getNbByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getNbByMonthWithPrevYear_customer_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(f.datef,'%m') as dm, COUNT(*) as nb FROM llx_facture as f WHERE f.datef BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND (f.fk_statut <> 3 OR f.close_code <> 'replaced') AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(f.datef,'%m') as dm, COUNT(*) as nb FROM llx_facture as f WHERE f.datef BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND (f.fk_statut <> 3 OR f.close_code <> 'replaced') AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(f.datef,'%m') as dm, COUNT(*) as nb FROM llx_facture as f WHERE f.datef BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND (f.fk_statut <> 3 OR f.close_code <> 'replaced') AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             FactureStats::getNbByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getNbByMonthWithPrevYear_customer_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type= this->MaxValue=30
2025-07-31 17:27:23 INFO    ::1             FactureStats::getAmountByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getAmountByMonthWithPrevYear_customer_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(datef,'%m') as dm, SUM(f.total_ht) FROM llx_facture as f WHERE f.datef BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND (f.fk_statut <> 3 OR f.close_code <> 'replaced') AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(datef,'%m') as dm, SUM(f.total_ht) FROM llx_facture as f WHERE f.datef BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND (f.fk_statut <> 3 OR f.close_code <> 'replaced') AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(datef,'%m') as dm, SUM(f.total_ht) FROM llx_facture as f WHERE f.datef BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND (f.fk_statut <> 3 OR f.close_code <> 'replaced') AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             FactureStats::getAmountByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getAmountByMonthWithPrevYear_customer_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type= this->MaxValue=900000
2025-07-31 17:27:23 INFO    ::1             box_graph_invoices_permonth::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.client, s.code_compta, s.logo, s.email, s.entity, s.tva_intra, s.siren as idprof1, s.siret as idprof2, s.ape as idprof3, s.idprof4, s.idprof5, s.idprof6, f.ref, f.date_lim_reglement as datelimite, f.type, f.datef as date, f.total_ht, f.total_tva, f.total_ttc, f.paye, f.fk_statut as status, f.rowid as facid, SUM(pf.amount) as am FROM llx_societe as s, llx_facture as f LEFT JOIN llx_paiement_facture as pf ON f.rowid = pf.fk_facture WHERE f.fk_soc = s.rowid AND f.entity IN (1) AND f.paye = 0 AND fk_statut = 1 GROUP BY s.rowid, s.nom, s.name_alias, s.code_client, s.client, s.logo, s.email, s.entity, s.tva_intra, s.siren, s.siret, s.ape, s.idprof4, s.idprof5, s.idprof6, s.code_compta, f.rowid, f.ref, f.date_lim_reglement, f.type, f.datef, f.total_ht, f.total_tva, f.total_ttc, f.paye, f.fk_statut ORDER BY datelimite ASC, f.ref ASC  LIMIT 6
2025-07-31 17:27:23 DEBUG   ::1             Translate::loadCacheCurrencies
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT code_iso, label, unicode FROM llx_c_currencies WHERE active = 1 AND code_iso = 'TND'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 38
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 38
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(f.total_ht) as total_ht  FROM llx_societe as s, llx_facture as f LEFT JOIN llx_paiement_facture as pf ON f.rowid = pf.fk_facture WHERE f.fk_soc = s.rowid AND f.entity IN (1) AND f.paye = 0 AND fk_statut = 1
2025-07-31 17:27:23 INFO    ::1             box_factures_imp::showBox
2025-07-31 17:27:23 DEBUG   ::1             box_comptes::loadBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT b.rowid, b.ref, b.label, b.bank,b.number, b.courant, b.clos, b.rappro, b.url, b.code_banque, b.code_guichet, b.cle_rib, b.bic, b.iban_prefix as iban, b.domiciliation, b.proprio, b.owner_address, b.account_number, b.currency_code, b.min_allowed, b.min_desired, comment, b.fk_accountancy_journal, aj.code as accountancy_journal FROM llx_bank_account as b LEFT JOIN llx_accounting_journal as aj ON aj.rowid=b.fk_accountancy_journal WHERE b.entity = 1 AND clos = 0 ORDER BY label LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT sum(amount) as amount FROM llx_bank WHERE fk_account = 2
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT sum(amount) as amount FROM llx_bank WHERE fk_account = 3
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT sum(amount) as amount FROM llx_bank WHERE fk_account = 1
2025-07-31 17:27:23 INFO    ::1             box_comptes::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_fournisseur, s.code_compta_fournisseur, s.fournisseur, s.logo, s.email, s.entity, c.rowid, c.ref, c.tms, c.date_commande, c.total_ht, c.total_tva, c.total_ttc, c.fk_statut as status FROM llx_societe as s, llx_commande_fournisseur as c WHERE c.fk_soc = s.rowid AND c.entity IN (1) ORDER BY c.tms DESC, c.ref DESC  LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_supplier_orders::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_fournisseur, s.code_compta_fournisseur, s.fournisseur, s.logo, s.email, s.entity, s.tva_intra, s.siren, s.siret, s.ape, s.idprof4, s.idprof5, s.idprof6, f.rowid as facid, f.ref, f.ref_supplier, f.date_lim_reglement as datelimite, f.datef as df, f.total_ht, f.total_tva, f.total_ttc, f.paye, f.fk_statut as status, f.type, f.tms, SUM(pf.amount) as am FROM llx_societe as s,llx_facture_fourn as f LEFT JOIN llx_paiementfourn_facturefourn as pf ON f.rowid = pf.fk_facturefourn WHERE f.fk_soc = s.rowid AND f.entity IN (1) AND f.paye = 0 AND fk_statut = 1 GROUP BY s.rowid, s.nom, s.name_alias, s.code_fournisseur, s.code_compta_fournisseur, s.fournisseur, s.logo, s.email, s.entity, s.tva_intra, s.siren, s.siret, s.ape, s.idprof4, s.idprof5, s.idprof6, f.rowid, f.ref, f.ref_supplier, f.date_lim_reglement, f.type, f.datef, f.total_ht, f.total_tva, f.total_ttc, f.paye, f.fk_statut, f.tms ORDER BY datelimite DESC, f.ref_supplier DESC  LIMIT 6
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 39
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 39
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 30
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 30
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(f.total_ht) as total_ht  FROM llx_societe as s,llx_facture_fourn as f LEFT JOIN llx_paiementfourn_facturefourn as pf ON f.rowid = pf.fk_facturefourn WHERE f.fk_soc = s.rowid AND f.entity IN (1) AND f.paye = 0 AND fk_statut = 1
2025-07-31 17:27:23 INFO    ::1             box_factures_fourn_imp::showBox
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getNbByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getNbByMonthWithPrevYear_supplier_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, COUNT(*) as nb FROM llx_commande_fournisseur as c WHERE c.date_commande BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, COUNT(*) as nb FROM llx_commande_fournisseur as c WHERE c.date_commande BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, COUNT(*) as nb FROM llx_commande_fournisseur as c WHERE c.date_commande BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getNbByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getNbByMonthWithPrevYear_supplier_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type= this->MaxValue=30
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getAmountByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getAmountByMonthWithPrevYear_supplier_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, SUM(c.total_ht) FROM llx_commande_fournisseur as c WHERE c.date_commande BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, SUM(c.total_ht) FROM llx_commande_fournisseur as c WHERE c.date_commande BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, SUM(c.total_ht) FROM llx_commande_fournisseur as c WHERE c.date_commande BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getAmountByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getAmountByMonthWithPrevYear_supplier_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type= this->MaxValue=50000000
2025-07-31 17:27:23 INFO    ::1             box_graph_orders_supplier_permonth::showBox
2025-07-31 17:27:23 DEBUG   ::1             Lead::_load_status sql=SELECT rowid, code, label, active FROM llx_c_lead_status WHERE active=1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, code, label, active FROM llx_c_lead_status WHERE active=1
2025-07-31 17:27:23 DEBUG   ::1             Lead::_load_type sql=SELECT rowid, code, label FROM llx_c_lead_type  WHERE active=1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, code, label FROM llx_c_lead_type  WHERE active=1
2025-07-31 17:27:23 DEBUG   ::1             Lead::fetchAll sql=SELECT t.rowid, t.ref, t.ref_ext, t.ref_int, t.fk_c_status, t.fk_c_type, t.fk_soc, t.date_closure, t.amount_prosp, t.fk_user_resp, t.description, t.note_private, t.note_public, t.fk_user_author, t.datec, t.fk_user_mod, t.tms FROM llx_lead as t LEFT JOIN llx_societe as so ON so.rowid=t.fk_soc LEFT JOIN llx_user as usr ON usr.rowid=t.fk_user_resp LEFT JOIN llx_c_lead_status as leadsta ON leadsta.rowid=t.fk_c_status LEFT JOIN llx_c_lead_type as leadtype ON leadtype.rowid=t.fk_c_type LEFT JOIN llx_lead_extrafields as leadextra ON leadextra.fk_object=t.rowid WHERE t.entity IN (1) AND t.date_closure<='2025-07-31 17:27:23' ORDER BY t.date_closure DESC  LIMIT 6 
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid, t.ref, t.ref_ext, t.ref_int, t.fk_c_status, t.fk_c_type, t.fk_soc, t.date_closure, t.amount_prosp, t.fk_user_resp, t.description, t.note_private, t.note_public, t.fk_user_author, t.datec, t.fk_user_mod, t.tms FROM llx_lead as t LEFT JOIN llx_societe as so ON so.rowid=t.fk_soc LEFT JOIN llx_user as usr ON usr.rowid=t.fk_user_resp LEFT JOIN llx_c_lead_status as leadsta ON leadsta.rowid=t.fk_c_status LEFT JOIN llx_c_lead_type as leadtype ON leadtype.rowid=t.fk_c_type LEFT JOIN llx_lead_extrafields as leadextra ON leadextra.fk_object=t.rowid WHERE t.entity IN (1) AND t.date_closure<='2025-07-31 17:27:23' ORDER BY t.date_closure DESC  LIMIT 6
2025-07-31 17:27:23 INFO    ::1             box_lead_late::showBox
2025-07-31 17:27:23 DEBUG   ::1             box_birthdays_members::loadBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT u.rowid, u.firstname, u.lastname, u.societe, u.birth, date_format(u.birth, '%d') as daya, u.email, u.statut as status, u.datefin FROM llx_adherent as u WHERE u.entity IN (1) AND u.statut = 1 AND  date_format( u.birth, '%c') = '7' ORDER BY daya ASC LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_birthdays_members::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.ref as product_ref, p.rowid as productid, p.tosell, p.tobuy, p.tobatch, c.rowid, c.date_creation, c.tms, c.ref, c.status, c.fk_user_valid FROM llx_product as p, llx_bom_bom as c WHERE c.fk_product = p.rowid AND c.entity = 1 ORDER BY c.tms DESC, c.ref DESC LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 INFO    ::1             box_boms::showBox
2025-07-31 17:27:23 DEBUG   ::1             box_birthdays::loadBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT u.rowid, u.firstname, u.lastname, u.birth as datea, date_format(u.birth, '%d') as daya, 'birth' as typea, u.email, u.statut as status FROM llx_user as u WHERE u.entity IN (0,1) AND u.statut = 1 AND  date_format( u.birth, '%c') = '7' AND u.birth < '2025-01-01 00:00:00' UNION SELECT u.rowid, u.firstname, u.lastname, u.dateemployment as datea, date_format(u.dateemployment, '%d') as daya, 'employment' as typea, u.email, u.statut as status FROM llx_user as u WHERE u.entity IN (0,1) AND u.statut = 1 AND  date_format( u.dateemployment, '%c') = '7' AND u.dateemployment < '2025-01-01 00:00:00' ORDER BY daya ASC LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_birthdays::showBox
2025-07-31 17:27:23 INFO    ::1             visitemedicale_box::showBox
2025-07-31 17:27:23 DEBUG   ::1             box_prospect::loadBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity, s.fk_stcomm, s.datec, s.tms, s.status FROM llx_societe as s WHERE s.client IN (2, 3) AND s.entity IN (1) ORDER BY s.tms DESC LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_prospect::showBox
2025-07-31 17:27:23 DEBUG   ::1             box_ficheinter::loadBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT f.rowid, f.ref, f.fk_soc, f.fk_statut as status, f.datec, f.date_valid as datev, f.tms as datem, s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity FROM llx_societe as s, llx_fichinter as f WHERE f.fk_soc = s.rowid  AND f.entity = 1 ORDER BY f.tms DESC LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 1
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_ficheinter::showBox
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getNbByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getNbByMonthWithPrevYear_customer_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, COUNT(*) as nb FROM llx_commande as c WHERE c.date_commande BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, COUNT(*) as nb FROM llx_commande as c WHERE c.date_commande BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, COUNT(*) as nb FROM llx_commande as c WHERE c.date_commande BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getNbByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getNbByMonthWithPrevYear_customer_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type= this->MaxValue=20
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getAmountByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getAmountByMonthWithPrevYear_customer_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, SUM(c.total_ht) FROM llx_commande as c WHERE c.date_commande BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, SUM(c.total_ht) FROM llx_commande as c WHERE c.date_commande BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(c.date_commande,'%m') as dm, SUM(c.total_ht) FROM llx_commande as c WHERE c.date_commande BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND c.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getAmountByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getAmountByMonthWithPrevYear_customer_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type= this->MaxValue=3000000
2025-07-31 17:27:23 INFO    ::1             box_graph_orders_permonth::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity, p.rowid, p.ref, p.fk_statut as status, p.datep as dp, p.datec, p.fin_validite, p.date_cloture, p.total_ht, p.total_tva, p.total_ttc, p.tms FROM llx_propal as p, llx_societe as s WHERE p.fk_soc = s.rowid AND p.entity IN (1) ORDER BY p.tms DESC, p.ref DESC  LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 32
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 32
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_propales::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.nom as name, s.rowid as socid, s.email, s.client, s.fournisseur, s.code_client, s.code_fournisseur, s.code_compta, s.code_compta_fournisseur, c.rowid, c.ref, c.statut as fk_statut, c.date_contrat, c.datec, c.tms as date_modification, c.fin_validite, c.date_cloture, c.ref_customer, c.ref_supplier FROM llx_societe as s, llx_contrat as c WHERE c.fk_soc = s.rowid AND c.entity = 1 ORDER BY c.tms DESC, c.ref DESC  LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_contracts::showBox
2025-07-31 17:27:23 INFO    ::1             FactureStats::getNbByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getNbByMonthWithPrevYear_supplier_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(f.datef,'%m') as dm, COUNT(*) as nb FROM llx_facture_fourn as f WHERE f.datef BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(f.datef,'%m') as dm, COUNT(*) as nb FROM llx_facture_fourn as f WHERE f.datef BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(f.datef,'%m') as dm, COUNT(*) as nb FROM llx_facture_fourn as f WHERE f.datef BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             FactureStats::getNbByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getNbByMonthWithPrevYear_supplier_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type= this->MaxValue=20
2025-07-31 17:27:23 INFO    ::1             FactureStats::getAmountByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getAmountByMonthWithPrevYear_supplier_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(datef,'%m') as dm, SUM(f.total_ht) FROM llx_facture_fourn as f WHERE f.datef BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(datef,'%m') as dm, SUM(f.total_ht) FROM llx_facture_fourn as f WHERE f.datef BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(datef,'%m') as dm, SUM(f.total_ht) FROM llx_facture_fourn as f WHERE f.datef BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND  f.fk_statut >= 0 AND f.entity IN (1) AND f.type IN (0,1,2,5) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             FactureStats::getAmountByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getAmountByMonthWithPrevYear_supplier_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type= this->MaxValue=50000000
2025-07-31 17:27:23 INFO    ::1             box_graph_invoices_supplier_permonth::showBox
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getAllByProductEntry cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getAllByProductEntry_customer_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             CommandeStats::_getAllByProduct
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT product.ref, COUNT(product.ref) as nb, SUM(tl.total_ht) as total, AVG(tl.total_ht) as avg FROM llx_commande as c INNER JOIN llx_commandedet as tl ON c.rowid = tl.fk_commande INNER JOIN llx_product as product ON tl.fk_product = product.rowid WHERE c.entity IN (1) AND c.date_commande BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' GROUP BY product.ref ORDER BY nb DESC
2025-07-31 17:27:23 INFO    ::1             CommandeStats::getAllByProductEntry save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/CommandeStats_getAllByProductEntry_customer_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type=pie this->MaxValue=30
2025-07-31 17:27:23 INFO    ::1             FactureStats::getAllByProductEntry cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getAllByProductEntry_customer_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             FactureStats::_getAllByProduct
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT product.ref, COUNT(product.ref) as nb, SUM(tl.total_ht) as total, AVG(tl.total_ht) as avg FROM llx_facture as f, llx_facturedet as tl, llx_product as product WHERE  f.fk_statut >= 0 AND f.entity IN (1) AND (f.fk_statut <> 3 OR f.close_code <> 'replaced') AND f.type IN (0,1,2,5) AND f.rowid = tl.fk_facture AND tl.fk_product = product.rowid AND f.datef BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' GROUP BY product.ref ORDER BY nb DESC
2025-07-31 17:27:23 INFO    ::1             FactureStats::getAllByProductEntry save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/FactureStats_getAllByProductEntry_customer_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type=pie this->MaxValue=60
2025-07-31 17:27:23 INFO    ::1             box_graph_product_distribution::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.label, p.price, p.ref, p.price_base_type, p.price_ttc, p.fk_product_type, p.tms, p.tosell, p.tobuy, p.barcode, p.seuil_stock_alerte, p.entity, p.accountancy_code_sell, p.accountancy_code_sell_intra, p.accountancy_code_sell_export, p.accountancy_code_buy, p.accountancy_code_buy_intra, p.accountancy_code_buy_export, SUM((CASE WHEN s.reel IS NULL THEN 0 ELSE s.reel END)) as total_stock FROM llx_product as p LEFT JOIN llx_product_stock as s on p.rowid = s.fk_product WHERE p.entity IN (1) AND p.seuil_stock_alerte > 0 GROUP BY p.rowid, p.ref, p.label, p.price, p.price_base_type, p.price_ttc, p.fk_product_type, p.tms, p.tosell, p.tobuy, p.barcode, p.seuil_stock_alerte, p.entity, p.accountancy_code_sell, p.accountancy_code_sell_intra, p.accountancy_code_sell_export, p.accountancy_code_buy, p.accountancy_code_buy_intra, p.accountancy_code_buy_export HAVING SUM((CASE WHEN s.reel IS NULL THEN 0 ELSE s.reel END)) < p.seuil_stock_alerte ORDER BY p.seuil_stock_alerte DESC LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 62 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/PDR015/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/PDR015'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 69 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/PDR022/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/PDR022'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 61 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/PDR014/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/PDR014'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 68 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/PDR021/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/PDR021'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 47 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/PDR008/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/PDR008'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 INFO    ::1             box_produits_alerte_stock::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.ref, p.title, p.fk_statut as status, p.public, p.fk_soc, s.nom as name, s.name_alias FROM llx_projet as p LEFT JOIN llx_societe as s on p.fk_soc = s.rowid WHERE p.entity IN (1) AND p.fk_statut = 1 ORDER BY p.datec DESC
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(*) as nb, sum(progress) as totprogress FROM llx_projet as p LEFT JOIN llx_projet_task as pt on pt.fk_projet = p.rowid WHERE p.entity IN (1) AND p.rowid = 20
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(*) as nb, sum(progress) as totprogress FROM llx_projet as p LEFT JOIN llx_projet_task as pt on pt.fk_projet = p.rowid WHERE p.entity IN (1) AND p.rowid = 18
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(*) as nb, sum(progress) as totprogress FROM llx_projet as p LEFT JOIN llx_projet_task as pt on pt.fk_projet = p.rowid WHERE p.entity IN (1) AND p.rowid = 10
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(*) as nb, sum(progress) as totprogress FROM llx_projet as p LEFT JOIN llx_projet_task as pt on pt.fk_projet = p.rowid WHERE p.entity IN (1) AND p.rowid = 6
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(*) as nb, sum(progress) as totprogress FROM llx_projet as p LEFT JOIN llx_projet_task as pt on pt.fk_projet = p.rowid WHERE p.entity IN (1) AND p.rowid = 7
2025-07-31 17:27:23 INFO    ::1             box_project::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.label, p.ref, p.price, p.price_base_type, p.price_ttc, p.fk_product_type, p.tms, p.tosell, p.tobuy, p.fk_price_expression, p.entity, p.accountancy_code_sell, p.accountancy_code_sell_intra, p.accountancy_code_sell_export, p.accountancy_code_buy, p.accountancy_code_buy_intra, p.accountancy_code_buy_export, p.barcode FROM llx_product as p WHERE p.entity IN (1) ORDER BY p.datec DESC LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 39 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/ORDPORT/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/ORDPORT'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 38 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/FRAI001/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/FRAI001'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 37 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/PR0004/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/PR0004'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 36 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/PR0003/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/PR0003'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT label FROM llx_product_lang WHERE fk_product = 35 AND lang = 'fr_FR' LIMIT 1
2025-07-31 17:27:23 INFO    ::1             files.lib.php::dol_dir_list path=/Applications/MAMP/dolibarr18/sinedtyi/documents/produit/FOURN001/ types=files recursive=0 filter= excludefilter="(\\.meta|_preview.*\\.png)$"
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, label, entity, filename, filepath, fullpath_orig, keywords, cover, gen_or_uploaded, extraparams, date_c, tms as date_m, fk_user_c, fk_user_m, acl, position, share FROM llx_ecm_files WHERE entity = 1 AND filepath = 'produit/FOURN001'
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 INFO    ::1             box_produits::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid, t.datelastrun, t.datenextrun, t.datestart, t.label, t.status, t.test, t.lastresult, t.processing FROM llx_cronjob as t WHERE status <> 0 AND entity IN (0, 1) ORDER BY t.datelastrun DESC
2025-07-31 17:27:23 INFO    ::1             box_scheduled_jobs::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.nom as name, s.rowid as socid, s.email, s.client, s.fournisseur, s.code_client, s.code_fournisseur, s.code_compta, s.code_compta_fournisseur, c.rowid, c.ref, c.statut as contract_status, c.ref_customer, c.ref_supplier, cd.rowid as cdid, cd.label, cd.description, cd.tms as datem, cd.statut as contractline_status, cd.product_type as type, cd.date_fin_validite as date_line, p.rowid as product_id, p.ref as product_ref, p.label as product_label, p.fk_product_type as product_type, p.entity as product_entity, p.tobuy, p.tosell FROM (llx_societe as s INNER JOIN llx_contrat as c ON s.rowid = c.fk_soc INNER JOIN llx_contratdet as cd ON c.rowid = cd.fk_contrat LEFT JOIN llx_product as p ON cd.fk_product = p.rowid) WHERE c.entity = 1 ORDER BY c.tms DESC LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_services_contracts::showBox
2025-07-31 17:27:23 INFO    ::1             ModeleBoxes::showBox
2025-07-31 17:27:23 DEBUG   ::1             BoxAbsence sql=
2025-07-31 17:27:23 DEBUG   ::1             sql=
2025-07-31 17:27:23 INFO    ::1             box_absence::showBox
2025-07-31 17:27:23 DEBUG   ::1             box_customers_outstanding_bill_reached::loadBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity, s.outstanding_limit, s.datec, s.tms, s.status FROM llx_societe as s WHERE s.client IN (1, 3) AND s.entity IN (1) AND s.outstanding_limit > 0 AND s.rowid IN (SELECT fk_soc from llx_facture as f WHERE f.fk_statut = 1 and f.fk_soc = s.rowid) ORDER BY s.tms DESC
2025-07-31 17:27:23 INFO    ::1             box_customers_outstanding_bill_reached::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT f.rowid as facid, f.ref, f.type, f.total_ht, f.total_tva, f.total_ttc, f.datef as date, f.paye, f.fk_statut as status, f.datec, f.tms, f.date_lim_reglement as datelimite, s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity, s.tva_intra, s.siren as idprof1, s.siret as idprof2, s.ape as idprof3, s.idprof4, s.idprof5, s.idprof6, SUM(pf.amount) as am FROM llx_facture as f LEFT JOIN llx_paiement_facture as pf ON f.rowid = pf.fk_facture, llx_societe as s WHERE f.fk_soc = s.rowid AND f.entity IN (1) GROUP BY s.rowid, s.nom, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity, s.tva_intra, s.siren, s.siret, s.ape, s.idprof4, s.idprof5, s.idprof6, f.rowid, f.ref, f.type, f.total_ht, f.total_tva, f.total_ttc, f.datef, f.paye, f.fk_statut, f.datec, f.tms, f.date_lim_reglement ORDER BY f.tms DESC, f.ref DESC  LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_factures::showBox
2025-07-31 17:27:23 DEBUG   ::1             Machines::fetchAll
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid,t.ref,t.label,t.description,t.date_creation,t.tms,t.fk_user_creat,t.fk_user_modif,t.fk_parent,t.niveau_hierarchique,t.code_equipement,t.equipe,t.magasin,t.marque,t.modele,t.numero_serie,t.date_achat,t.heures,t.etat_general,t.type_huile_moteur,t.quantite_huile_moteur,t.type,t.immatriculation,t.kilometrage,t.derniere_revision,t.batiment,t.etage,t.zone,t.emplacement_precis,t.coordonnees_gps,t.fk_fabricant,t.fk_fournisseur,t.reference_fabricant,t.manuel_utilisation,t.date_fin_garantie,t.duree_garantie_mois,t.fk_contrat_maintenance,t.type_contrat_maintenance,t.puissance,t.tension,t.poids,t.dimensions,t.capacite,t.vitesse_max,t.temperature_fonctionnement,t.type_huile_hydrau,t.quantite_huile_hydrau,t.type_huile_pont,t.quantite_huile_pont,t.ref_filtre_air,t.ref_filtre_carburant,t.ref_filtre_huile_moteur,t.ref_filtre_huile_hydrau,t.type_bougies,t.ref_lames,t.ref_courroie_lame,t.ref_courroie_moteur,t.ref_plateau_tondeuse,t.instructions_maintenance,t.frequence_maintenance_heures,t.frequence_maintenance_jours,t.prochaine_maintenance_date,t.prochaine_maintenance_heures,t.etat_actuel,t.criticite,t.disponibilite_requise,t.valeur_achat,t.valeur_actuelle,t.cout_maintenance_annuel,t.stat_nb_pannes,t.stat_cumul_temps_intervention,t.stat_temps_arret_total,t.stat_derniere_panne,t.stat_mtbf,t.stat_mttr,t.stat_disponibilite,t.certifications_requises,t.date_derniere_inspection,t.date_prochaine_inspection,t.niveau_securite,t.import_key,t.status FROM llx_gestionnaireparc_machines as t WHERE 1 = 1 AND (etat_actuel IN (0))
2025-07-31 17:27:23 DEBUG   ::1             Machines::fetchAll
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid,t.ref,t.label,t.description,t.date_creation,t.tms,t.fk_user_creat,t.fk_user_modif,t.fk_parent,t.niveau_hierarchique,t.code_equipement,t.equipe,t.magasin,t.marque,t.modele,t.numero_serie,t.date_achat,t.heures,t.etat_general,t.type_huile_moteur,t.quantite_huile_moteur,t.type,t.immatriculation,t.kilometrage,t.derniere_revision,t.batiment,t.etage,t.zone,t.emplacement_precis,t.coordonnees_gps,t.fk_fabricant,t.fk_fournisseur,t.reference_fabricant,t.manuel_utilisation,t.date_fin_garantie,t.duree_garantie_mois,t.fk_contrat_maintenance,t.type_contrat_maintenance,t.puissance,t.tension,t.poids,t.dimensions,t.capacite,t.vitesse_max,t.temperature_fonctionnement,t.type_huile_hydrau,t.quantite_huile_hydrau,t.type_huile_pont,t.quantite_huile_pont,t.ref_filtre_air,t.ref_filtre_carburant,t.ref_filtre_huile_moteur,t.ref_filtre_huile_hydrau,t.type_bougies,t.ref_lames,t.ref_courroie_lame,t.ref_courroie_moteur,t.ref_plateau_tondeuse,t.instructions_maintenance,t.frequence_maintenance_heures,t.frequence_maintenance_jours,t.prochaine_maintenance_date,t.prochaine_maintenance_heures,t.etat_actuel,t.criticite,t.disponibilite_requise,t.valeur_achat,t.valeur_actuelle,t.cout_maintenance_annuel,t.stat_nb_pannes,t.stat_cumul_temps_intervention,t.stat_temps_arret_total,t.stat_derniere_panne,t.stat_mtbf,t.stat_mttr,t.stat_disponibilite,t.certifications_requises,t.date_derniere_inspection,t.date_prochaine_inspection,t.niveau_securite,t.import_key,t.status FROM llx_gestionnaireparc_machines as t WHERE 1 = 1 AND (etat_actuel IN (1))
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT * FROM llx_gestionnaireparc_pannes WHERE 1=1 AND gravite = 0 AND etat = 0
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT * FROM llx_gestionnaireparc_pannes WHERE 1=1 AND gravite = 1 AND etat = 0
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT * FROM llx_gestionnaireparc_pannes WHERE 1=1 AND phase_reparation = 0 AND etat = 0
2025-07-31 17:27:23 DEBUG   ::1             Interventions::fetchAll
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid,t.compte_rendu,t.date_creation,t.tms,t.fk_user_creat,t.fk_user_modif,t.intervention_type,t.fk_machine,t.date_intervention,t.agent,t.duree_intervention,t.fk_panne,t.statut_intervention,t.description,t.ref,t.operation1,t.ref_operation1,t.operation2,t.ref_operation2,t.operation3,t.ref_operation3,t.operation4,t.ref_operation4,t.operation5,t.ref_operation5,t.operation6,t.ref_operation6,t.operation7,t.ref_operation7,t.operation8,t.ref_operation8,t.operation9,t.ref_operation9,t.operation10,t.ref_operation10,t.operation11,t.ref_operation11,t.operation12,t.ref_operation12,t.operation13,t.ref_operation13,t.operation14,t.ref_operation14,t.operation15,t.ref_operation15,t.resultat_intervention,t.fk_actioncomm,t.maintenance_type,t.panne FROM llx_gestionnaireparc_interventions as t WHERE 1 = 1 AND (intervention_type IN (0) AND statut_intervention IN (0))
2025-07-31 17:27:23 DEBUG   ::1             Interventions::fetchAll
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid,t.compte_rendu,t.date_creation,t.tms,t.fk_user_creat,t.fk_user_modif,t.intervention_type,t.fk_machine,t.date_intervention,t.agent,t.duree_intervention,t.fk_panne,t.statut_intervention,t.description,t.ref,t.operation1,t.ref_operation1,t.operation2,t.ref_operation2,t.operation3,t.ref_operation3,t.operation4,t.ref_operation4,t.operation5,t.ref_operation5,t.operation6,t.ref_operation6,t.operation7,t.ref_operation7,t.operation8,t.ref_operation8,t.operation9,t.ref_operation9,t.operation10,t.ref_operation10,t.operation11,t.ref_operation11,t.operation12,t.ref_operation12,t.operation13,t.ref_operation13,t.operation14,t.ref_operation14,t.operation15,t.ref_operation15,t.resultat_intervention,t.fk_actioncomm,t.maintenance_type,t.panne FROM llx_gestionnaireparc_interventions as t WHERE 1 = 1 AND (intervention_type IN (1) AND statut_intervention IN (0))
2025-07-31 17:27:23 DEBUG   ::1             Interventions::fetchAll
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid,t.compte_rendu,t.date_creation,t.tms,t.fk_user_creat,t.fk_user_modif,t.intervention_type,t.fk_machine,t.date_intervention,t.agent,t.duree_intervention,t.fk_panne,t.statut_intervention,t.description,t.ref,t.operation1,t.ref_operation1,t.operation2,t.ref_operation2,t.operation3,t.ref_operation3,t.operation4,t.ref_operation4,t.operation5,t.ref_operation5,t.operation6,t.ref_operation6,t.operation7,t.ref_operation7,t.operation8,t.ref_operation8,t.operation9,t.ref_operation9,t.operation10,t.ref_operation10,t.operation11,t.ref_operation11,t.operation12,t.ref_operation12,t.operation13,t.ref_operation13,t.operation14,t.ref_operation14,t.operation15,t.ref_operation15,t.resultat_intervention,t.fk_actioncomm,t.maintenance_type,t.panne FROM llx_gestionnaireparc_interventions as t WHERE 1 = 1 AND (statut_intervention IN (1))
2025-07-31 17:27:23 DEBUG   ::1             Interventions::fetchAll
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid,t.compte_rendu,t.date_creation,t.tms,t.fk_user_creat,t.fk_user_modif,t.intervention_type,t.fk_machine,t.date_intervention,t.agent,t.duree_intervention,t.fk_panne,t.statut_intervention,t.description,t.ref,t.operation1,t.ref_operation1,t.operation2,t.ref_operation2,t.operation3,t.ref_operation3,t.operation4,t.ref_operation4,t.operation5,t.ref_operation5,t.operation6,t.ref_operation6,t.operation7,t.ref_operation7,t.operation8,t.ref_operation8,t.operation9,t.ref_operation9,t.operation10,t.ref_operation10,t.operation11,t.ref_operation11,t.operation12,t.ref_operation12,t.operation13,t.ref_operation13,t.operation14,t.ref_operation14,t.operation15,t.ref_operation15,t.resultat_intervention,t.fk_actioncomm,t.maintenance_type,t.panne FROM llx_gestionnaireparc_interventions as t WHERE 1 = 1 AND (statut_intervention IN (2))
2025-07-31 17:27:23 INFO    ::1             gestionnaireparcwidget1::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_fournisseur, s.code_compta_fournisseur, s.fournisseur, s.logo, s.email, s.entity, c.rowid, c.ref, c.tms, c.date_commande, c.date_livraison as delivery_date, c.total_ht, c.total_tva, c.total_ttc, c.fk_statut as status FROM llx_societe as s, llx_commande_fournisseur as c WHERE c.fk_soc = s.rowid AND c.entity IN (1) AND c.date_livraison IS NOT NULL AND c.fk_statut IN (3, 4) ORDER BY c.date_livraison ASC, c.fk_statut ASC LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_supplier_orders_awaiting_reception::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_fournisseur, s.code_compta_fournisseur, s.fournisseur, s.logo, s.email, s.entity, f.rowid as facid, f.ref, f.ref_supplier, f.total_ht, f.total_tva, f.total_ttc, f.paye, f.fk_statut as status, f.datef as date, f.datec as datec, f.date_lim_reglement as datelimite, f.tms, f.type FROM llx_societe as s, llx_facture_fourn as f WHERE f.fk_soc = s.rowid AND f.entity = 1 ORDER BY f.tms DESC, f.ref DESC  LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             FactureFournisseur::getSommePaiement
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT sum(amount) as amount, sum(multicurrency_amount) as multicurrency_amount FROM llx_paiementfourn_facturefourn WHERE fk_facturefourn = 21
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             FactureFournisseur::getSommePaiement
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT sum(amount) as amount, sum(multicurrency_amount) as multicurrency_amount FROM llx_paiementfourn_facturefourn WHERE fk_facturefourn = 27
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             FactureFournisseur::getSommePaiement
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT sum(amount) as amount, sum(multicurrency_amount) as multicurrency_amount FROM llx_paiementfourn_facturefourn WHERE fk_facturefourn = 23
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             FactureFournisseur::getSommePaiement
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT sum(amount) as amount, sum(multicurrency_amount) as multicurrency_amount FROM llx_paiementfourn_facturefourn WHERE fk_facturefourn = 22
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             FactureFournisseur::getSommePaiement
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT sum(amount) as amount, sum(multicurrency_amount) as multicurrency_amount FROM llx_paiementfourn_facturefourn WHERE fk_facturefourn = 31
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_factures_fourn::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_fournisseur, s.code_compta_fournisseur, s.fournisseur, s.logo, s.email, s.entity, s.datec, s.tms, s.status FROM llx_societe as s WHERE s.fournisseur = 1 AND s.entity IN (1) ORDER BY s.tms DESC  LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 43
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 43
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 40
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 40
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_fournisseurs::showBox
2025-07-31 17:27:23 DEBUG   ::1             Lead::_load_status sql=SELECT rowid, code, label, active FROM llx_c_lead_status WHERE active=1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, code, label, active FROM llx_c_lead_status WHERE active=1
2025-07-31 17:27:23 DEBUG   ::1             Lead::_load_type sql=SELECT rowid, code, label FROM llx_c_lead_type  WHERE active=1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, code, label FROM llx_c_lead_type  WHERE active=1
2025-07-31 17:27:23 DEBUG   ::1             Lead::fetchAll sql=SELECT t.rowid, t.ref, t.ref_ext, t.ref_int, t.fk_c_status, t.fk_c_type, t.fk_soc, t.date_closure, t.amount_prosp, t.fk_user_resp, t.description, t.note_private, t.note_public, t.fk_user_author, t.datec, t.fk_user_mod, t.tms FROM llx_lead as t LEFT JOIN llx_societe as so ON so.rowid=t.fk_soc LEFT JOIN llx_user as usr ON usr.rowid=t.fk_user_resp LEFT JOIN llx_c_lead_status as leadsta ON leadsta.rowid=t.fk_c_status LEFT JOIN llx_c_lead_type as leadtype ON leadtype.rowid=t.fk_c_type LEFT JOIN llx_lead_extrafields as leadextra ON leadextra.fk_object=t.rowid WHERE t.entity IN (1) ORDER BY t.ref DESC  LIMIT 6 
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid, t.ref, t.ref_ext, t.ref_int, t.fk_c_status, t.fk_c_type, t.fk_soc, t.date_closure, t.amount_prosp, t.fk_user_resp, t.description, t.note_private, t.note_public, t.fk_user_author, t.datec, t.fk_user_mod, t.tms FROM llx_lead as t LEFT JOIN llx_societe as so ON so.rowid=t.fk_soc LEFT JOIN llx_user as usr ON usr.rowid=t.fk_user_resp LEFT JOIN llx_c_lead_status as leadsta ON leadsta.rowid=t.fk_c_status LEFT JOIN llx_c_lead_type as leadtype ON leadtype.rowid=t.fk_c_type LEFT JOIN llx_lead_extrafields as leadextra ON leadextra.fk_object=t.rowid WHERE t.entity IN (1) ORDER BY t.ref DESC  LIMIT 6
2025-07-31 17:27:23 INFO    ::1             box_lead_current::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.ref as product_ref, p.rowid as productid, p.tosell, p.tobuy, p.tobatch, c.rowid, c.date_creation, c.tms, c.ref, c.status FROM llx_product as p, llx_mrp_mo as c WHERE c.fk_product = p.rowid AND c.entity = 1 ORDER BY c.tms DESC, c.ref DESC LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=productdao-path=/of/class/actions_of.class.php, context=productdao-path=/packaging/class/actions_packaging.class.php
2025-07-31 17:27:23 INFO    ::1             box_mos::showBox
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=userdao-path=/absence/class/actions_absence.class.php
2025-07-31 17:27:23 INFO    ::1             box_lastlogin::showBox
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=index-path=/purchaserequest/class/actions_purchaserequest.class.php, context=index-path=/ultimateimmo/class/actions_ultimateimmo.class.php, context=index-path=/cabinetmed/class/actions_cabinetmed.class.php, context=index-path=/postit/class/actions_postit.class.php, context=index-path=/grapefruit/class/actions_grapefruit.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(c.rowid) as nb FROM llx_cabinetmed_cons as c WHERE c.entity = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(rowid) as nb FROM llx_societe WHERE canvas = 'patient@cabinetmed' WHERE c.entity = 1
2025-07-31 17:27:23 ERR     ::1             DoliDBMysqli::query SQL Error message: DB_ERROR_SYNTAX You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE c.entity = 1' at line 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT COUNT(DISTINCT u.rowid) as nb FROM llx_user as u WHERE u.entity IN (0,1) AND u.statut > 0
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(a.rowid) as nb FROM llx_adherent as a WHERE a.statut > 0 AND a.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(ex.rowid) as nb FROM llx_expensereport as ex WHERE ex.fk_statut > 0 AND ex.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(h.rowid) as nb FROM llx_holiday as h WHERE h.statut > 1 AND h.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(s.rowid) as nb, s.client FROM llx_societe as s WHERE s.client IN (1,2,3) AND s.entity IN (1) GROUP BY s.client
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(s.rowid) as nb FROM llx_societe as s WHERE s.fournisseur = 1 AND s.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(sp.rowid) as nb FROM llx_socpeople as sp WHERE sp.entity IN (1) AND (sp.priv='0' OR (sp.priv='1' AND sp.fk_user_creat = 1))
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(p.rowid) as nb, fk_product_type FROM llx_product as p WHERE p.entity IN (1) GROUP BY fk_product_type
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(p.rowid) as nb FROM llx_projet as p WHERE p.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(p.rowid) as nb FROM llx_propal as p LEFT JOIN llx_societe as s ON p.fk_soc = s.rowid WHERE p.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(co.rowid) as nb FROM llx_commande as co LEFT JOIN llx_societe as s ON co.fk_soc = s.rowid WHERE co.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(f.rowid) as nb FROM llx_facture as f LEFT JOIN llx_societe as s ON f.fk_soc = s.rowid WHERE f.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(d.rowid) as nb FROM llx_don as d WHERE d.fk_statut > 0 AND d.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(p.rowid) as nb FROM llx_supplier_proposal as p LEFT JOIN llx_societe as s ON p.fk_soc = s.rowid WHERE p.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(co.rowid) as nb FROM llx_commande_fournisseur as co LEFT JOIN llx_societe as s ON co.fk_soc = s.rowid WHERE co.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(f.rowid) as nb FROM llx_facture_fourn as f LEFT JOIN llx_societe as s ON f.fk_soc = s.rowid WHERE f.entity = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(c.rowid) as nb FROM llx_contrat as c LEFT JOIN llx_societe as s ON c.fk_soc = s.rowid WHERE c.entity = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(fi.rowid) as nb FROM llx_fichinter as fi LEFT JOIN llx_societe as s ON fi.fk_soc = s.rowid WHERE fi.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(p.rowid) as nb FROM llx_ticket as p LEFT JOIN llx_societe as s ON p.fk_soc = s.rowid WHERE p.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT count(r.rowid) as nb FROM llx_resource as r WHERE r.entity IN (1)
2025-07-31 17:27:23 INFO    ::1             box_dolibarr_state_board::showBox
2025-07-31 17:27:23 DEBUG   ::1             box_clients::loadBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity, s.datec, s.tms, s.status FROM llx_societe as s WHERE s.client IN (1, 3) AND s.entity IN (1) ORDER BY s.tms DESC LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 25
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 25
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_clients::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity, c.ref, c.tms, c.rowid, c.date_commande, c.ref_client, c.fk_statut, c.fk_user_valid, c.facture, c.total_ht, c.total_tva, c.total_ttc FROM llx_commande as c, llx_societe as s WHERE c.fk_soc = s.rowid AND c.entity IN (1) ORDER BY c.tms DESC, c.ref DESC  LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT SUM(total_ttc) as total_unpaid FROM llx_facture WHERE fk_statut =1 AND paye = 0 AND date_lim_reglement < NOW() AND fk_soc = 24
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT max(date_commande) as last_order FROM llx_commande WHERE fk_statut >= 1 AND fk_soc = 24
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_commandes::showBox
2025-07-31 17:27:23 INFO    ::1             PropaleStats::getNbByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/PropaleStats_getNbByMonthWithPrevYear_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             PropaleStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(p.datep,'%m') as dm, COUNT(*) as nb FROM llx_propal as p WHERE p.datep BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND p.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             PropaleStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(p.datep,'%m') as dm, COUNT(*) as nb FROM llx_propal as p WHERE p.datep BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND p.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             PropaleStats::_getNbByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(p.datep,'%m') as dm, COUNT(*) as nb FROM llx_propal as p WHERE p.datep BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND p.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             PropaleStats::getNbByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/PropaleStats_getNbByMonthWithPrevYear_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type=bars,bars,bars this->MaxValue=2
2025-07-31 17:27:23 INFO    ::1             PropaleStats::getAmountByMonthWithPrevYear cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/PropaleStats_getAmountByMonthWithPrevYear_fr_FR_entity.1_user1.cache is not found or older than now - cachedelay (1753982843 - 86400) so we can't use it.
2025-07-31 17:27:23 DEBUG   ::1             PropaleStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(p.datep,'%m') as dm, SUM(p.total_ht) FROM llx_propal as p WHERE p.datep BETWEEN '2023-01-01 00:00:00' AND '2023-12-31 23:59:59' AND p.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             PropaleStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(p.datep,'%m') as dm, SUM(p.total_ht) FROM llx_propal as p WHERE p.datep BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59' AND p.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 DEBUG   ::1             PropaleStats::_getAmountByMonth
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT date_format(p.datep,'%m') as dm, SUM(p.total_ht) FROM llx_propal as p WHERE p.datep BETWEEN '2025-01-01 00:00:00' AND '2025-12-31 23:59:59' AND p.entity IN (1) GROUP BY dm ORDER BY dm DESC
2025-07-31 17:27:23 INFO    ::1             PropaleStats::getAmountByMonthWithPrevYear save cache file /Applications/MAMP/dolibarr18/sinedtyi/documents/users/temp/PropaleStats_getAmountByMonthWithPrevYear_fr_FR_entity.1_user1.cache onto disk.
2025-07-31 17:27:23 INFO    ::1             DolGraph::draw_chart this->type=bars,bars,bars this->MaxValue=5000
2025-07-31 17:27:23 INFO    ::1             box_graph_propales_permonth::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity, e.ref, e.tms, e.rowid, e.ref_customer, e.fk_statut, e.fk_user_valid, c.ref as commande_ref, c.rowid as commande_id FROM llx_expedition as e LEFT JOIN llx_element_element as el ON e.rowid = el.fk_target AND el.targettype = 'shipping' AND el.sourcetype IN ('commande') LEFT JOIN llx_commande as c ON el.fk_source = c.rowid AND el.sourcetype IN ('commande') AND el.targettype = 'shipping' LEFT JOIN llx_societe as s ON s.rowid = e.fk_soc WHERE e.entity IN (1) ORDER BY e.date_delivery, e.ref DESC  LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=thirdpartydao-path=/invoiceslate/class/actions_invoiceslate.class.php
2025-07-31 17:27:23 INFO    ::1             box_shipments::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT  c.rowid, c.ref, c.statut as fk_statut, c.date_contrat, c.ref_customer, c.ref_supplier, s.nom as name, s.rowid as socid, s.email, s.client, s.fournisseur, s.code_client, s.code_fournisseur, s.code_compta, s.code_compta_fournisseur, MIN(cd.date_fin_validite) as date_line, COUNT(cd.rowid) as nb_services FROM llx_contrat as c, llx_societe s, llx_contratdet as cd WHERE cd.statut = 4 AND cd.date_fin_validite <= '2025-07-31 17:27:23' AND c.entity = 1 AND c.fk_soc=s.rowid AND cd.fk_contrat=c.rowid AND c.statut > 0 GROUP BY c.rowid, c.ref, c.statut, c.date_contrat, c.ref_customer, c.ref_supplier, s.nom, s.rowid, s.email, s.client, s.fournisseur, s.code_client, s.code_fournisseur, s.code_compta, s.code_compta_fournisseur ORDER BY date_line ASC LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_services_expired::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid as id, t.ref, t.track_id, t.fk_soc, t.fk_user_create, t.fk_user_assign, t.subject, t.message, t.fk_statut as status, t.type_code, t.category_code, t.severity_code, t.datec, t.date_read, t.date_close, t.origin_email, type.label as type_label, category.label as category_label, severity.label as severity_label, s.nom as company_name, s.email as socemail, s.client, s.fournisseur FROM llx_ticket as t LEFT JOIN llx_c_ticket_type as type ON type.code=t.type_code LEFT JOIN llx_c_ticket_category as category ON category.code=t.category_code LEFT JOIN llx_c_ticket_severity as severity ON severity.code=t.severity_code LEFT JOIN llx_societe as s ON s.rowid=t.fk_soc WHERE t.entity IN (1) ORDER BY t.datec DESC, t.rowid DESC  LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_last_ticket::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.ref, p.title, p.fk_soc, p.fk_statut as status, p.fk_opp_status as opp_status, p.opp_percent, p.opp_amount, p.public, s.nom as name, s.name_alias, cls.code as opp_status_code FROM llx_projet as p LEFT JOIN llx_societe as s on p.fk_soc = s.rowid LEFT JOIN llx_c_lead_status as cls on p.fk_opp_status = cls.rowid WHERE p.entity IN (1) AND p.usage_opportunity = 1 AND p.fk_opp_status > 0 AND p.fk_statut IN (0,1) ORDER BY p.datec DESC
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 INFO    ::1             box_project_opportunities::showBox
2025-07-31 17:27:23 DEBUG   ::1             box_actions::loadBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT a.id, a.label, a.datep as dp, a.percent as percentage, ta.code, ta.libelle as type_label, s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity FROM llx_c_actioncomm AS ta, llx_actioncomm AS a LEFT JOIN llx_societe as s ON a.fk_soc = s.rowid WHERE a.fk_action = ta.id AND a.entity IN (1) AND a.percent >= 0 AND a.percent < 100 ORDER BY a.datep ASC LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_actions::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT t.rowid as id, t.ref, t.track_id, t.fk_soc, t.fk_user_create, t.fk_user_assign, t.subject, t.message, t.fk_statut, t.type_code, t.category_code, t.severity_code, t.datec, t.tms as datem, t.date_read, t.date_close, t.origin_email , type.label as type_label, category.label as category_label, severity.label as severity_label, s.nom as company_name, s.email as socemail, s.client, s.fournisseur FROM llx_ticket as t LEFT JOIN llx_c_ticket_type as type ON type.code=t.type_code LEFT JOIN llx_c_ticket_category as category ON category.code=t.category_code LEFT JOIN llx_c_ticket_severity as severity ON severity.code=t.severity_code LEFT JOIN llx_societe as s ON s.rowid=t.fk_soc WHERE t.entity IN (1) ORDER BY t.tms DESC, t.rowid DESC LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_last_modified_ticket::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT pt.rowid, pt.ref, pt.fk_projet, pt.fk_task_parent, pt.datec, pt.dateo, pt.datee, pt.datev, pt.label, pt.description, pt.duration_effective, pt.planned_workload, pt.progress, p.rowid project_id, p.ref project_ref, p.title project_title, p.fk_statut FROM llx_projet_task as pt JOIN llx_projet as p ON (pt.fk_projet = p.rowid) WHERE  pt.entity = 1 AND p.fk_statut = 1 AND (pt.progress < 100 OR pt.progress IS NULL )  AND p.usage_task = 1  ORDER BY pt.datee ASC, pt.dateo ASC LIMIT 5
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=projectdao-path=/nomenclature/class/actions_nomenclature.class.php
2025-07-31 17:27:23 INFO    ::1             box_task::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT k.rowid as id, k.date_creation, k.ref, k.lang, k.question, k.status as status FROM llx_knowledgemanagement_knowledgerecord as k WHERE k.entity IN (1) AND k.status > 0 ORDER BY k.date_creation DESC, k.rowid DESC  LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_last_knowledgerecord::showBox
2025-07-31 17:27:23 DEBUG   ::1             box_actions_future::loadBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT a.id, a.label, a.datep as dp, a.percent as percentage, ta.code, ta.libelle as type_label, s.rowid as socid, s.nom as name, s.name_alias, s.code_client, s.code_compta, s.client, s.logo, s.email, s.entity FROM llx_c_actioncomm AS ta, llx_actioncomm AS a LEFT JOIN llx_societe as s ON a.fk_soc = s.rowid WHERE a.fk_action = ta.id AND a.entity IN (1) AND a.datep > '2025-07-31 17:27:23' ORDER BY a.datep ASC LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_actions_future::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT b.title, b.url, b.target, b.favicon FROM llx_bookmark as b WHERE fk_user = 1 AND b.entity = 1 ORDER BY position ASC LIMIT 5
2025-07-31 17:27:23 INFO    ::1             box_bookmarks::showBox
2025-07-31 17:27:23 INFO    ::1             ModeleBoxes::showBox
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT a.id, a.datep as dp FROM llx_actioncomm as a WHERE 1 = 1 AND a.percent >= 0 AND a.percent < 100 AND a.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.fk_statut as status, p.fk_opp_status, p.datee as datee FROM (llx_projet as p) LEFT JOIN llx_societe as s on p.fk_soc = s.rowid WHERE p.fk_statut = 1 AND p.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT ctc.rowid, ctc.code FROM llx_c_type_contact as ctc WHERE ctc.element = 'project' AND ctc.source = 'internal'
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT DISTINCT p.rowid, p.ref FROM llx_projet as p LEFT JOIN llx_element_contact as ec ON ec.element_id = p.rowid WHERE p.entity IN (1) AND ( p.public = 1 OR ( ec.fk_c_type_contact IN (70,42,41,69,68) AND ec.fk_socpeople = 1) )
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid as projectid, p.fk_statut as projectstatus, t.rowid as taskid, t.progress as progress, t.fk_statut as status, t.dateo as date_start, t.datee as datee FROM llx_projet as p, llx_projet_task as t WHERE p.entity IN (1) AND p.fk_statut = 1 AND t.fk_projet = p.rowid AND (t.progress IS NULL OR t.progress < 100)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.ref, p.datec as datec, p.fin_validite as datefin, p.total_ht FROM llx_propal as p WHERE p.entity IN (1) AND p.fk_statut = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.ref, p.datec as datec, p.fin_validite as datefin, p.total_ht FROM llx_propal as p WHERE p.entity IN (1) AND p.fk_statut = 2
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.ref, p.datec as datec, p.date_cloture as datefin FROM llx_supplier_proposal as p WHERE p.entity IN (1) AND p.fk_statut = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.ref, p.datec as datec, p.date_cloture as datefin FROM llx_supplier_proposal as p WHERE p.entity IN (1) AND p.fk_statut = 2
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT c.rowid, c.date_creation as datec, c.date_commande, c.date_livraison as delivery_date, c.fk_statut, c.total_ht FROM llx_commande as c WHERE c.entity IN (1) AND ((c.fk_statut IN (1,2)) OR (c.fk_statut = 3 AND c.facture = 0))
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT c.rowid, c.date_creation as datec, c.date_commande, c.fk_statut, c.date_livraison as delivery_date, c.total_ht FROM llx_commande_fournisseur as c WHERE c.entity = 1 AND c.fk_statut IN (1, 2)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT c.rowid, c.date_creation as datec, c.date_commande, c.fk_statut, c.date_livraison as delivery_date, c.total_ht FROM llx_commande_fournisseur as c WHERE c.entity = 1 AND c.fk_statut IN (3, 4)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT cd.rowid, cd.date_ouverture_prevue as datefin FROM llx_contrat as c, llx_contratdet as cd, llx_societe as s WHERE c.statut = 1 AND c.rowid = cd.fk_contrat AND cd.statut = 0 AND c.fk_soc = s.rowid AND c.entity = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT cd.rowid, cd.date_fin_validite as datefin FROM llx_contrat as c, llx_contratdet as cd, llx_societe as s WHERE c.statut = 1 AND c.rowid = cd.fk_contrat AND cd.statut = 4 AND c.fk_soc = s.rowid AND c.entity = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT p.rowid, p.ref, p.datec as datec FROM llx_ticket as p WHERE p.entity IN (1) AND p.fk_statut NOT IN (8, 9)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT f.rowid, f.date_lim_reglement as datefin, f.fk_statut, f.total_ht FROM llx_facture as f WHERE f.paye=0 AND f.entity IN (1) AND f.fk_statut = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT ff.rowid, ff.date_lim_reglement as datefin, ff.fk_statut as status, ff.total_ht, ff.total_ttc FROM llx_facture_fourn as ff WHERE ff.paye = 0 AND ff.fk_statut > 0 AND ff.entity = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT COUNT(ba.rowid) as nb FROM llx_bank_account as ba WHERE ba.rappro > 0 and ba.clos = 0 AND ba.entity IN (1) AND ba.courant != 2
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT b.rowid, b.datev as datefin FROM llx_bank as b, llx_bank_account as ba WHERE b.rappro=0 AND b.fk_account = ba.rowid AND ba.entity IN (1) AND (ba.rappro = 1 AND ba.courant != 2) AND clos = 0
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT b.rowid, b.datev as datefin FROM llx_bank as b, llx_bank_account as ba WHERE b.fk_account = ba.rowid AND ba.entity IN (1) AND b.fk_type = 'CHQ' AND b.fk_bordereau = 0 AND b.amount > 0
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT a.rowid, a.datefin, a.statut FROM llx_adherent as a, llx_adherent_type as t WHERE a.fk_adherent_type = t.rowid AND a.statut = -1 AND a.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT a.rowid, a.datefin, a.statut FROM llx_adherent as a, llx_adherent_type as t WHERE a.fk_adherent_type = t.rowid AND a.statut = 1 AND a.entity IN (1) AND ((a.datefin IS NULL or a.datefin < '2025-07-31 17:27:23') AND t.subscription = '1')
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT ex.rowid, ex.date_valid FROM llx_expensereport as ex WHERE ex.fk_statut = 2 AND ex.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT ex.rowid, ex.date_valid FROM llx_expensereport as ex WHERE ex.fk_statut = 5 AND ex.entity IN (1)
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=userdao-path=/absence/class/actions_absence.class.php
2025-07-31 17:27:23 DEBUG   ::1             User::loadParentOf
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT fk_user as id_parent, rowid as id_son FROM llx_user WHERE fk_user <> 0 AND entity IN (0,1)
2025-07-31 17:27:23 DEBUG   ::1             User::get_full_tree get user list
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT DISTINCT u.rowid, u.firstname, u.lastname, u.fk_user, u.fk_soc, u.login, u.email, u.gender, u.admin, u.statut, u.photo, u.entity FROM llx_user as u WHERE u.entity IN (0,1)
2025-07-31 17:27:23 DEBUG   ::1             User::get_full_tree call to build_path_from_id_user
2025-07-31 17:27:23 DEBUG   ::1             User::get_full_tree dol_sort_array
2025-07-31 17:27:23 INFO    ::1             Build childid for id = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT h.rowid, h.date_debut FROM llx_holiday as h WHERE h.statut = 2 AND h.entity IN (1) AND (h.fk_user IN (2,1) OR h.fk_validator IN (2,1))
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=get_sheet_linkable_objects-path=/dolimeet/class/actions_dolimeet.class.php, context=get_sheet_linkable_objects-path=/dolicar/class/actions_dolicar.class.php
2025-07-31 17:27:23 INFO    ::1             --- End access to /sinedtyi/index.php
2025-07-31 17:27:23 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:23 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:23 NOTICE  ::1             --- Access to GET /sinedtyi/custom/discountrules/js/discountrules.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:23 INFO    ::1             --- End access to /sinedtyi/custom/discountrules/js/discountrules.js.php
2025-07-31 17:27:23 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:23 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:23 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:23 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:23 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/css/insurance.css.php - action=, massaction=
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:23 INFO    ::1             --- End access to /sinedtyi/custom/insurance/css/insurance.css.php
2025-07-31 17:27:23 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:27:23 NOTICE  ::1             --- Access to GET /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:23 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:24 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:24 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:24 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:24 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:24 INFO    ::1             --- End access to /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php
2025-07-31 17:27:24 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:24 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:24 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:24 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:24 NOTICE  ::1             --- Access to GET /sinedtyi/custom/postit/css/postit.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:24 INFO    ::1             --- End access to /sinedtyi/custom/postit/css/postit.css.php
2025-07-31 17:27:24 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:27:24 NOTICE  ::1             --- Access to GET /sinedtyi/custom/multisociete/js/multisociete.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:24 INFO    ::1             --- End access to /sinedtyi/custom/multisociete/js/multisociete.js.php
2025-07-31 17:27:24 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:24 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:24 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:24 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:24 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/js/insurance.js.php - action=, massaction=
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:24 INFO    ::1             --- End access to /sinedtyi/custom/insurance/js/insurance.js.php
2025-07-31 17:27:24 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:24 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:24 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:24 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:24 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:24 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:24 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:24 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:24 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:24 INFO    ::1             dolnotif_nb_test_for_page=1
2025-07-31 17:27:24 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:27:24' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:27:24 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:27:25 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:25 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:25 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:25 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:25 NOTICE  ::1             --- Access to GET /sinedtyi/custom/postit/script/interface.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:25 DEBUG   ::1             sql=SELECT rowid, entity, status FROM llx_postit  WHERE (fk_user=1 OR fk_user_todo=1 OR status='public' OR status='shared')  AND (fk_object=-1 OR status='shared') AND type_object='global' ORDER BY rowid
2025-07-31 17:27:25 INFO    ::1             --- End access to /sinedtyi/custom/postit/script/interface.php
2025-07-31 17:27:28 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:28 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:28 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:28 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:28 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:28 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:28 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:28 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:28 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:28 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:28 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:28 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:28 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:28 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:28 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:28 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:28 INFO    ::1             dolnotif_nb_test_for_page=1
2025-07-31 17:27:28 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:27:28' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:27:28 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:27:42 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:42 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:42 NOTICE  ::1             --- Access to GET /sinedtyi/debug_live_translation.php - action=, massaction=
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=toprightmenu-path=/multisociete/class/actions_multisociete.class.php
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, label, name, country_code, currency_code, default_lang, active, visible FROM llx_entity WHERE active = 1 AND visible = 1 ORDER BY label ASC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, title, url, target FROM llx_bookmark WHERE (fk_user = 1 OR fk_user is NULL OR fk_user = 0) AND entity IN (1) ORDER BY position
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=leftblock-path=/ultimateimmo/class/actions_ultimateimmo.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=searchform-path=/ultimateimmo/class/actions_ultimateimmo.class.php, context=searchform-path=/cabinetmed/class/actions_cabinetmed.class.php, context=searchform-path=/lead/class/actions_lead.class.php, context=searchform-path=/of/class/actions_of.class.php
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=get_sheet_linkable_objects-path=/dolimeet/class/actions_dolimeet.class.php, context=get_sheet_linkable_objects-path=/dolicar/class/actions_dolicar.class.php
2025-07-31 17:27:42 INFO    ::1             --- End access to /sinedtyi/debug_live_translation.php
2025-07-31 17:27:42 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:27:42 NOTICE  ::1             --- Access to GET /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 INFO    ::1             --- End access to /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php
2025-07-31 17:27:42 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:42 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:42 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/css/insurance.css.php - action=, massaction=
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:42 INFO    ::1             --- End access to /sinedtyi/custom/insurance/css/insurance.css.php
2025-07-31 17:27:42 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:42 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:42 NOTICE  ::1             --- Access to GET /sinedtyi/custom/discountrules/js/discountrules.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:42 INFO    ::1             --- End access to /sinedtyi/custom/discountrules/js/discountrules.js.php
2025-07-31 17:27:42 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:42 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:42 NOTICE  ::1             --- Access to GET /sinedtyi/custom/postit/css/postit.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:42 INFO    ::1             --- End access to /sinedtyi/custom/postit/css/postit.css.php
2025-07-31 17:27:42 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:27:42 NOTICE  ::1             --- Access to GET /sinedtyi/custom/multisociete/js/multisociete.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:42 INFO    ::1             --- End access to /sinedtyi/custom/multisociete/js/multisociete.js.php
2025-07-31 17:27:42 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:42 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:42 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:42 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:42 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/js/insurance.js.php - action=, massaction=
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:27:42 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:27:43 INFO    ::1             --- End access to /sinedtyi/custom/insurance/js/insurance.js.php
2025-07-31 17:27:46 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:46 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:46 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:46 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:46 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:46 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:46 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:46 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:46 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:46 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:46 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:46 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:46 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:46 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:46 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:46 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:46 INFO    ::1             dolnotif_nb_test_for_page=1
2025-07-31 17:27:46 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:27:46' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:27:46 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:27:49 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:27:49 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:27:49 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:27:49 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:27:49 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:27:49 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:27:49 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:27:49 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:49 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:49 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:27:49 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:27:49 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:27:49 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:49 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:49 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:27:49 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:27:49 INFO    ::1             dolnotif_nb_test_for_page=2
2025-07-31 17:27:49 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:27:49' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:27:49 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:28:07 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:07 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:07 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:07 NOTICE  ::1             --- Access to GET /sinedtyi/debug_live_translation.php - action=, massaction=
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:28:07 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:07 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:07 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=toprightmenu-path=/multisociete/class/actions_multisociete.class.php
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, label, name, country_code, currency_code, default_lang, active, visible FROM llx_entity WHERE active = 1 AND visible = 1 ORDER BY label ASC
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, title, url, target FROM llx_bookmark WHERE (fk_user = 1 OR fk_user is NULL OR fk_user = 0) AND entity IN (1) ORDER BY position
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=leftblock-path=/ultimateimmo/class/actions_ultimateimmo.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=searchform-path=/ultimateimmo/class/actions_ultimateimmo.class.php, context=searchform-path=/cabinetmed/class/actions_cabinetmed.class.php, context=searchform-path=/lead/class/actions_lead.class.php, context=searchform-path=/of/class/actions_of.class.php
2025-07-31 17:28:07 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=get_sheet_linkable_objects-path=/dolimeet/class/actions_dolimeet.class.php, context=get_sheet_linkable_objects-path=/dolicar/class/actions_dolicar.class.php
2025-07-31 17:28:07 INFO    ::1             --- End access to /sinedtyi/debug_live_translation.php
2025-07-31 17:28:07 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:07 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:07 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:07 NOTICE  ::1             --- Access to GET /sinedtyi/custom/discountrules/js/discountrules.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:28:07 INFO    ::1             --- End access to /sinedtyi/custom/discountrules/js/discountrules.js.php
2025-07-31 17:28:07 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:07 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:07 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:07 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/css/insurance.css.php - action=, massaction=
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:28:07 INFO    ::1             --- End access to /sinedtyi/custom/insurance/css/insurance.css.php
2025-07-31 17:28:07 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:07 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:07 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:07 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:07 NOTICE  ::1             --- Access to GET /sinedtyi/custom/postit/css/postit.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:07 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:28:08 INFO    ::1             --- End access to /sinedtyi/custom/postit/css/postit.css.php
2025-07-31 17:28:08 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:28:08 NOTICE  ::1             --- Access to GET /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:28:08 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:08 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:08 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:08 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:08 INFO    ::1             --- End access to /sinedtyi/custom/ultimateimmo/css/ultimateimmo.css.php
2025-07-31 17:28:08 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,0) AND entity IN (1,1))
2025-07-31 17:28:08 NOTICE  ::1             --- Access to GET /sinedtyi/custom/multisociete/js/multisociete.js.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:28:08 INFO    ::1             --- End access to /sinedtyi/custom/multisociete/js/multisociete.js.php
2025-07-31 17:28:08 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:08 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:08 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:08 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:08 NOTICE  ::1             --- Access to GET /sinedtyi/custom/insurance/js/insurance.js.php - action=, massaction=
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT transkey, transvalue FROM llx_overwrite_trans where (lang='fr_FR' OR lang IS NULL) AND entity IN (0, 0,1) ORDER BY lang DESC
2025-07-31 17:28:08 DEBUG   ::1             sql=SELECT m.rowid, m.type, m.module, m.fk_menu, m.fk_mainmenu, m.fk_leftmenu, m.url, m.titre, m.prefix, m.langs, m.perms, m.enabled, m.target, m.mainmenu, m.leftmenu, m.position FROM llx_menu as m WHERE m.entity IN (0,1) AND m.menu_handler IN ('eldy','all') AND m.usertype IN (0,2) ORDER BY m.position, m.rowid
2025-07-31 17:28:08 INFO    ::1             --- End access to /sinedtyi/custom/insurance/js/insurance.js.php
2025-07-31 17:28:11 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:11 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:11 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:11 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:11 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:11 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:11 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:11 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:11 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:11 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:11 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:11 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:11 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:11 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:11 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:11 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:11 INFO    ::1             dolnotif_nb_test_for_page=1
2025-07-31 17:28:11 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:28:11' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:28:11 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:28:19 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:19 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:19 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:19 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:19 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:19 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:19 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:19 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:19 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:19 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:19 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:19 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:19 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:19 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:19 INFO    ::1             dolnotif_nb_test_for_page=3
2025-07-31 17:28:19 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:28:19' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:28:19 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:28:25 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:25 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:25 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:25 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:25 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:25 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:25 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:25 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:25 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:25 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:25 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:25 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:25 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:25 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:25 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:25 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:25 INFO    ::1             dolnotif_nb_test_for_page=3
2025-07-31 17:28:25 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:28:25' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:28:25 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:28:28 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:28 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:28 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:28 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:28 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:28 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:28 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:28 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:28 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:28 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:28 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:28 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:28 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:28 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:28 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:28 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:28 INFO    ::1             dolnotif_nb_test_for_page=3
2025-07-31 17:28:28 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:28:28' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:28:28 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:28:41 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:41 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:41 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:41 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:41 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:41 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:41 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:41 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:41 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:41 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:41 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:41 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:41 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:41 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:41 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:41 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:41 INFO    ::1             dolnotif_nb_test_for_page=2
2025-07-31 17:28:41 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:28:41' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:28:41 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:28:49 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:49 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:49 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:49 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:49 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:49 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:49 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:49 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:49 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:49 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:49 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:49 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:49 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:49 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:49 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:49 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:49 INFO    ::1             dolnotif_nb_test_for_page=4
2025-07-31 17:28:49 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:28:49' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:28:49 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:28:55 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:55 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:55 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:55 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:55 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:55 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:55 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:55 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:55 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:55 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:55 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:55 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:55 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:55 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:55 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:55 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:55 INFO    ::1             dolnotif_nb_test_for_page=4
2025-07-31 17:28:55 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:28:55' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:28:55 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
2025-07-31 17:28:58 DEBUG   ::1             - This is an already logged session. _SESSION['dol_login']=sinedtyi _SESSION['dol_entity']=1
2025-07-31 17:28:58 DEBUG   ::1             sql=SELECT u.rowid, u.lastname, u.firstname, u.employee, u.gender, u.civility as civility_code, u.birth, u.email, u.personal_email, u.job, u.socialnetworks, u.signature, u.office_phone, u.office_fax, u.user_mobile, u.personal_mobile, u.address, u.zip, u.town, u.fk_state as state_id, u.fk_country as country_id, u.admin, u.login, u.note_private, u.note_public, u.pass, u.pass_crypted, u.pass_temp, u.api_key, u.fk_soc, u.fk_socpeople, u.fk_member, u.fk_user, u.ldap_sid, u.fk_user_expense_validator, u.fk_user_holiday_validator, u.statut as status, u.lang, u.entity, u.datec as datec, u.tms as datem, u.datelastlogin as datel, u.datepreviouslogin as datep, u.flagdelsessionsbefore, u.iplastlogin, u.ippreviouslogin, u.datelastpassvalidation, u.datestartvalidity, u.dateendvalidity, u.photo as photo, u.openid as openid, u.accountancy_code, u.thm, u.tjm, u.salary, u.salaryextra, u.weeklyhours, u.color, u.dateemployment, u.dateemploymentend, u.fk_warehouse, u.ref_ext, u.default_range, u.default_c_exp_tax_cat, u.national_registration_number, u.ref_employee, c.code as country_code, c.label as country, d.code_departement as state_code, d.nom as state FROM llx_user as u LEFT JOIN llx_c_country as c ON u.fk_country = c.rowid LEFT JOIN llx_c_departements as d ON u.fk_state = d.rowid WHERE u.entity IN (0, 1) AND u.login = 'sinedtyi' ORDER BY u.entity ASC
2025-07-31 17:28:58 DEBUG   ::1             sql=SELECT rowid, name, label, type, size, elementtype, fieldunique, fieldrequired, param, pos, alwayseditable, perms, langs, list, printable, totalizable, fielddefault, fieldcomputed, entity, enabled, help, css, cssview, csslist FROM llx_extrafields WHERE elementtype = 'user' ORDER BY pos
2025-07-31 17:28:58 DEBUG   ::1             sql=SELECT rowid, contrat, thm, horaire, dds, electronic_signature, ticketresto_ok, matricule_paye, efficiency, qc_frequency, control_history_link, paiedolibarr_scall, paiedolibarrmatricule, paiedolibarrsituation_f, paiedolibarrnbrenfants, paiedolibarrcnss, paiedolibarrcimr, paiedolibarrcin, paiedolibarrnbdaywork, professional_qualification, contract_type FROM llx_user_extrafields WHERE fk_object = 1
2025-07-31 17:28:58 DEBUG   ::1             sql=SELECT param, value FROM llx_user_param WHERE fk_user = 1 AND entity = 1
2025-07-31 17:28:58 DEBUG   ::1             DefaultValues::fetchAll
2025-07-31 17:28:58 DEBUG   ::1             sql=SELECT rowid,type,user_id,page,param,value FROM llx_default_values as t WHERE 1 = 1 AND (t.user_id IN (0,1) AND entity IN (0,1))
2025-07-31 17:28:58 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:58 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:58 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_user_rights as ur, llx_rights_def as r WHERE r.id = ur.fk_id AND r.entity = 1 AND ur.entity = 1 AND ur.fk_user= 1 AND r.perms IS NOT NULL AND r.perms NOT LIKE '%_advance'
2025-07-31 17:28:58 DEBUG   ::1             sql=SELECT DISTINCT r.module, r.perms, r.subperms FROM llx_usergroup_rights as gr, llx_usergroup_user as gu, llx_rights_def as r WHERE r.id = gr.fk_id AND gr.entity = 1 AND gu.entity IN (0,1) AND r.entity = 1 AND gr.fk_usergroup = gu.fk_usergroup AND gu.fk_user = 1 AND r.perms IS NOT NULL
2025-07-31 17:28:58 NOTICE  ::1             --- Access to POST /sinedtyi/core/ajax/check_notifications.php - action=, massaction= NOTOKENRENEWAL=1
2025-07-31 17:28:58 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:58 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:58 WARNING ::1             functions::dol_include_once Tried to load unexisting file: /purchaserequest/class/actions_purchaserequest.class.php
2025-07-31 17:28:58 DEBUG   ::1             HookManager::initHooks Loading hooks: context=main-path=/purchaserequest/class/actions_purchaserequest.class.php, context=main-path=/multisociete/class/actions_multisociete.class.php, context=main-path=/digiriskdolibarr/class/actions_digiriskdolibarr.class.php, context=main-path=/digiquali/class/actions_digiquali.class.php, context=main-path=/dossierimport/class/actions_dossierimport.class.php, context=main-path=/postit/class/actions_postit.class.php, context=main-path=/smi/class/actions_smi.class.php
2025-07-31 17:28:58 INFO    ::1             dolnotif_nb_test_for_page=4
2025-07-31 17:28:58 DEBUG   ::1             sql=SELECT a.id as id_agenda, a.code, a.datep, a.label, a.location, ar.rowid as id_reminder, ar.dateremind, ar.fk_user as id_user_reminder FROM llx_actioncomm as a INNER JOIN llx_actioncomm_reminder as ar ON a.id = ar.fk_actioncomm AND ar.fk_user = 1 AND ar.typeremind = 'browser' AND ar.dateremind < '2025-07-31 17:28:58' AND ar.status = 0 AND ar.entity = 1 ORDER BY datep ASC LIMIT 10
2025-07-31 17:28:58 INFO    ::1             --- End access to /sinedtyi/core/ajax/check_notifications.php
