<?php
/**
 * Reset complet de Dolibarr pour éliminer "A text to show"
 * Solution radicale mais efficace
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Reset Complet Dolibarr</title></head><body>';
print '<h1>Reset Complet de Dolibarr</h1>';

if (isset($_GET['action']) && $_GET['action'] == 'reset_complete') {
    print '<h2>Exécution du reset complet</h2>';
    
    $steps = array();
    $errors = array();
    
    try {
        // 1. Sauvegarde des données critiques
        print '<h3>1. Sauvegarde des données critiques</h3>';
        
        $backup_data = array();
        
        // Sauvegarder les utilisateurs
        $sql = "SELECT * FROM " . MAIN_DB_PREFIX . "user WHERE admin = 1";
        $result = $db->query($sql);
        if ($result) {
            while ($obj = $db->fetch_object($result)) {
                $backup_data['admin_users'][] = $obj;
            }
            $steps[] = "Utilisateurs admin sauvegardés";
        }
        
        // Sauvegarder la configuration critique
        $critical_configs = array('MAIN_LANG_DEFAULT', 'MAIN_DB_PREFIX', 'MAIN_URL_ROOT');
        foreach ($critical_configs as $config) {
            $sql = "SELECT * FROM " . MAIN_DB_PREFIX . "const WHERE name = '{$config}'";
            $result = $db->query($sql);
            if ($result && $obj = $db->fetch_object($result)) {
                $backup_data['configs'][$config] = $obj->value;
            }
        }
        $steps[] = "Configuration critique sauvegardée";
        
        // 2. Nettoyage complet des traductions
        print '<h3>2. Nettoyage complet des traductions</h3>';
        
        $translation_tables = array('overwrite_trans', 'c_translations');
        foreach ($translation_tables as $table) {
            $sql = "TRUNCATE TABLE " . MAIN_DB_PREFIX . $table;
            if ($db->query($sql)) {
                $steps[] = "Table {$table} vidée complètement";
            }
        }
        
        // 3. Reset des données d'affichage
        print '<h3>3. Reset des données d\'affichage</h3>';
        
        // Supprimer toutes les configurations d'affichage personnalisées
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%MAIN_SHOW%' OR name LIKE '%DISPLAY%' OR name LIKE '%VIEW%'";
        if ($db->query($sql)) {
            $steps[] = "Configurations d'affichage supprimées";
        }
        
        // 4. Correction massive des données
        print '<h3>4. Correction massive des données</h3>';
        
        // Produits - reset complet des libellés
        $sql = "UPDATE " . MAIN_DB_PREFIX . "product SET 
                label = CASE 
                    WHEN ref IS NOT NULL AND ref != '' THEN ref
                    ELSE CONCAT('Produit ', LPAD(rowid, 6, '0'))
                END,
                ref = CASE 
                    WHEN ref IS NULL OR ref = '' THEN CONCAT('PROD', LPAD(rowid, 6, '0'))
                    ELSE ref
                END";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            $steps[] = "Produits: {$affected} enregistrements normalisés";
        }
        
        // Sociétés - reset complet
        $sql = "UPDATE " . MAIN_DB_PREFIX . "societe SET 
                nom = CASE 
                    WHEN nom IS NOT NULL AND nom != '' AND nom NOT LIKE '%text to show%' THEN nom
                    WHEN name_alias IS NOT NULL AND name_alias != '' THEN name_alias
                    WHEN code_client IS NOT NULL AND code_client != '' THEN CONCAT('Société ', code_client)
                    ELSE CONCAT('Société ', LPAD(rowid, 6, '0'))
                END,
                code_client = CASE 
                    WHEN code_client IS NULL OR code_client = '' THEN CONCAT('CLI', LPAD(rowid, 6, '0'))
                    ELSE code_client
                END";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            $steps[] = "Sociétés: {$affected} enregistrements normalisés";
        }
        
        // Contacts - reset complet
        $sql = "UPDATE " . MAIN_DB_PREFIX . "socpeople SET 
                lastname = CASE 
                    WHEN lastname IS NOT NULL AND lastname != '' AND lastname NOT LIKE '%text to show%' THEN lastname
                    WHEN firstname IS NOT NULL AND firstname != '' THEN CONCAT(firstname, ' (Contact)')
                    ELSE CONCAT('Contact ', LPAD(rowid, 6, '0'))
                END,
                firstname = CASE 
                    WHEN firstname IS NULL OR firstname = '' THEN 'Prénom'
                    ELSE firstname
                END";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            $steps[] = "Contacts: {$affected} enregistrements normalisés";
        }
        
        // 5. Reset des modules problématiques
        print '<h3>5. Reset des modules problématiques</h3>';
        
        // Désactiver temporairement les modules qui peuvent causer des problèmes
        $problematic_modules = array(
            'MAIN_MODULE_ABRICOT',
            'MAIN_MODULE_QUALITY', 
            'MAIN_MODULE_DOUCHETTE'
        );
        
        foreach ($problematic_modules as $module) {
            $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '0' WHERE name = '{$module}'";
            if ($db->query($sql)) {
                $steps[] = "Module {$module} temporairement désactivé";
            }
        }
        
        // 6. Configuration optimale
        print '<h3>6. Application de la configuration optimale</h3>';
        
        $optimal_config = array(
            'MAIN_LANG_DEFAULT' => 'fr_FR',
            'MAIN_MULTILANGS' => '0',
            'MAIN_ENABLE_OVERWRITE_TRANSLATION' => '0',
            'MAIN_OPTIMIZE_SPEED' => '0',
            'MAIN_USE_JQUERY_MULTISELECT' => 'select2',
            'MAIN_DISABLE_JAVASCRIPT' => '0',
            'MAIN_SHOW_TECHNICAL_ID' => '0',
            'PRODUCT_USE_SEARCH_TO_SELECT' => '1',
            'COMPANY_USE_SEARCH_TO_SELECT' => '1'
        );
        
        foreach ($optimal_config as $param => $value) {
            $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                    VALUES ('{$param}', '{$value}', 'chaine', 'Reset optimal config', '0', '1') 
                    ON DUPLICATE KEY UPDATE value = '{$value}'";
            if ($db->query($sql)) {
                $steps[] = "Configuration {$param} = {$value}";
            }
        }
        
        // 7. Nettoyage complet des caches
        print '<h3>7. Nettoyage complet des caches</h3>';
        
        // Supprimer tous les fichiers de cache
        $cache_paths = array(
            DOL_DATA_ROOT . '/admin/temp',
            DOL_DATA_ROOT . '/admin/temp/langs',
            DOL_DATA_ROOT . '/admin/temp/js',
            DOL_DATA_ROOT . '/admin/temp/css'
        );
        
        foreach ($cache_paths as $path) {
            if (is_dir($path)) {
                if (function_exists('dol_delete_dir_recursive')) {
                    dol_delete_dir_recursive($path);
                    $steps[] = "Cache {$path} supprimé";
                }
            }
        }
        
        // Supprimer les caches en base
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%CACHE%'";
        if ($db->query($sql)) {
            $steps[] = "Caches en base supprimés";
        }
        
        // 8. Réinitialisation des sessions
        print '<h3>8. Réinitialisation des sessions</h3>';
        
        // Forcer la déconnexion de tous les utilisateurs
        $sql = "UPDATE " . MAIN_DB_PREFIX . "user SET datestartvalidity = NULL, dateendvalidity = NULL";
        if ($db->query($sql)) {
            $steps[] = "Sessions utilisateurs réinitialisées";
        }
        
        // 9. Reconstruction des index et optimisation
        print '<h3>9. Optimisation de la base de données</h3>';
        
        $tables_to_optimize = array('product', 'societe', 'socpeople', 'facture', 'commande');
        foreach ($tables_to_optimize as $table) {
            $sql = "OPTIMIZE TABLE " . MAIN_DB_PREFIX . $table;
            if ($db->query($sql)) {
                $steps[] = "Table {$table} optimisée";
            }
        }
        
        // 10. Vérification finale
        print '<h3>10. Vérification finale</h3>';
        
        // Compter les enregistrements problématiques restants
        $check_queries = array(
            'product' => "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "product WHERE label LIKE '%text to show%'",
            'societe' => "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "societe WHERE nom LIKE '%text to show%'",
            'socpeople' => "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . "socpeople WHERE lastname LIKE '%text to show%'"
        );
        
        $remaining_issues = 0;
        foreach ($check_queries as $table => $sql) {
            $result = $db->query($sql);
            if ($result) {
                $obj = $db->fetch_object($result);
                $count = $obj->count;
                if ($count > 0) {
                    $remaining_issues += $count;
                    $steps[] = "⚠️ Table {$table}: {$count} problèmes restants";
                } else {
                    $steps[] = "✅ Table {$table}: aucun problème";
                }
            }
        }
        
        if ($remaining_issues == 0) {
            $steps[] = "🎉 SUCCÈS: Aucun problème 'A text to show' détecté";
        }
        
    } catch (Exception $e) {
        $errors[] = "Erreur critique: " . $e->getMessage();
    }
    
    // Affichage des résultats
    if (!empty($steps)) {
        print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>✅ Étapes exécutées :</h4>';
        print '<ul>';
        foreach ($steps as $step) {
            print '<li>' . $step . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    if (!empty($errors)) {
        print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>❌ Erreurs :</h4>';
        print '<ul>';
        foreach ($errors as $error) {
            print '<li>' . $error . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    print '<div style="background: #dc3545; color: white; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center;">';
    print '<h4 style="color: white;">🔄 REDÉMARRAGE OBLIGATOIRE</h4>';
    print '<p style="color: white; font-size: 16px;"><strong>Le reset est terminé. Vous DEVEZ maintenant :</strong></p>';
    print '<ol style="color: white; text-align: left; font-size: 14px;">';
    print '<li><strong>ARRÊTER MAMP complètement</strong></li>';
    print '<li><strong>ATTENDRE 10 secondes</strong></li>';
    print '<li><strong>REDÉMARRER MAMP</strong></li>';
    print '<li><strong>FERMER TOUS les onglets du navigateur</strong></li>';
    print '<li><strong>VIDER le cache du navigateur</strong> (Ctrl+Shift+Delete)</li>';
    print '<li><strong>ROUVRIR Dolibarr dans un nouvel onglet</strong></li>';
    print '<li><strong>SE RECONNECTER</strong></li>';
    print '</ol>';
    print '</div>';
    
} else {
    // Menu principal
    print '<h2>Reset Complet de Dolibarr</h2>';
    
    print '<div style="background: #dc3545; color: white; padding: 20px; border-radius: 5px; margin: 10px 0;">';
    print '<h3 style="color: white;">⚠️ SOLUTION RADICALE</h3>';
    print '<p style="color: white;">Cette solution effectue un <strong>reset complet</strong> de votre installation Dolibarr pour éliminer définitivement le problème "A text to show".</p>';
    print '</div>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🔧 Ce que fait le reset complet :</h3>';
    print '<ul>';
    print '<li>🗑️ <strong>Vide complètement</strong> les tables de traduction</li>';
    print '<li>🔄 <strong>Normalise toutes</strong> les données (produits, sociétés, contacts)</li>';
    print '<li>⚙️ <strong>Applique une configuration optimale</strong></li>';
    print '<li>🚫 <strong>Désactive temporairement</strong> les modules problématiques</li>';
    print '<li>🧹 <strong>Nettoie tous</strong> les caches</li>';
    print '<li>🔍 <strong>Optimise</strong> la base de données</li>';
    print '<li>✅ <strong>Vérifie</strong> que le problème est résolu</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>⚠️ ATTENTION - Lisez avant de continuer :</h3>';
    print '<ul>';
    print '<li>🔸 Cette solution est <strong>RADICALE</strong> mais très efficace</li>';
    print '<li>🔸 Elle va <strong>normaliser toutes vos données</strong></li>';
    print '<li>🔸 Les <strong>traductions personnalisées</strong> seront perdues</li>';
    print '<li>🔸 Certains <strong>modules seront temporairement désactivés</strong></li>';
    print '<li>🔸 Un <strong>redémarrage complet</strong> sera nécessaire</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>✅ Utilisez cette solution si :</h3>';
    print '<ul>';
    print '<li>✅ Les autres solutions n\'ont <strong>pas fonctionné</strong></li>';
    print '<li>✅ Vous voulez une <strong>solution définitive</strong></li>';
    print '<li>✅ Vous acceptez de <strong>perdre les personnalisations</strong></li>';
    print '<li>✅ Vous pouvez <strong>redémarrer le serveur</strong></li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 30px 0;">';
    print '<a href="?action=reset_complete" style="background: #dc3545; color: white; padding: 20px 40px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold;">💥 EXÉCUTER LE RESET COMPLET</a>';
    print '</div>';
    
    print '<p style="text-align: center; color: #6c757d; font-style: italic;">Cette action est irréversible. Assurez-vous d\'avoir une sauvegarde si nécessaire.</p>';
}

print '</body></html>';
?>
