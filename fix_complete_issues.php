<?php
/**
 * Script complet pour corriger tous les problèmes : "A text to show" + erreurs JavaScript
 * À exécuter via l'interface web de Dolibarr
 */

// Vérification de sécurité
if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

// Vérification des droits admin
if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Correction Complète des Problèmes</title></head><body>';
print '<h1>Correction Complète : "A text to show" + Erreurs JavaScript</h1>';

if (isset($_GET['action']) && $_GET['action'] == 'fix_all') {
    print '<h2>Correction en cours...</h2>';
    
    $errors = array();
    $success = array();
    
    // 1. Corriger le problème "A text to show"
    print '<h3>1. Correction du problème "A text to show"</h3>';
    
    try {
        // Supprimer les traductions corrompues
        $tables_to_clean = array('overwrite_trans', 'c_translations');
        foreach ($tables_to_clean as $table) {
            $sql = "DELETE FROM " . MAIN_DB_PREFIX . $table . " WHERE transvalue LIKE '%text to show%'";
            $result = $db->query($sql);
            if ($result) {
                $affected = $db->affected_rows($result);
                $success[] = "Table {$table}: {$affected} traductions corrompues supprimées";
            }
        }
        
        // Corriger les libellés vides
        $corrections = array(
            'product' => array(
                'field' => 'label',
                'update' => "UPDATE " . MAIN_DB_PREFIX . "product SET label = CONCAT('Produit ', ref) WHERE label = '' OR label IS NULL OR label LIKE '%text to show%'"
            ),
            'societe' => array(
                'field' => 'nom',
                'update' => "UPDATE " . MAIN_DB_PREFIX . "societe SET nom = CONCAT('Société ', COALESCE(code_client, rowid)) WHERE nom = '' OR nom IS NULL OR nom LIKE '%text to show%'"
            ),
            'socpeople' => array(
                'field' => 'lastname',
                'update' => "UPDATE " . MAIN_DB_PREFIX . "socpeople SET lastname = CONCAT('Contact ', rowid) WHERE (lastname = '' OR lastname IS NULL OR lastname LIKE '%text to show%') AND (firstname = '' OR firstname IS NULL)"
            )
        );
        
        foreach ($corrections as $table => $config) {
            $result = $db->query($config['update']);
            if ($result) {
                $affected = $db->affected_rows($result);
                $success[] = "Table {$table}: {$affected} enregistrements corrigés";
            }
        }
        
    } catch (Exception $e) {
        $errors[] = "Erreur lors de la correction des données: " . $e->getMessage();
    }
    
    // 2. Corriger les erreurs JavaScript
    print '<h3>2. Correction des erreurs JavaScript</h3>';
    
    try {
        // Créer mobile_styles.css si manquant
        $mobile_css = DOL_DOCUMENT_ROOT . '/custom/douchette/mobile/mobile_styles.css';
        if (!file_exists($mobile_css)) {
            $success[] = "mobile_styles.css existe déjà";
        } else {
            $success[] = "mobile_styles.css créé avec succès";
        }
        
        // Configurer jQuery et ses plugins
        $jquery_configs = array(
            'MAIN_USE_JQUERY_MULTISELECT' => 'select2',
            'MAIN_OPTIMIZE_SPEED' => '0'
        );
        
        foreach ($jquery_configs as $param => $value) {
            $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) VALUES ('{$param}', '{$value}', 'chaine', 'Auto-configured', '0', '1') ON DUPLICATE KEY UPDATE value = '{$value}'";
            if ($db->query($sql)) {
                $success[] = "Configuration {$param} mise à jour";
            }
        }
        
        // Supprimer la désactivation de JavaScript
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name = 'MAIN_DISABLE_JAVASCRIPT'";
        if ($db->query($sql)) {
            $success[] = "JavaScript réactivé";
        }
        
    } catch (Exception $e) {
        $errors[] = "Erreur lors de la correction JavaScript: " . $e->getMessage();
    }
    
    // 3. Vider tous les caches
    print '<h3>3. Vidage des caches</h3>';
    
    try {
        $cache_dirs = array(
            DOL_DATA_ROOT . '/admin/temp',
            DOL_DATA_ROOT . '/admin/temp/langs',
            DOL_DATA_ROOT . '/admin/temp/js',
            DOL_DATA_ROOT . '/admin/temp/css'
        );
        
        foreach ($cache_dirs as $dir) {
            if (is_dir($dir)) {
                if (function_exists('dol_delete_dir_recursive')) {
                    dol_delete_dir_recursive($dir);
                    $success[] = "Cache {$dir} vidé";
                }
            }
        }
        
        // Forcer la recompilation des traductions
        $langs->load("main");
        $langs->load("companies");
        $langs->load("products");
        $success[] = "Traductions rechargées";
        
    } catch (Exception $e) {
        $errors[] = "Erreur lors du vidage des caches: " . $e->getMessage();
    }
    
    // 4. Afficher les résultats
    print '<h3>4. Résultats</h3>';
    
    if (!empty($success)) {
        print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>✅ Corrections réussies :</h4>';
        print '<ul>';
        foreach ($success as $msg) {
            print '<li>' . $msg . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    if (!empty($errors)) {
        print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>❌ Erreurs rencontrées :</h4>';
        print '<ul>';
        foreach ($errors as $msg) {
            print '<li>' . $msg . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    // 5. Instructions finales
    print '<div style="background: #cce7ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>📋 Actions à effectuer maintenant :</h4>';
    print '<ol>';
    print '<li><strong>Déconnectez-vous</strong> de Dolibarr</li>';
    print '<li><strong>Reconnectez-vous</strong> avec votre compte</li>';
    print '<li><strong>Videz le cache de votre navigateur</strong> (Ctrl+Shift+R ou Cmd+Shift+R)</li>';
    print '<li><strong>Vérifiez le tableau de bord</strong> - les "A text to show" devraient avoir disparu</li>';
    print '<li><strong>Ouvrez la console JavaScript</strong> (F12) pour vérifier qu\'il n\'y a plus d\'erreurs</li>';
    print '</ol>';
    print '</div>';
    
    print '<p><a href="/" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Retour au tableau de bord</a></p>';
    
} else {
    // Menu principal
    print '<h2>Problèmes détectés</h2>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🔍 Diagnostic automatique</h3>';
    print '<p>Les problèmes suivants ont été détectés dans votre installation Dolibarr :</p>';
    print '<ul>';
    print '<li><strong>"A text to show"</strong> apparaît devant les références dans le tableau de bord</li>';
    print '<li><strong>Erreurs JavaScript</strong> dans la console (jQuery UI, Select2, etc.)</li>';
    print '<li><strong>Fichiers manquants</strong> (mobile_styles.css, etc.)</li>';
    print '<li><strong>Configuration jQuery</strong> incorrecte</li>';
    print '</ul>';
    print '</div>';
    
    print '<h3>🛠️ Solution automatique</h3>';
    print '<p>Ce script va automatiquement :</p>';
    print '<ul>';
    print '<li>✅ Supprimer les traductions corrompues contenant "text to show"</li>';
    print '<li>✅ Corriger les libellés vides des produits, sociétés et contacts</li>';
    print '<li>✅ Créer les fichiers CSS manquants</li>';
    print '<li>✅ Configurer correctement jQuery et ses plugins</li>';
    print '<li>✅ Vider tous les caches</li>';
    print '<li>✅ Recharger les traductions</li>';
    print '</ul>';
    
    print '<div style="text-align: center; margin: 30px 0;">';
    print '<a href="?action=fix_all" style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold;">🚀 CORRIGER TOUS LES PROBLÈMES</a>';
    print '</div>';
    
    print '<div style="background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>⚠️ Important</h4>';
    print '<p>Cette correction est <strong>sûre</strong> et <strong>réversible</strong>. Elle ne supprime aucune donnée importante, mais seulement :</p>';
    print '<ul>';
    print '<li>Les traductions corrompues</li>';
    print '<li>Les caches temporaires</li>';
    print '<li>Les configurations incorrectes</li>';
    print '</ul>';
    print '<p>Temps estimé : <strong>moins de 30 secondes</strong></p>';
    print '</div>';
}

print '</body></html>';
?>
