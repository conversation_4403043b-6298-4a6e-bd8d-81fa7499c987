<?php
// Script pour corriger le problème "A text to show"
require_once 'main.inc.php';

echo "<h1>Correction du problème 'A text to show'</h1>";

// 1. Supprimer les traductions corrompues
echo "<h2>1. Nettoyage des traductions corrompues</h2>";

$tables_to_clean = array('overwrite_trans', 'c_translations');
foreach ($tables_to_clean as $table) {
    $sql = "DELETE FROM " . MAIN_DB_PREFIX . $table . " WHERE transvalue LIKE '%text to show%'";
    $result = $db->query($sql);
    if ($result) {
        $affected = $db->affected_rows($result);
        echo "Table {$table}: {$affected} entrées supprimées<br>";
    } else {
        echo "Table {$table}: erreur ou n'existe pas - " . $db->lasterror() . "<br>";
    }
}

// 2. Vérifier et corriger les libellés vides
echo "<h2>2. Correction des libellés vides</h2>";

// Produits avec libellés vides
$sql = "SELECT rowid, ref FROM " . MAIN_DB_PREFIX . "product WHERE label = '' OR label IS NULL OR label = 'A text to show'";
$result = $db->query($sql);
if ($result) {
    $count = 0;
    while ($obj = $db->fetch_object($result)) {
        $new_label = "Produit " . $obj->ref;
        $sql_update = "UPDATE " . MAIN_DB_PREFIX . "product SET label = '" . $db->escape($new_label) . "' WHERE rowid = " . $obj->rowid;
        if ($db->query($sql_update)) {
            $count++;
        }
    }
    echo "Produits corrigés: {$count}<br>";
}

// Sociétés avec noms vides
$sql = "SELECT rowid, code_client FROM " . MAIN_DB_PREFIX . "societe WHERE nom = '' OR nom IS NULL OR nom = 'A text to show'";
$result = $db->query($sql);
if ($result) {
    $count = 0;
    while ($obj = $db->fetch_object($result)) {
        $new_name = "Société " . ($obj->code_client ? $obj->code_client : $obj->rowid);
        $sql_update = "UPDATE " . MAIN_DB_PREFIX . "societe SET nom = '" . $db->escape($new_name) . "' WHERE rowid = " . $obj->rowid;
        if ($db->query($sql_update)) {
            $count++;
        }
    }
    echo "Sociétés corrigées: {$count}<br>";
}

// 3. Reconstruire le cache des traductions
echo "<h2>3. Reconstruction du cache</h2>";

// Supprimer les fichiers de cache
$cache_dirs = array(
    DOL_DATA_ROOT . '/admin/temp',
    DOL_DATA_ROOT . '/admin/temp/langs'
);

foreach ($cache_dirs as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*');
        $deleted = 0;
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
                $deleted++;
            }
        }
        echo "Cache {$dir}: {$deleted} fichiers supprimés<br>";
    }
}

// 4. Forcer la recompilation des traductions
echo "<h2>4. Recompilation des traductions</h2>";

// Recharger les traductions
$langs->load("main");
$langs->load("companies");
$langs->load("products");
$langs->load("orders");

echo "Traductions rechargées<br>";

// 5. Test final
echo "<h2>5. Test final</h2>";
echo "Test langs->trans('Product'): " . $langs->trans('Product') . "<br>";
echo "Test langs->trans('ThirdParty'): " . $langs->trans('ThirdParty') . "<br>";
echo "Test langs->trans('Reference'): " . $langs->trans('Reference') . "<br>";

// Vérifier un produit
$sql = "SELECT rowid, ref, label FROM " . MAIN_DB_PREFIX . "product ORDER BY rowid LIMIT 1";
$result = $db->query($sql);
if ($result && $obj = $db->fetch_object($result)) {
    echo "Premier produit - ID: {$obj->rowid}, Ref: {$obj->ref}, Label: {$obj->label}<br>";
}

// Vérifier une société
$sql = "SELECT rowid, nom FROM " . MAIN_DB_PREFIX . "societe ORDER BY rowid LIMIT 1";
$result = $db->query($sql);
if ($result && $obj = $db->fetch_object($result)) {
    echo "Première société - ID: {$obj->rowid}, Nom: {$obj->nom}<br>";
}

echo "<h2>Correction terminée</h2>";
echo "<p><strong>Actions recommandées :</strong></p>";
echo "<ul>";
echo "<li>Vider le cache Dolibarr via Admin > Outils > Purger les caches</li>";
echo "<li>Se déconnecter et se reconnecter</li>";
echo "<li>Vérifier que les pages s'affichent correctement</li>";
echo "</ul>";
?>
