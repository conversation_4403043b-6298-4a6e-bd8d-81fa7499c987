<?php
/**
 * <PERSON>ript avancé pour corriger définitivement le problème "A text to show"
 * À exécuter via l'interface web de Dolibarr
 */

// Vérification de sécurité
if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

// Vérification des droits admin
if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Correction Avancée A text to show</title></head><body>';
print '<h1>Correction Avancée du problème "A text to show"</h1>';

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'diagnostic':
            print '<h2>Diagnostic Complet</h2>';
            
            // 1. Vérifier la configuration de langue
            print '<h3>Configuration de langue</h3>';
            print 'Langue par défaut: ' . $langs->getDefaultLang() . '<br>';
            print 'Langue courante: ' . $langs->defaultlang . '<br>';
            print 'MAIN_LANG_DEFAULT: ' . getDolGlobalString('MAIN_LANG_DEFAULT') . '<br>';
            
            // 2. Vérifier les fichiers de langue
            $langfile = DOL_DOCUMENT_ROOT . "/langs/" . $langs->defaultlang . "/main.lang";
            print 'Fichier de langue principal: ' . ($langfile) . ' - ' . (file_exists($langfile) ? 'EXISTE' : 'MANQUANT') . '<br>';
            
            // 3. Compter les problèmes par table
            print '<h3>Problèmes par table</h3>';
            $tables_to_check = array(
                'product' => array('field' => 'label', 'name_field' => 'ref'),
                'societe' => array('field' => 'nom', 'name_field' => 'nom'),
                'socpeople' => array('field' => 'lastname', 'name_field' => 'lastname'),
                'commande' => array('field' => 'ref', 'name_field' => 'ref'),
                'facture' => array('field' => 'ref', 'name_field' => 'ref'),
                'commande_fournisseur' => array('field' => 'ref', 'name_field' => 'ref'),
                'facture_fourn' => array('field' => 'ref', 'name_field' => 'ref')
            );
            
            foreach ($tables_to_check as $table => $config) {
                $sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . $table . " WHERE " . $config['field'] . " LIKE '%text to show%'";
                $result = $db->query($sql);
                if ($result) {
                    $obj = $db->fetch_object($result);
                    if ($obj->nb > 0) {
                        print "<span style='color: red;'>Table {$table}: {$obj->nb} entrées problématiques</span><br>";
                    } else {
                        print "<span style='color: green;'>Table {$table}: OK</span><br>";
                    }
                }
            }
            
            // 4. Vérifier les traductions personnalisées
            print '<h3>Traductions personnalisées</h3>';
            $trans_tables = array('overwrite_trans', 'c_translations');
            foreach ($trans_tables as $table) {
                $sql = "SELECT COUNT(*) as nb FROM " . MAIN_DB_PREFIX . $table . " WHERE transvalue LIKE '%text to show%'";
                $result = $db->query($sql);
                if ($result) {
                    $obj = $db->fetch_object($result);
                    if ($obj->nb > 0) {
                        print "<span style='color: red;'>Table {$table}: {$obj->nb} traductions corrompues</span><br>";
                    } else {
                        print "<span style='color: green;'>Table {$table}: OK</span><br>";
                    }
                } else {
                    print "Table {$table}: n'existe pas<br>";
                }
            }
            break;
            
        case 'fix_all':
            print '<h2>Correction Complète</h2>';
            
            // 1. Nettoyer les traductions corrompues
            print '<h3>1. Nettoyage des traductions</h3>';
            $trans_tables = array('overwrite_trans', 'c_translations');
            foreach ($trans_tables as $table) {
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . $table . " WHERE transvalue LIKE '%text to show%'";
                $result = $db->query($sql);
                if ($result) {
                    $affected = $db->affected_rows($result);
                    print "Table {$table}: {$affected} entrées supprimées<br>";
                }
            }
            
            // 2. Corriger les données principales
            print '<h3>2. Correction des données</h3>';
            
            // Produits
            $sql = "UPDATE " . MAIN_DB_PREFIX . "product SET label = CONCAT('Produit ', ref) WHERE label = '' OR label IS NULL OR label LIKE '%text to show%'";
            $result = $db->query($sql);
            if ($result) {
                $affected = $db->affected_rows($result);
                print "Produits: {$affected} libellés corrigés<br>";
            }
            
            // Sociétés
            $sql = "UPDATE " . MAIN_DB_PREFIX . "societe SET nom = CONCAT('Société ', COALESCE(code_client, rowid)) WHERE nom = '' OR nom IS NULL OR nom LIKE '%text to show%'";
            $result = $db->query($sql);
            if ($result) {
                $affected = $db->affected_rows($result);
                print "Sociétés: {$affected} noms corrigés<br>";
            }
            
            // Contacts
            $sql = "UPDATE " . MAIN_DB_PREFIX . "socpeople SET lastname = CONCAT('Contact ', rowid) WHERE (lastname = '' OR lastname IS NULL OR lastname LIKE '%text to show%') AND (firstname = '' OR firstname IS NULL)";
            $result = $db->query($sql);
            if ($result) {
                $affected = $db->affected_rows($result);
                print "Contacts: {$affected} noms corrigés<br>";
            }
            
            // 3. Vider tous les caches
            print '<h3>3. Vidage des caches</h3>';
            $cache_dirs = array(
                DOL_DATA_ROOT . '/admin/temp',
                DOL_DATA_ROOT . '/admin/temp/langs'
            );
            
            foreach ($cache_dirs as $dir) {
                if (is_dir($dir)) {
                    if (function_exists('dol_delete_dir_recursive')) {
                        dol_delete_dir_recursive($dir);
                        print "Cache {$dir}: vidé<br>";
                    }
                }
            }
            
            // 4. Forcer la recompilation des traductions
            print '<h3>4. Recompilation des traductions</h3>';
            $langs->load("main");
            $langs->load("companies");
            $langs->load("products");
            print "Traductions rechargées<br>";
            
            print '<p><strong style="color: green;">Correction complète terminée !</strong></p>';
            print '<p><strong>Actions recommandées :</strong></p>';
            print '<ul>';
            print '<li>Déconnectez-vous et reconnectez-vous</li>';
            print '<li>Videz le cache de votre navigateur (Ctrl+F5)</li>';
            print '<li>Vérifiez que le problème est résolu sur le tableau de bord</li>';
            print '</ul>';
            break;
    }
} else {
    // Menu principal
    print '<h2>Choisissez une action</h2>';
    print '<p><a href="?action=diagnostic" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Diagnostic Complet</a></p>';
    print '<p><a href="?action=fix_all" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Correction Complète</a></p>';
    
    print '<h3>Description des actions</h3>';
    print '<ul>';
    print '<li><strong>Diagnostic Complet :</strong> Analyse détaillée du problème sans modification</li>';
    print '<li><strong>Correction Complète :</strong> Corrige automatiquement tous les problèmes détectés</li>';
    print '</ul>';
}

print '</body></html>';
?>
