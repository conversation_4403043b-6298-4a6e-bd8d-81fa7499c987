<?php
// Script pour créer les extrafields manquants pour le rapport de délivrabilité
require_once 'main.inc.php';
dol_include_once('/core/class/extrafields.class.php');

$extrafields = new ExtraFields($db);

echo "=== Création des extrafields manquants ===\n";

// 1. Créer l'extrafield date_de_livraison pour propaldet si il n'existe pas
echo "Vérification de l'extrafield date_de_livraison pour propaldet...\n";
$extrafields->fetch_name_optionals_label('propaldet');
if (empty($extrafields->attributes['propaldet']['label']['date_de_livraison'])) {
    echo "Création de l'extrafield date_de_livraison pour propaldet...\n";
    $result = $extrafields->addExtraField(
        'date_de_livraison',
        'Date de livraison',
        'date',
        100,
        '',
        'propaldet',
        0,
        0,
        '',
        '',
        0,
        '',
        1,
        'Date de livraison prévue pour cette ligne'
    );
    if ($result > 0) {
        echo "✓ Extrafield date_de_livraison créé pour propaldet\n";
    } else {
        echo "✗ Erreur lors de la création de l'extrafield pour propaldet: " . $extrafields->error . "\n";
    }
} else {
    echo "✓ Extrafield date_de_livraison existe déjà pour propaldet\n";
}

// 2. Créer l'extrafield date_de_livraison pour commande_fournisseurdet si il n'existe pas
echo "\nVérification de l'extrafield date_de_livraison pour commande_fournisseurdet...\n";
$extrafields->fetch_name_optionals_label('commande_fournisseurdet');
if (empty($extrafields->attributes['commande_fournisseurdet']['label']['date_de_livraison'])) {
    echo "Création de l'extrafield date_de_livraison pour commande_fournisseurdet...\n";
    $result = $extrafields->addExtraField(
        'date_de_livraison',
        'Date de livraison',
        'date',
        100,
        '',
        'commande_fournisseurdet',
        0,
        0,
        '',
        '',
        0,
        '',
        1,
        'Date de livraison prévue pour cette ligne'
    );
    if ($result > 0) {
        echo "✓ Extrafield date_de_livraison créé pour commande_fournisseurdet\n";
    } else {
        echo "✗ Erreur lors de la création de l'extrafield pour commande_fournisseurdet: " . $extrafields->error . "\n";
    }
} else {
    echo "✓ Extrafield date_de_livraison existe déjà pour commande_fournisseurdet\n";
}

// 3. Mettre à jour les configurations
echo "\n=== Mise à jour des configurations ===\n";

// Configuration pour propaldet
$result = dolibarr_set_const($db, 'OF_DELIVERABILITY_REPORT_PROPAL_DATE_EXTRAFIELD', 'date_de_livraison', 'chaine', 0, '', $conf->entity);
if ($result > 0) {
    echo "✓ Configuration OF_DELIVERABILITY_REPORT_PROPAL_DATE_EXTRAFIELD mise à jour\n";
} else {
    echo "✗ Erreur lors de la mise à jour de la configuration propal\n";
}

// Configuration pour commande_fournisseurdet
$result = dolibarr_set_const($db, 'OF_DELIVERABILITY_REPORT_SUPPLIERORDER_DATE_EXTRAFIELD', 'date_de_livraison', 'chaine', 0, '', $conf->entity);
if ($result > 0) {
    echo "✓ Configuration OF_DELIVERABILITY_REPORT_SUPPLIERORDER_DATE_EXTRAFIELD mise à jour\n";
} else {
    echo "✗ Erreur lors de la mise à jour de la configuration fournisseur\n";
}

echo "\n=== Vérification finale ===\n";
echo "OF_DELIVERABILITY_REPORT_ORDER_DATE_EXTRAFIELD: " . getDolGlobalString('OF_DELIVERABILITY_REPORT_ORDER_DATE_EXTRAFIELD') . "\n";
echo "OF_DELIVERABILITY_REPORT_PROPAL_DATE_EXTRAFIELD: " . getDolGlobalString('OF_DELIVERABILITY_REPORT_PROPAL_DATE_EXTRAFIELD') . "\n";
echo "OF_DELIVERABILITY_REPORT_SUPPLIERORDER_DATE_EXTRAFIELD: " . getDolGlobalString('OF_DELIVERABILITY_REPORT_SUPPLIERORDER_DATE_EXTRAFIELD') . "\n";

echo "\nScript terminé.\n";
?>
