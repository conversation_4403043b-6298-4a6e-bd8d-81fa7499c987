<?php
/**
 * Diagnostic approfondi du problème "A text to show"
 * Recherche la source exacte du problème
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Diagnostic Approfondi - A text to show</title></head><body>';
print '<h1>Diagnostic Approfondi du Problème "A text to show"</h1>';

if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    switch ($action) {
        case 'deep_analysis':
            print '<h2>Analyse Approfondie</h2>';
            
            // 1. Vérifier la configuration de langue
            print '<h3>1. Configuration de langue</h3>';
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Paramètre</th><th>Valeur</th></tr>';
            
            $lang_config = array(
                'MAIN_LANG_DEFAULT' => getDolGlobalString('MAIN_LANG_DEFAULT'),
                'User Language' => $user->lang,
                'System Language' => $langs->defaultlang,
                'MAIN_MULTILANGS' => getDolGlobalString('MAIN_MULTILANGS'),
                'MAIN_ENABLE_OVERWRITE_TRANSLATION' => getDolGlobalString('MAIN_ENABLE_OVERWRITE_TRANSLATION')
            );
            
            foreach ($lang_config as $param => $value) {
                print '<tr><td>' . $param . '</td><td>' . ($value ? $value : 'Non défini') . '</td></tr>';
            }
            print '</table>';
            
            // 2. Tester des traductions spécifiques
            print '<h3>2. Test de traductions courantes</h3>';
            print '<table border="1" style="border-collapse: collapse; width: 100%;">';
            print '<tr><th>Clé</th><th>Traduction</th><th>Status</th></tr>';
            
            $test_keys = array(
                'Ref',
                'Label',
                'Name',
                'Company',
                'Product',
                'Service',
                'ThirdParty',
                'Contact',
                'Invoice',
                'Order'
            );
            
            foreach ($test_keys as $key) {
                $translation = $langs->trans($key);
                $status = ($translation == $key) ? '❌ Manquante' : '✅ OK';
                if (strpos($translation, 'text to show') !== false) {
                    $status = '🔴 PROBLÈME';
                }
                print '<tr><td>' . $key . '</td><td>' . $translation . '</td><td>' . $status . '</td></tr>';
            }
            print '</table>';
            
            // 3. Vérifier les fichiers de langue chargés
            print '<h3>3. Fichiers de langue chargés</h3>';
            if (isset($langs->_tab_loaded) && is_array($langs->_tab_loaded)) {
                print '<ul>';
                foreach ($langs->_tab_loaded as $domain => $loaded) {
                    print '<li>' . $domain . ': ' . ($loaded ? '✅ Chargé' : '❌ Non chargé') . '</li>';
                }
                print '</ul>';
            } else {
                print '<p>Impossible d\'accéder aux informations de chargement des langues.</p>';
            }
            
            // 4. Rechercher dans la base de données
            print '<h3>4. Recherche dans la base de données</h3>';
            
            // Vérifier les tables de traduction
            $translation_tables = array('overwrite_trans', 'c_translations');
            foreach ($translation_tables as $table) {
                $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . $table . " WHERE transvalue LIKE '%text to show%'";
                $result = $db->query($sql);
                if ($result) {
                    $obj = $db->fetch_object($result);
                    $count = $obj->count;
                    print "Table {$table}: {$count} entrées contenant 'text to show'<br>";
                    
                    if ($count > 0) {
                        // Afficher quelques exemples
                        $sql2 = "SELECT transkey, transvalue, lang FROM " . MAIN_DB_PREFIX . $table . " WHERE transvalue LIKE '%text to show%' LIMIT 5";
                        $result2 = $db->query($sql2);
                        if ($result2) {
                            print '<table border="1" style="border-collapse: collapse; margin: 10px 0;">';
                            print '<tr><th>Clé</th><th>Valeur</th><th>Langue</th></tr>';
                            while ($obj2 = $db->fetch_object($result2)) {
                                print '<tr>';
                                print '<td>' . htmlspecialchars($obj2->transkey) . '</td>';
                                print '<td>' . htmlspecialchars($obj2->transvalue) . '</td>';
                                print '<td>' . htmlspecialchars($obj2->lang) . '</td>';
                                print '</tr>';
                            }
                            print '</table>';
                        }
                    }
                }
            }
            
            // 5. Vérifier les données avec des libellés vides
            print '<h3>5. Données avec libellés vides ou problématiques</h3>';
            
            $data_checks = array(
                'product' => array('field' => 'label', 'name_field' => 'ref'),
                'societe' => array('field' => 'nom', 'name_field' => 'code_client'),
                'socpeople' => array('field' => 'lastname', 'name_field' => 'firstname')
            );
            
            foreach ($data_checks as $table => $config) {
                $sql = "SELECT COUNT(*) as count FROM " . MAIN_DB_PREFIX . $table . " WHERE " . $config['field'] . " = '' OR " . $config['field'] . " IS NULL OR " . $config['field'] . " LIKE '%text to show%'";
                $result = $db->query($sql);
                if ($result) {
                    $obj = $db->fetch_object($result);
                    $count = $obj->count;
                    print "Table {$table}: {$count} enregistrements avec libellé vide/problématique<br>";
                    
                    if ($count > 0 && $count < 20) {
                        // Afficher quelques exemples
                        $sql2 = "SELECT rowid, " . $config['field'] . ", " . $config['name_field'] . " FROM " . MAIN_DB_PREFIX . $table . " WHERE " . $config['field'] . " = '' OR " . $config['field'] . " IS NULL OR " . $config['field'] . " LIKE '%text to show%' LIMIT 10";
                        $result2 = $db->query($sql2);
                        if ($result2) {
                            print '<table border="1" style="border-collapse: collapse; margin: 10px 0;">';
                            print '<tr><th>ID</th><th>' . ucfirst($config['field']) . '</th><th>' . ucfirst($config['name_field']) . '</th></tr>';
                            while ($obj2 = $db->fetch_object($result2)) {
                                print '<tr>';
                                print '<td>' . $obj2->rowid . '</td>';
                                print '<td>' . htmlspecialchars($obj2->{$config['field']}) . '</td>';
                                print '<td>' . htmlspecialchars($obj2->{$config['name_field']}) . '</td>';
                                print '</tr>';
                            }
                            print '</table>';
                        }
                    }
                }
            }
            
            // 6. Test de la fonction de traduction
            print '<h3>6. Test de la fonction de traduction</h3>';
            
            // Créer une clé de test inexistante
            $test_key = 'NONEXISTENT_KEY_TEST_' . time();
            $test_result = $langs->trans($test_key);
            
            print "Clé de test: {$test_key}<br>";
            print "Résultat: {$test_result}<br>";
            
            if ($test_result == $test_key) {
                print "✅ Comportement normal: clé inexistante retournée telle quelle<br>";
            } elseif (strpos($test_result, 'text to show') !== false) {
                print "🔴 PROBLÈME: la fonction de traduction génère 'text to show'<br>";
            } else {
                print "⚠️ Comportement inattendu: {$test_result}<br>";
            }
            
            break;
            
        case 'fix_advanced':
            print '<h2>Correction Avancée</h2>';
            
            $fixes_applied = array();
            $errors = array();
            
            try {
                // 1. Réinitialiser complètement les traductions
                print '<h3>1. Réinitialisation des traductions</h3>';
                
                // Supprimer toutes les traductions corrompues
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "overwrite_trans WHERE transvalue LIKE '%text to show%' OR transvalue = '' OR transvalue IS NULL";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes_applied[] = "overwrite_trans: {$affected} entrées supprimées";
                }
                
                $sql = "DELETE FROM " . MAIN_DB_PREFIX . "c_translations WHERE transvalue LIKE '%text to show%' OR transvalue = '' OR transvalue IS NULL";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes_applied[] = "c_translations: {$affected} entrées supprimées";
                }
                
                // 2. Forcer la langue par défaut
                print '<h3>2. Configuration de la langue</h3>';
                
                $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) VALUES ('MAIN_LANG_DEFAULT', 'fr_FR', 'chaine', 'Default language', '0', '1') ON DUPLICATE KEY UPDATE value = 'fr_FR'";
                if ($db->query($sql)) {
                    $fixes_applied[] = "Langue par défaut définie sur fr_FR";
                }
                
                // Désactiver les traductions personnalisées temporairement
                $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '0' WHERE name = 'MAIN_ENABLE_OVERWRITE_TRANSLATION'";
                if ($db->query($sql)) {
                    $fixes_applied[] = "Traductions personnalisées désactivées temporairement";
                }
                
                // 3. Corriger les données avec des valeurs par défaut intelligentes
                print '<h3>3. Correction des données</h3>';
                
                // Produits
                $sql = "UPDATE " . MAIN_DB_PREFIX . "product SET label = CASE 
                    WHEN ref IS NOT NULL AND ref != '' THEN CONCAT('Produit ', ref)
                    WHEN description IS NOT NULL AND description != '' THEN LEFT(description, 50)
                    ELSE CONCAT('Produit #', rowid)
                END 
                WHERE label = '' OR label IS NULL OR label LIKE '%text to show%'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes_applied[] = "Produits: {$affected} libellés corrigés";
                }
                
                // Sociétés
                $sql = "UPDATE " . MAIN_DB_PREFIX . "societe SET nom = CASE 
                    WHEN code_client IS NOT NULL AND code_client != '' THEN CONCAT('Société ', code_client)
                    WHEN email IS NOT NULL AND email != '' THEN CONCAT('Société ', LEFT(email, 20))
                    ELSE CONCAT('Société #', rowid)
                END 
                WHERE nom = '' OR nom IS NULL OR nom LIKE '%text to show%'";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes_applied[] = "Sociétés: {$affected} noms corrigés";
                }
                
                // Contacts
                $sql = "UPDATE " . MAIN_DB_PREFIX . "socpeople SET lastname = CASE 
                    WHEN firstname IS NOT NULL AND firstname != '' THEN CONCAT(firstname, ' (Contact)')
                    WHEN email IS NOT NULL AND email != '' THEN CONCAT('Contact ', LEFT(email, 15))
                    ELSE CONCAT('Contact #', rowid)
                END 
                WHERE (lastname = '' OR lastname IS NULL OR lastname LIKE '%text to show%') AND (firstname = '' OR firstname IS NULL)";
                if ($db->query($sql)) {
                    $affected = $db->affected_rows();
                    $fixes_applied[] = "Contacts: {$affected} noms corrigés";
                }
                
                // 4. Vider tous les caches et forcer le rechargement
                print '<h3>4. Vidage des caches</h3>';
                
                $cache_dirs = array(
                    DOL_DATA_ROOT . '/admin/temp',
                    DOL_DATA_ROOT . '/admin/temp/langs',
                    DOL_DATA_ROOT . '/admin/temp/js',
                    DOL_DATA_ROOT . '/admin/temp/css'
                );
                
                foreach ($cache_dirs as $dir) {
                    if (is_dir($dir)) {
                        if (function_exists('dol_delete_dir_recursive')) {
                            dol_delete_dir_recursive($dir);
                            $fixes_applied[] = "Cache {$dir} vidé";
                        }
                    }
                }
                
                // 5. Forcer le rechargement des traductions
                print '<h3>5. Rechargement des traductions</h3>';
                
                // Réinitialiser l'objet langs
                $langs->tab_translate = array();
                $langs->_tab_loaded = array();
                
                // Recharger les domaines principaux
                $domains = array('main', 'companies', 'products', 'bills', 'orders', 'contracts');
                foreach ($domains as $domain) {
                    $langs->load($domain);
                    $fixes_applied[] = "Domaine {$domain} rechargé";
                }
                
            } catch (Exception $e) {
                $errors[] = "Erreur lors de la correction: " . $e->getMessage();
            }
            
            // Afficher les résultats
            if (!empty($fixes_applied)) {
                print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>✅ Corrections appliquées :</h4>';
                print '<ul>';
                foreach ($fixes_applied as $fix) {
                    print '<li>' . $fix . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            if (!empty($errors)) {
                print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
                print '<h4>❌ Erreurs :</h4>';
                print '<ul>';
                foreach ($errors as $error) {
                    print '<li>' . $error . '</li>';
                }
                print '</ul>';
                print '</div>';
            }
            
            print '<div style="background: #cce7ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 20px 0;">';
            print '<h4>📋 Actions finales :</h4>';
            print '<ol>';
            print '<li><strong>Fermez votre navigateur complètement</strong></li>';
            print '<li><strong>Redémarrez votre serveur web</strong> (MAMP)</li>';
            print '<li><strong>Rouvrez Dolibarr</strong> et reconnectez-vous</li>';
            print '<li><strong>Vérifiez le tableau de bord</strong></li>';
            print '</ol>';
            print '</div>';
            
            break;
    }
} else {
    // Menu principal
    print '<h2>Diagnostic Approfondi</h2>';
    print '<p>Ce script effectue une analyse complète pour identifier la source exacte du problème "A text to show".</p>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🔍 Que fait ce diagnostic ?</h3>';
    print '<ul>';
    print '<li>✅ Analyse la configuration des langues</li>';
    print '<li>✅ Teste les traductions courantes</li>';
    print '<li>✅ Vérifie les fichiers de langue chargés</li>';
    print '<li>✅ Recherche dans la base de données</li>';
    print '<li>✅ Identifie les données problématiques</li>';
    print '<li>✅ Teste la fonction de traduction</li>';
    print '</ul>';
    print '</div>';
    
    print '<p><a href="?action=deep_analysis" style="background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px; margin-right: 10px;">🔍 LANCER LE DIAGNOSTIC</a></p>';
    
    print '<p><a href="?action=fix_advanced" style="background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 16px;">🛠️ CORRECTION AVANCÉE</a></p>';
    
    print '<div style="background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>⚠️ Important</h4>';
    print '<p>La correction avancée va :</p>';
    print '<ul>';
    print '<li>Supprimer toutes les traductions corrompues</li>';
    print '<li>Réinitialiser la configuration des langues</li>';
    print '<li>Corriger intelligemment les données vides</li>';
    print '<li>Vider tous les caches</li>';
    print '<li>Forcer le rechargement des traductions</li>';
    print '</ul>';
    print '</div>';
}

print '</body></html>';
?>
