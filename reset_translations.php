<?php
// Script pour réinitialiser complètement les traductions
require_once 'main.inc.php';

echo "<h1>Réinitialisation complète des traductions</h1>";

// 1. Supprimer toutes les traductions personnalisées
echo "<h2>1. Suppression des traductions personnalisées</h2>";
$sql = "DELETE FROM " . MAIN_DB_PREFIX . "overwrite_trans";
$result = $db->query($sql);
if ($result) {
    echo "Toutes les traductions personnalisées supprimées<br>";
} else {
    echo "Erreur ou table n'existe pas: " . $db->lasterror() . "<br>";
}

// 2. Réinitialiser la configuration de langue
echo "<h2>2. Configuration de langue</h2>";
$result = dolibarr_set_const($db, 'MAIN_LANG_DEFAULT', 'fr_FR', 'chaine', 0, '', $conf->entity);
if ($result > 0) {
    echo "Langue par défaut définie sur fr_FR<br>";
} else {
    echo "Erreur lors de la définition de la langue<br>";
}

// 3. Désactiver les traductions personnalisées
$result = dolibarr_set_const($db, 'MAIN_ENABLE_OVERWRITE_TRANSLATION', '0', 'chaine', 0, '', $conf->entity);
if ($result > 0) {
    echo "Traductions personnalisées désactivées<br>";
}

// 4. Nettoyer tous les caches possibles
echo "<h2>3. Nettoyage complet des caches</h2>";
$cache_patterns = array(
    DOL_DATA_ROOT . '/admin/temp/*',
    DOL_DATA_ROOT . '/admin/temp/langs/*',
    DOL_DATA_ROOT . '/' . $conf->entity . '/temp/*'
);

foreach ($cache_patterns as $pattern) {
    $files = glob($pattern);
    if ($files) {
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        echo "Cache nettoyé: " . dirname($pattern) . "<br>";
    }
}

// 5. Forcer la reconstruction des objets de base
echo "<h2>4. Test des objets de base</h2>";

// Créer un nouvel objet langs propre
$new_langs = new Translate('', $conf);
$new_langs->setDefaultLang('fr_FR');
$new_langs->load("main");

echo "Test avec nouvel objet Translate:<br>";
echo "- Product: " . $new_langs->trans('Product') . "<br>";
echo "- ThirdParty: " . $new_langs->trans('ThirdParty') . "<br>";
echo "- Reference: " . $new_langs->trans('Reference') . "<br>";

// 6. Vérifier les fichiers de langue
echo "<h2>5. Vérification des fichiers de langue</h2>";
$lang_files = array(
    DOL_DOCUMENT_ROOT . '/langs/fr_FR/main.lang',
    DOL_DOCUMENT_ROOT . '/langs/fr_FR/companies.lang',
    DOL_DOCUMENT_ROOT . '/langs/fr_FR/products.lang'
);

foreach ($lang_files as $file) {
    if (file_exists($file)) {
        echo "✓ {$file} existe (" . filesize($file) . " octets)<br>";
    } else {
        echo "✗ {$file} manquant<br>";
    }
}

// 7. Test de lecture directe d'un fichier de langue
echo "<h2>6. Test de lecture directe</h2>";
$main_lang_file = DOL_DOCUMENT_ROOT . '/langs/fr_FR/main.lang';
if (file_exists($main_lang_file)) {
    $content = file_get_contents($main_lang_file);
    if (strpos($content, 'Product=') !== false) {
        preg_match('/Product=(.*)/', $content, $matches);
        echo "Traduction directe de 'Product': " . (isset($matches[1]) ? trim($matches[1]) : 'Non trouvé') . "<br>";
    }
}

// 8. Réinitialiser les sessions
echo "<h2>7. Nettoyage des sessions</h2>";
if (session_status() == PHP_SESSION_ACTIVE) {
    session_destroy();
    echo "Session détruite<br>";
}

echo "<h2>Réinitialisation terminée</h2>";
echo "<p><strong>Actions à effectuer maintenant :</strong></p>";
echo "<ol>";
echo "<li>Se déconnecter complètement de Dolibarr</li>";
echo "<li>Fermer le navigateur</li>";
echo "<li>Rouvrir le navigateur et se reconnecter</li>";
echo "<li>Aller dans Admin > Outils > Purger les caches et tout vider</li>";
echo "<li>Tester les pages</li>";
echo "</ol>";
?>
