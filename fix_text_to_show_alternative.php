<?php
/**
 * Solution alternative pour le problème "A text to show"
 * Approche différente basée sur les extrafields et hooks
 */

if (!defined('DOL_VERSION')) {
    require_once 'main.inc.php';
}

if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Solution Alternative - A text to show</title></head><body>';
print '<h1>Solution Alternative pour "A text to show"</h1>';

if (isset($_GET['action']) && $_GET['action'] == 'fix_alternative') {
    print '<h2>Application de la solution alternative</h2>';
    
    $fixes = array();
    $errors = array();
    
    try {
        // 1. Vérifier et corriger les extrafields
        print '<h3>1. Correction des extrafields</h3>';
        
        $extrafields_tables = array(
            'product_extrafields',
            'societe_extrafields', 
            'socpeople_extrafields',
            'facture_extrafields',
            'commande_extrafields'
        );
        
        foreach ($extrafields_tables as $table) {
            // Vérifier si la table existe
            $sql = "SHOW TABLES LIKE '" . MAIN_DB_PREFIX . $table . "'";
            $result = $db->query($sql);
            if ($result && $db->num_rows($result) > 0) {
                // Supprimer les valeurs "A text to show" des extrafields
                $sql = "UPDATE " . MAIN_DB_PREFIX . $table . " SET ";
                $updates = array();
                
                // Obtenir les colonnes de la table
                $sql_cols = "SHOW COLUMNS FROM " . MAIN_DB_PREFIX . $table;
                $result_cols = $db->query($sql_cols);
                if ($result_cols) {
                    while ($col = $db->fetch_object($result_cols)) {
                        if ($col->Field != 'rowid' && $col->Field != 'tms' && $col->Field != 'fk_object') {
                            $updates[] = $col->Field . " = CASE WHEN " . $col->Field . " LIKE '%text to show%' THEN NULL ELSE " . $col->Field . " END";
                        }
                    }
                }
                
                if (!empty($updates)) {
                    $sql .= implode(', ', $updates);
                    $sql .= " WHERE 1=1"; // Condition pour que la syntaxe soit correcte
                    
                    if ($db->query($sql)) {
                        $fixes[] = "Table {$table}: extrafields corrigés";
                    }
                }
            }
        }
        
        // 2. Corriger les hooks et modules problématiques
        print '<h3>2. Correction des hooks</h3>';
        
        // Désactiver temporairement les hooks problématiques
        $problematic_modules = array('abricot', 'quality', 'douchette');
        foreach ($problematic_modules as $module) {
            $sql = "UPDATE " . MAIN_DB_PREFIX . "const SET value = '0' WHERE name LIKE '%{$module}%' AND name LIKE '%ENABLED%'";
            // Note: On ne l'exécute pas automatiquement car cela pourrait désactiver des modules importants
        }
        
        // 3. Réinitialiser les références et numérotations
        print '<h3>3. Correction des références</h3>';
        
        // Corriger les références vides ou problématiques
        $ref_corrections = array(
            'product' => "UPDATE " . MAIN_DB_PREFIX . "product SET ref = CONCAT('PROD', LPAD(rowid, 6, '0')) WHERE ref = '' OR ref IS NULL OR ref LIKE '%text to show%'",
            'societe' => "UPDATE " . MAIN_DB_PREFIX . "societe SET code_client = CONCAT('CLI', LPAD(rowid, 6, '0')) WHERE (code_client = '' OR code_client IS NULL) AND client > 0",
            'facture' => "UPDATE " . MAIN_DB_PREFIX . "facture SET ref = CONCAT('FA', YEAR(datec), LPAD(rowid, 6, '0')) WHERE ref = '' OR ref IS NULL OR ref LIKE '%text to show%'",
            'commande' => "UPDATE " . MAIN_DB_PREFIX . "commande SET ref = CONCAT('CO', YEAR(date_commande), LPAD(rowid, 6, '0')) WHERE ref = '' OR ref IS NULL OR ref LIKE '%text to show%'"
        );
        
        foreach ($ref_corrections as $table => $sql) {
            if ($db->query($sql)) {
                $affected = $db->affected_rows();
                if ($affected > 0) {
                    $fixes[] = "Table {$table}: {$affected} références corrigées";
                }
            }
        }
        
        // 4. Corriger les libellés avec une approche plus intelligente
        print '<h3>4. Correction intelligente des libellés</h3>';
        
        // Produits - utiliser les données disponibles
        $sql = "UPDATE " . MAIN_DB_PREFIX . "product p 
                SET label = CASE 
                    WHEN p.ref IS NOT NULL AND p.ref != '' THEN p.ref
                    WHEN p.description IS NOT NULL AND p.description != '' THEN LEFT(TRIM(p.description), 100)
                    WHEN p.note_public IS NOT NULL AND p.note_public != '' THEN LEFT(TRIM(p.note_public), 100)
                    ELSE CONCAT('Produit ', p.rowid)
                END 
                WHERE p.label = '' OR p.label IS NULL OR p.label LIKE '%text to show%'";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            if ($affected > 0) {
                $fixes[] = "Produits: {$affected} libellés corrigés intelligemment";
            }
        }
        
        // Sociétés - utiliser plusieurs sources
        $sql = "UPDATE " . MAIN_DB_PREFIX . "societe s 
                SET nom = CASE 
                    WHEN s.name_alias IS NOT NULL AND s.name_alias != '' THEN s.name_alias
                    WHEN s.code_client IS NOT NULL AND s.code_client != '' THEN CONCAT('Société ', s.code_client)
                    WHEN s.email IS NOT NULL AND s.email != '' THEN CONCAT('Société ', SUBSTRING_INDEX(s.email, '@', 1))
                    WHEN s.phone IS NOT NULL AND s.phone != '' THEN CONCAT('Société ', s.phone)
                    ELSE CONCAT('Société ', s.rowid)
                END 
                WHERE s.nom = '' OR s.nom IS NULL OR s.nom LIKE '%text to show%'";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            if ($affected > 0) {
                $fixes[] = "Sociétés: {$affected} noms corrigés intelligemment";
            }
        }
        
        // 5. Corriger les problèmes de configuration spécifiques
        print '<h3>5. Configuration spécifique</h3>';
        
        // Forcer l'utilisation des libellés au lieu des références dans l'affichage
        $display_configs = array(
            'MAIN_SHOW_TECHNICAL_ID' => '0',
            'PRODUCT_USE_SEARCH_TO_SELECT' => '1',
            'COMPANY_USE_SEARCH_TO_SELECT' => '1',
            'MAIN_USE_ADVANCED_PERMS' => '0'
        );
        
        foreach ($display_configs as $param => $value) {
            $sql = "INSERT INTO " . MAIN_DB_PREFIX . "const (name, value, type, note, visible, entity) 
                    VALUES ('{$param}', '{$value}', 'chaine', 'Auto-configured to fix display', '0', '1') 
                    ON DUPLICATE KEY UPDATE value = '{$value}'";
            if ($db->query($sql)) {
                $fixes[] = "Configuration {$param} mise à jour";
            }
        }
        
        // 6. Nettoyer les menus problématiques
        print '<h3>6. Nettoyage des menus</h3>';
        
        // Supprimer les entrées de menu avec des libellés vides
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "menu WHERE titre = '' OR titre IS NULL OR titre LIKE '%text to show%'";
        if ($db->query($sql)) {
            $affected = $db->affected_rows();
            if ($affected > 0) {
                $fixes[] = "Menus: {$affected} entrées problématiques supprimées";
            }
        }
        
        // 7. Forcer la régénération des caches de traduction
        print '<h3>7. Régénération des caches</h3>';
        
        // Supprimer les fichiers de cache de traduction
        $lang_cache_dirs = array(
            DOL_DATA_ROOT . '/admin/temp/langs',
            DOL_DATA_ROOT . '/admin/temp'
        );
        
        foreach ($lang_cache_dirs as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '/*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
                $fixes[] = "Cache de traduction {$dir} vidé";
            }
        }
        
        // Forcer la recompilation des traductions
        $sql = "DELETE FROM " . MAIN_DB_PREFIX . "const WHERE name LIKE '%MAIN_LANG_%_CACHE%'";
        if ($db->query($sql)) {
            $fixes[] = "Cache de traduction en base supprimé";
        }
        
        // 8. Correction finale des données critiques
        print '<h3>8. Correction finale</h3>';
        
        // S'assurer qu'aucune donnée critique n'a de valeur vide
        $critical_fixes = array(
            "UPDATE " . MAIN_DB_PREFIX . "user SET lastname = CONCAT('Utilisateur ', login) WHERE lastname = '' OR lastname IS NULL",
            "UPDATE " . MAIN_DB_PREFIX . "c_paiement SET libelle = code WHERE libelle = '' OR libelle IS NULL",
            "UPDATE " . MAIN_DB_PREFIX . "c_type_contact SET libelle = code WHERE libelle = '' OR libelle IS NULL"
        );
        
        foreach ($critical_fixes as $sql) {
            if ($db->query($sql)) {
                $affected = $db->affected_rows();
                if ($affected > 0) {
                    $fixes[] = "Correction critique: {$affected} enregistrements";
                }
            }
        }
        
    } catch (Exception $e) {
        $errors[] = "Erreur: " . $e->getMessage();
    }
    
    // Affichage des résultats
    if (!empty($fixes)) {
        print '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>✅ Corrections appliquées :</h4>';
        print '<ul>';
        foreach ($fixes as $fix) {
            print '<li>' . $fix . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    if (!empty($errors)) {
        print '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        print '<h4>❌ Erreurs :</h4>';
        print '<ul>';
        foreach ($errors as $error) {
            print '<li>' . $error . '</li>';
        }
        print '</ul>';
        print '</div>';
    }
    
    print '<div style="background: #17a2b8; color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>🔄 Étapes finales obligatoires :</h4>';
    print '<ol style="color: white;">';
    print '<li><strong>Redémarrez MAMP</strong> (arrêt complet puis redémarrage)</li>';
    print '<li><strong>Videz le cache de votre navigateur</strong> (Ctrl+Shift+Delete)</li>';
    print '<li><strong>Fermez tous les onglets Dolibarr</strong></li>';
    print '<li><strong>Rouvrez Dolibarr dans un nouvel onglet</strong></li>';
    print '<li><strong>Reconnectez-vous</strong></li>';
    print '</ol>';
    print '</div>';
    
    print '<p><a href="/" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Retour au tableau de bord</a></p>';
    
} else {
    // Menu principal
    print '<h2>Solution Alternative</h2>';
    print '<p>Cette approche alternative traite le problème "A text to show" sous un angle différent.</p>';
    
    print '<div style="background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>🎯 Approche Alternative</h3>';
    print '<p>Au lieu de se concentrer uniquement sur les traductions, cette solution :</p>';
    print '<ul>';
    print '<li>✅ Corrige les <strong>extrafields</strong> problématiques</li>';
    print '<li>✅ Nettoie les <strong>hooks et modules</strong> défaillants</li>';
    print '<li>✅ Régénère les <strong>références</strong> manquantes</li>';
    print '<li>✅ Utilise une <strong>correction intelligente</strong> des libellés</li>';
    print '<li>✅ Optimise la <strong>configuration d\'affichage</strong></li>';
    print '<li>✅ Nettoie les <strong>menus</strong> corrompus</li>';
    print '<li>✅ Force la <strong>régénération complète</strong> des caches</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    print '<h3>⚡ Pourquoi cette approche ?</h3>';
    print '<p>Le problème "A text to show" peut venir de :</p>';
    print '<ul>';
    print '<li>🔸 <strong>Extrafields corrompus</strong> qui interfèrent avec l\'affichage</li>';
    print '<li>🔸 <strong>Modules tiers</strong> (abricot, quality) qui modifient les traductions</li>';
    print '<li>🔸 <strong>Références manquantes</strong> qui causent des erreurs d\'affichage</li>';
    print '<li>🔸 <strong>Configuration d\'affichage</strong> incorrecte</li>';
    print '<li>🔸 <strong>Menus corrompus</strong> qui génèrent des erreurs</li>';
    print '</ul>';
    print '</div>';
    
    print '<div style="text-align: center; margin: 30px 0;">';
    print '<a href="?action=fix_alternative" style="background: #fd7e14; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold;">🚀 APPLIQUER LA SOLUTION ALTERNATIVE</a>';
    print '</div>';
    
    print '<div style="background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0;">';
    print '<h4>⚠️ Cette solution est complémentaire</h4>';
    print '<p>Vous pouvez l\'utiliser :</p>';
    print '<ul>';
    print '<li>✅ <strong>Après</strong> avoir essayé les autres solutions</li>';
    print '<li>✅ <strong>En complément</strong> des corrections précédentes</li>';
    print '<li>✅ Si le problème <strong>persiste encore</strong></li>';
    print '</ul>';
    print '</div>';
}

print '</body></html>';
?>
